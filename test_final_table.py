#!/usr/bin/env python3
"""
Final test of the enhanced table streaming implementation
"""
import requests
import json
import time

def test_final_implementation():
    """Test the final enhanced table implementation"""
    
    # Use the same test data that we know works
    test_data = """S/S 430  BA  NO PI
 
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#"""

    url = "http://localhost:8000/api/llm/stream"
    payload = {
        "text": test_data,
        "function": "table",
        "unit_system": "metric"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    print("🧪 Testing enhanced table implementation...")
    print(f"📍 URL: {url}")
    print(f"📋 Payload: {json.dumps(payload, indent=2)}")
    print("=" * 60)
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True)
        
        if response.status_code == 200:
            print("✅ Stream started successfully!")
            accumulated_data = ""
            chunk_count = 0
            header_complete = False
            
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    
                    if decoded_line.startswith('data: '):
                        try:
                            data_str = decoded_line[6:]
                            if data_str == '[DONE]':
                                print("\\n🏁 Stream completed successfully!")
                                break
                            
                            data = json.loads(data_str)
                            if 'content' in data:
                                chunk_content = data['content']
                                accumulated_data += chunk_content
                                chunk_count += 1
                                
                                # Check for key milestones
                                if '<table_stream>' in accumulated_data and not header_complete:
                                    print(f"\\n📊 Found table_stream start at chunk {chunk_count}")
                                
                                # Check for complete header line
                                if 'Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks' in accumulated_data and not header_complete:
                                    print(f"✅ Complete header line detected at chunk {chunk_count}!")
                                    print("🎯 Header: Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks")
                                    header_complete = True
                                
                                # Show progress every 50 chunks
                                if chunk_count % 50 == 0:
                                    print(f"📈 Progress: {chunk_count} chunks processed, {len(accumulated_data)} chars")
                                
                        except json.JSONDecodeError:
                            pass
            
            print("\\n" + "=" * 60)
            print("📊 FINAL RESULT ANALYSIS:")
            print("=" * 60)
            
            # Parse the final result
            import re
            match = re.search(r'<table_stream>\s*([\s\S]*?)\s*</table_stream>', accumulated_data)
            if match:
                table_content = match.group(1).strip()
                lines = table_content.split('\n')
                
                if lines:
                    header_line = lines[0]
                    headers = [h.strip() for h in header_line.split('|') if h.strip()]
                    
                    print(f"✅ Table parsed successfully!")
                    print(f"📋 Header count: {len(headers)}")
                    print(f"📋 Headers: {headers}")
                    
                    data_rows = [line for line in lines[1:] if line.strip()]
                    print(f"📊 Data rows: {len(data_rows)}")
                    
                    if len(headers) == 7:
                        print("🎉 SUCCESS: Correct 7-column table detected!")
                        print("✅ All expected columns present:")
                        for i, header in enumerate(headers, 1):
                            print(f"   {i}. {header}")
                    else:
                        print(f"⚠️  WARNING: Expected 7 columns, got {len(headers)}")
                        
                    if data_rows:
                        print(f"\\n📝 Sample data row:")
                        sample_row = data_rows[0]
                        cells = [c.strip() for c in sample_row.split('|')]
                        print(f"   Raw: {sample_row}")
                        print(f"   Cells ({len(cells)}): {cells}")
                        
                    return True
                else:
                    print("❌ No table lines found")
                    return False
            else:
                print("❌ No table_stream content found")
                print(f"📄 Raw accumulated data: {accumulated_data}")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"💥 Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_final_implementation()
    if success:
        print("\\n🎉 SUCCESS: Table streaming working correctly!")
        print("🚀 The enhanced implementation should now properly display 7 columns!")
    else:
        print("\\n💥 FAILURE: Issues detected with table streaming")
    
    print("\\n💡 Next steps:")
    print("1. Open http://localhost:5173 in your browser")
    print("2. Select 'Table' function")
    print("3. Paste the test data and submit")
    print("4. Check browser console for detailed debug logs")
    print("5. Verify that the table shows 7 columns")