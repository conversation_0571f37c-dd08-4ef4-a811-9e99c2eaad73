#!/usr/bin/env python3
"""
MCP Test Script for Table Parsing with Real Test Cases
"""

import requests
import json
import time
import re

def test_llm_endpoint():
    """Test the LLM endpoint with table parsing"""
    
    # Test case from testCase.txt
    test_input = """
Please convert the following steel specifications from imperial to metric and create a table:

S/S 430 BA NO PI
.015(+/-.0015) X 2.343"(+/-.005) X COIL - 7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL - 8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL - 12,550#
.015(+/-.0015) X 19.68"(+/-.03125) X COIL - 8,835#
.015(+/-.0015) X 47.438"(+/-.03125) X COIL - 57,655#

S/S 430 #2B NO PI
.015(+/-.0015) X 16.938"(+/-.005) X COIL - 5,000#
.016(+/-.0015) X 19.6875"(+/-.005) X COIL - 725,321#
.016(+/-.0015) X 35.500"(+/-.03125) X COIL - 122,083#
.016(+/-.0015) X 36.000"(+/-.03125) X COIL - 234,265#
.018(+/-.0015) X 36.000"(+/-.03125) X COIL - 33,841#
"""
    
    url = "http://localhost:8000/api/llm"
    
    payload = {
        "text": test_input,
        "unit_system": "metric",
        "function": "table"
    }
    
    print("🧪 Testing LLM Endpoint with Table Function")
    print("=" * 60)
    print(f"Input: {test_input[:100]}...")
    print(f"URL: {url}")
    print()
    
    try:
        print("📡 Sending request to LLM endpoint...")
        response = requests.post(url, json=payload, timeout=60)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print()
            
            # Print the full response
            print("📋 Full Response:")
            print("-" * 40)
            print(json.dumps(result, indent=2, ensure_ascii=False))
            print("-" * 40)
            print()
            
            # Analyze the response
            if 'message' in result:
                message = result['message']
                print("📄 Response Message Analysis:")
                print(f"Message length: {len(message)}")
                print()
                
                # Check for table_stream format
                if '<table_stream>' in message:
                    print("✅ Found table_stream format!")
                    
                    # Extract and analyze table content
                    match = re.search(r'<table_stream>(.*?)</table_stream>', message, re.DOTALL)
                    if match:
                        table_content = match.group(1).strip()
                        print("📊 Extracted Table Content:")
                        print("-" * 30)
                        print(table_content)
                        print("-" * 30)
                        print()
                        
                        # Parse and validate the table
                        lines = [line.strip() for line in table_content.split('\n') if line.strip()]
                        
                        if lines:
                            print("🔍 Table Structure Analysis:")
                            
                            # Analyze header
                            header_line = lines[0]
                            headers = [h.strip() for h in header_line.split('|')]
                            print(f"Headers ({len(headers)}): {headers}")
                            
                            # Expected headers
                            expected_headers = ['Item Code', 'Description', 'Size (Original)', 'Customer QTY', 'Size (Converted)', 'Converted QTY', 'Remarks']
                            
                            if len(headers) == len(expected_headers):
                                print("✅ Header count matches expected (7 columns)")
                            else:
                                print(f"❌ Header count mismatch! Expected {len(expected_headers)}, got {len(headers)}")
                            
                            # Analyze data rows
                            data_rows = lines[1:]
                            print(f"Data rows: {len(data_rows)}")
                            
                            all_rows_valid = True
                            for i, row in enumerate(data_rows):
                                cells = [c.strip() for c in row.split('|')]
                                print(f"  Row {i+1}: {len(cells)} cells")
                                
                                if len(cells) != len(headers):
                                    print(f"    ❌ Column mismatch! Expected {len(headers)}, got {len(cells)}")
                                    print(f"    Raw row: {row}")
                                    print(f"    Parsed cells: {cells}")
                                    all_rows_valid = False
                                else:
                                    print(f"    ✅ Column count matches")
                                    
                                    # Show first few cells for verification
                                    if i < 3:  # Show first 3 rows
                                        for j, (header, cell) in enumerate(zip(headers, cells)):
                                            print(f"      {header}: {cell}")
                            
                            if all_rows_valid:
                                print("🎉 All rows have correct column alignment!")
                            else:
                                print("❌ Some rows have column misalignment issues")
                                
                        else:
                            print("❌ No table lines found")
                    else:
                        print("❌ Could not extract table content from table_stream tags")
                        
                else:
                    print("❌ No table_stream format found")
                    
                    # Check for other table formats
                    if '|' in message and ('Item Code' in message or 'Description' in message):
                        print("📋 Found pipe-delimited content, but not in table_stream format")
                        print("Raw content:")
                        print(message)
                    else:
                        print("❌ No recognizable table format found")
                        print("Raw message:")
                        print(message[:500] + "..." if len(message) > 500 else message)
            else:
                print("❌ No 'message' field in response")
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Backend server not running")
        print("💡 Please start the backend server:")
        print("   cd backend && python3 main.py")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

def test_health_check():
    """Test if backend is running"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend is not running: {e}")
        return False

def test_simple_conversion():
    """Test simple conversion without table"""
    
    test_input = ".015\" x 2.343\" x COIL - 7190#"
    
    url = "http://localhost:8000/api/llm"
    payload = {
        "text": test_input,
        "unit_system": "metric",
        "function": "conversion"
    }
    
    print("\n🔧 Testing Simple Conversion (Non-Table)")
    print("=" * 50)
    print(f"Input: {test_input}")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Simple conversion successful")
            print(f"Response: {result.get('message', 'No message')}")
            return True
        else:
            print(f"❌ Simple conversion failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Simple conversion error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting MCP Table Parsing Test")
    print("=" * 70)
    
    # Check if backend is running
    if not test_health_check():
        print("\n💡 Please start the backend server first:")
        print("   cd backend && python3 main.py")
        exit(1)
    
    print()
    
    # Test simple conversion first
    simple_success = test_simple_conversion()
    
    print()
    
    # Test table parsing
    table_success = test_llm_endpoint()
    
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY")
    print("=" * 70)
    
    if simple_success and table_success:
        print("🎉 ALL TESTS COMPLETED")
        print("✅ Backend is responding")
        print("✅ LLM endpoint is working")
        print("📊 Check the table analysis above for column alignment issues")
    else:
        print("❌ SOME TESTS FAILED")
        if not simple_success:
            print("❌ Simple conversion failed")
        if not table_success:
            print("❌ Table parsing test failed")
    
    print("\n💡 Next Steps:")
    print("1. Review the table structure analysis above")
    print("2. If column misalignment is detected, update the LLM prompt")
    print("3. Iterate until all columns align correctly")
    print("4. Test with frontend application")
