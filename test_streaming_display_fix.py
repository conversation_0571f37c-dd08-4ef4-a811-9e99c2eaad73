#!/usr/bin/env python3
"""
Test script to verify that streaming table display works correctly
"""

import requests
import json
import time
import os

def test_streaming_display():
    """Test that tables display progressively during streaming"""
    
    # Test data
    test_input = """
    Please convert the following steel specifications from imperial to metric and create a table:

    001 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.343(+/-0.005)" x COIL - 7190#
    002 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.406(+/-0.005)" x COIL - 8061#
    003 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 16.50(+/-0.005)" x COIL - 12550#
    """
    
    print("🧪 Testing Streaming Display Fix")
    print("=" * 50)
    print()
    
    # API endpoint
    url = "http://localhost:8000/api/llm/stream"
    
    # Request payload
    payload = {
        "text": test_input,
        "unit_system": "metric",
        "function": "table"
    }
    
    try:
        # Clear proxy environment variables
        old_http_proxy = os.environ.get('http_proxy')
        old_https_proxy = os.environ.get('https_proxy')
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']
        
        session = requests.Session()
        session.trust_env = False
        
        print("📡 Starting streaming request...")
        response = session.post(url, json=payload, stream=True, timeout=60)
        
        # Restore proxy settings
        if old_http_proxy:
            os.environ['http_proxy'] = old_http_proxy
        if old_https_proxy:
            os.environ['https_proxy'] = old_https_proxy
        
        if response.status_code == 200:
            print("✅ Streaming request successful")
            print()
            
            chunk_count = 0
            headers_found = False
            first_row_found = False
            table_complete = False
            
            # Track streaming display milestones
            milestones = {
                'headers_available': False,
                'first_row_available': False,
                'multiple_rows_available': False,
                'table_complete': False
            }
            
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith('data: '):
                    data_part = line[6:]
                    
                    if data_part.strip() == '[DONE]':
                        print("🏁 Stream completed")
                        break
                    
                    try:
                        chunk_data = json.loads(data_part)
                        chunk_count += 1
                        
                        if 'accumulated_content' in chunk_data:
                            accumulated_content = chunk_data['accumulated_content']
                            
                            # Simulate frontend parsing logic
                            if '<table_stream>' in accumulated_content:
                                # Extract table content
                                start = accumulated_content.find('<table_stream>') + len('<table_stream>')
                                if '</table_stream>' in accumulated_content:
                                    end = accumulated_content.find('</table_stream>')
                                    table_content = accumulated_content[start:end].strip()
                                    is_complete = True
                                else:
                                    table_content = accumulated_content[start:].strip()
                                    is_complete = False
                                
                                if table_content:
                                    lines = table_content.split('\n')
                                    lines = [line.strip() for line in lines if line.strip()]
                                    
                                    # Check for headers
                                    if len(lines) > 0 and not headers_found:
                                        header_line = lines[0]
                                        if '|' in header_line:
                                            headers = header_line.split('|')
                                            if len(headers) == 7:
                                                headers_found = True
                                                milestones['headers_available'] = True
                                                print(f"📋 MILESTONE: Headers found at chunk {chunk_count}")
                                                print(f"   Headers: {headers}")
                                    
                                    # Check for first data row
                                    if len(lines) > 1 and headers_found and not first_row_found:
                                        first_data_line = lines[1]
                                        if '|' in first_data_line:
                                            cells = first_data_line.split('|')
                                            if len(cells) == 7:
                                                first_row_found = True
                                                milestones['first_row_available'] = True
                                                print(f"📊 MILESTONE: First row found at chunk {chunk_count}")
                                                print(f"   Row data: {cells}")
                                    
                                    # Check for multiple rows
                                    if len(lines) > 2 and not milestones['multiple_rows_available']:
                                        milestones['multiple_rows_available'] = True
                                        print(f"📈 MILESTONE: Multiple rows ({len(lines)-1}) available at chunk {chunk_count}")
                                    
                                    # Check for completion
                                    if is_complete and not table_complete:
                                        table_complete = True
                                        milestones['table_complete'] = True
                                        print(f"✅ MILESTONE: Table complete at chunk {chunk_count}")
                                        print(f"   Total rows: {len(lines)-1}")
                                        
                                        # Verify final data integrity
                                        data_integrity_ok = True
                                        for i, line in enumerate(lines[1:], 1):
                                            cells = line.split('|')
                                            if len(cells) != 7:
                                                print(f"❌ Row {i} column mismatch: expected 7, got {len(cells)}")
                                                data_integrity_ok = False
                                        
                                        if data_integrity_ok:
                                            print("🎉 Final data integrity check: PASSED")
                                        else:
                                            print("❌ Final data integrity check: FAILED")
                                        
                                        break
                                        
                    except json.JSONDecodeError:
                        pass  # Skip invalid JSON chunks
            
            print()
            print("📊 Streaming Display Analysis:")
            print("-" * 30)
            print(f"Total chunks processed: {chunk_count}")
            
            for milestone, achieved in milestones.items():
                status = "✅" if achieved else "❌"
                print(f"{status} {milestone.replace('_', ' ').title()}: {achieved}")
            
            print()
            
            # Determine if streaming display would work
            streaming_display_works = (
                milestones['headers_available'] and 
                milestones['first_row_available'] and
                milestones['table_complete']
            )
            
            if streaming_display_works:
                print("🎉 STREAMING DISPLAY: WORKING CORRECTLY")
                print("   ✅ Headers appear early in stream")
                print("   ✅ Rows appear progressively")
                print("   ✅ Table completes successfully")
                print("   ✅ Data integrity maintained")
            else:
                print("❌ STREAMING DISPLAY: ISSUES DETECTED")
                if not milestones['headers_available']:
                    print("   ❌ Headers not detected in stream")
                if not milestones['first_row_available']:
                    print("   ❌ First row not detected in stream")
                if not milestones['table_complete']:
                    print("   ❌ Table completion not detected")
            
        else:
            print(f"❌ Streaming request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_streaming_display()
