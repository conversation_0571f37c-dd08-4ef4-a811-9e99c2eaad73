#!/usr/bin/env python3
"""
SMTP Debug Tool for Aliyun Enterprise Email
Tests SMTP connection and email sending functionality
"""

import smtplib
import ssl
import os
import sys
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from dotenv import load_dotenv

def print_colored(message, color="white"):
    """Print colored messages"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "purple": "\033[95m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "reset": "\033[0m"
    }
    print(f"{colors.get(color, colors['white'])}{message}{colors['reset']}")

def load_smtp_config():
    """Load SMTP configuration from .env file"""
    load_dotenv()
    
    config = {
        'server': os.getenv('SMTP_SERVER', 'smtp.qiye.aliyun.com'),
        'port': int(os.getenv('SMTP_PORT', 465)),
        'username': os.getenv('SMTP_USERNAME', ''),
        'password': os.getenv('SMTP_PASSWORD', ''),
        'sender_name': os.getenv('SMTP_SENDER_NAME', 'SteelNet'),
        'domain': os.getenv('SMTP_DOMAIN', 'steelnet.ai')
    }
    
    return config

def test_smtp_connection(config):
    """Test SMTP connection to Aliyun Enterprise Email"""
    print_colored("=== Testing SMTP Connection ===", "blue")
    print(f"Server: {config['server']}")
    print(f"Port: {config['port']}")
    print(f"Username: {config['username']}")
    print(f"SSL: {'Yes' if config['port'] == 465 else 'No'}")
    print()
    
    try:
        # Create SMTP connection
        if config['port'] == 465:
            # SSL connection
            print_colored("Creating SSL connection...", "cyan")
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(config['server'], config['port'], context=context)
        else:
            # Plain or STARTTLS connection
            print_colored("Creating plain connection...", "cyan")
            server = smtplib.SMTP(config['server'], config['port'])
            if config['port'] == 587:
                server.starttls()
        
        print_colored("✓ Connection established", "green")
        
        # Enable debug output
        server.set_debuglevel(1)
        
        # Login
        print_colored("Attempting login...", "cyan")
        server.login(config['username'], config['password'])
        print_colored("✓ Login successful", "green")
        
        # Test EHLO
        print_colored("Testing EHLO...", "cyan")
        server.ehlo()
        print_colored("✓ EHLO successful", "green")
        
        server.quit()
        print_colored("✓ SMTP connection test passed!", "green")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print_colored(f"✗ Authentication failed: {e}", "red")
        print_colored("Check your username and password", "yellow")
        return False
    except smtplib.SMTPConnectError as e:
        print_colored(f"✗ Connection failed: {e}", "red")
        print_colored("Check server and port settings", "yellow")
        return False
    except smtplib.SMTPServerDisconnected as e:
        print_colored(f"✗ Server disconnected: {e}", "red")
        return False
    except Exception as e:
        print_colored(f"✗ Unexpected error: {e}", "red")
        return False

def send_test_email(config, recipient_email=None):
    """Send a test email using Aliyun Enterprise Email"""
    if not recipient_email:
        recipient_email = config['username']  # Send to self
    
    print_colored("=== Sending Test Email ===", "blue")
    print(f"From: {config['username']}")
    print(f"To: {recipient_email}")
    print()
    
    try:
        # Create message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = f"Test Email from {config['sender_name']} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        msg['From'] = f"{config['sender_name']} <{config['username']}>"
        msg['To'] = recipient_email
        
        # Create HTML content
        html_content = f"""
        <html>
        <body>
            <h2>🎉 Aliyun Enterprise Email Test</h2>
            <p>This is a test email sent from <strong>{config['sender_name']}</strong></p>
            <p><strong>Configuration Details:</strong></p>
            <ul>
                <li>SMTP Server: {config['server']}</li>
                <li>Port: {config['port']}</li>
                <li>SSL: {'Yes' if config['port'] == 465 else 'No'}</li>
                <li>Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</li>
            </ul>
            <p>If you received this email, your Aliyun Enterprise Email configuration is working correctly! ✅</p>
            <hr>
            <p><small>Sent from SteelNet Unit Converter Application</small></p>
        </body>
        </html>
        """
        
        # Create plain text version
        text_content = f"""
        Aliyun Enterprise Email Test
        
        This is a test email sent from {config['sender_name']}
        
        Configuration Details:
        - SMTP Server: {config['server']}
        - Port: {config['port']}
        - SSL: {'Yes' if config['port'] == 465 else 'No'}
        - Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        
        If you received this email, your Aliyun Enterprise Email configuration is working correctly!
        
        Sent from SteelNet Unit Converter Application
        """
        
        # Attach parts
        part1 = MIMEText(text_content, 'plain')
        part2 = MIMEText(html_content, 'html')
        msg.attach(part1)
        msg.attach(part2)
        
        # Send email
        if config['port'] == 465:
            context = ssl.create_default_context()
            server = smtplib.SMTP_SSL(config['server'], config['port'], context=context)
        else:
            server = smtplib.SMTP(config['server'], config['port'])
            if config['port'] == 587:
                server.starttls()
        
        server.login(config['username'], config['password'])
        server.send_message(msg)
        server.quit()
        
        print_colored("✓ Test email sent successfully!", "green")
        print_colored(f"Check {recipient_email} for the test email", "cyan")
        return True
        
    except Exception as e:
        print_colored(f"✗ Failed to send test email: {e}", "red")
        return False

def main():
    """Main function"""
    print_colored("=== Aliyun Enterprise Email SMTP Debug Tool ===", "purple")
    print_colored(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", "cyan")
    print()
    
    # Load configuration
    config = load_smtp_config()
    
    if not config['username'] or not config['password']:
        print_colored("❌ SMTP credentials not found in .env file", "red")
        print_colored("Please run setup_aliyun_email.sh first", "yellow")
        sys.exit(1)
    
    print_colored("Configuration loaded:", "green")
    print(f"  Server: {config['server']}")
    print(f"  Port: {config['port']}")
    print(f"  Username: {config['username']}")
    print(f"  Sender Name: {config['sender_name']}")
    print()
    
    # Test connection
    connection_success = test_smtp_connection(config)
    print()
    
    if connection_success:
        # Ask if user wants to send test email
        while True:
            choice = input("Do you want to send a test email? (y/n): ").lower().strip()
            if choice in ['y', 'yes']:
                recipient = input(f"Enter recipient email (press Enter for {config['username']}): ").strip()
                if not recipient:
                    recipient = config['username']
                
                print()
                send_test_email(config, recipient)
                break
            elif choice in ['n', 'no']:
                print_colored("Skipping test email", "yellow")
                break
            else:
                print_colored("Please enter 'y' or 'n'", "yellow")
    
    print()
    print_colored("=== Debug Session Complete ===", "purple")
    
    if connection_success:
        print_colored("✅ Your Aliyun Enterprise Email is configured correctly!", "green")
        print_colored("You can now use email verification in your application", "cyan")
    else:
        print_colored("❌ Please fix the configuration issues above", "red")
        print_colored("Run setup_aliyun_email.sh to reconfigure", "yellow")

if __name__ == "__main__":
    main() 