#!/usr/bin/env python3
"""
Direct LLM Test Script - Test table parsing without server
"""

import sys
import os
import re

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_llm_service_directly():
    """Test the LLM service directly without going through the API"""
    
    try:
        # Import the LLM service
        from llm.service import LLMService
        
        print("🧪 Testing LLM Service Directly")
        print("=" * 60)
        
        # Initialize the service
        llm_service = LLMService()
        print("✅ LLM Service initialized")
        
        # Test case from testCase.txt
        test_input = """
Please convert the following steel specifications from imperial to metric and create a table:

S/S 430 BA NO PI
.015(+/-.0015) X 2.343"(+/-.005) X COIL - 7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL - 8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL - 12,550#
.015(+/-.0015) X 19.68"(+/-.03125) X COIL - 8,835#
.015(+/-.0015) X 47.438"(+/-.03125) X COIL - 57,655#

S/S 430 #2B NO PI
.015(+/-.0015) X 16.938"(+/-.005) X COIL - 5,000#
.016(+/-.0015) X 19.6875"(+/-.005) X COIL - 725,321#
.016(+/-.0015) X 35.500"(+/-.03125) X COIL - 122,083#
.016(+/-.0015) X 36.000"(+/-.03125) X COIL - 234,265#
.018(+/-.0015) X 36.000"(+/-.03125) X COIL - 33,841#
"""
        
        print("📋 Test Input:")
        print(test_input[:200] + "...")
        print()
        
        # Call the LLM service
        print("📡 Calling LLM service...")
        import asyncio
        response = asyncio.run(llm_service.convert_units(
            text=test_input,
            unit_system="metric",
            function="conversion",
            table_mode=True
        ))
        
        print("✅ LLM service responded")
        print()
        
        # Analyze the response
        print("📄 LLM Response Analysis:")
        print("-" * 40)
        print(f"Response type: {type(response)}")
        print(f"Response keys: {list(response.keys()) if isinstance(response, dict) else 'Not a dict'}")
        print()

        # Extract the message from response
        if isinstance(response, dict) and 'message' in response:
            response_text = response['message']
        else:
            response_text = str(response)
        
        print("📋 Full Response:")
        print("-" * 30)
        print(response_text)
        print("-" * 30)
        print()
        
        # Check for table_stream format
        if '<table_stream>' in response_text:
            print("✅ Found table_stream format!")
            
            # Extract and analyze table content
            match = re.search(r'<table_stream>(.*?)</table_stream>', response_text, re.DOTALL)
            if match:
                table_content = match.group(1).strip()
                print("📊 Extracted Table Content:")
                print("-" * 30)
                print(table_content)
                print("-" * 30)
                print()
                
                # Parse and validate the table
                lines = [line.strip() for line in table_content.split('\n') if line.strip()]
                
                if lines:
                    print("🔍 Table Structure Analysis:")
                    
                    # Analyze header
                    header_line = lines[0]
                    headers = [h.strip() for h in header_line.split('|')]
                    print(f"Headers ({len(headers)}): {headers}")
                    
                    # Expected headers
                    expected_headers = ['Item Code', 'Description', 'Size (Original)', 'Customer QTY', 'Size (Converted)', 'Converted QTY', 'Remarks']
                    
                    if len(headers) == len(expected_headers):
                        print("✅ Header count matches expected (7 columns)")
                    else:
                        print(f"❌ Header count mismatch! Expected {len(expected_headers)}, got {len(headers)}")
                    
                    # Analyze data rows
                    data_rows = lines[1:]
                    print(f"Data rows: {len(data_rows)}")
                    
                    all_rows_valid = True
                    for i, row in enumerate(data_rows):
                        cells = [c.strip() for c in row.split('|')]
                        print(f"  Row {i+1}: {len(cells)} cells")
                        
                        if len(cells) != len(headers):
                            print(f"    ❌ Column mismatch! Expected {len(headers)}, got {len(cells)}")
                            print(f"    Raw row: {row}")
                            print(f"    Parsed cells: {cells}")
                            all_rows_valid = False
                        else:
                            print(f"    ✅ Column count matches")
                            
                            # Show first few cells for verification
                            if i < 3:  # Show first 3 rows
                                for j, (header, cell) in enumerate(zip(headers, cells)):
                                    print(f"      {header}: {cell}")
                    
                    if all_rows_valid:
                        print("🎉 All rows have correct column alignment!")
                        return True
                    else:
                        print("❌ Some rows have column misalignment issues")
                        return False
                        
                else:
                    print("❌ No table lines found")
                    return False
            else:
                print("❌ Could not extract table content from table_stream tags")
                return False
                
        else:
            print("❌ No table_stream format found")
            
            # Check for other table formats
            if '|' in response_text and ('Item Code' in response_text or 'Description' in response_text):
                print("📋 Found pipe-delimited content, but not in table_stream format")
                print("Raw content:")
                print(response_text)
            else:
                print("❌ No recognizable table format found")
                print("Raw response:")
                print(response_text[:500] + "..." if len(response_text) > 500 else response_text)
            
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're in the correct directory and dependencies are installed")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_prompt_file():
    """Analyze the current prompt file"""
    
    print("\n🔍 Analyzing Current Prompt File")
    print("=" * 50)
    
    try:
        with open('backend/llm_prompt.txt', 'r', encoding='utf-8') as f:
            prompt_content = f.read()
        
        print("✅ Successfully read prompt file")
        
        # Check for table_stream format
        if '<table_stream>' in prompt_content:
            print("✅ Found <table_stream> tags in prompt")
            
            # Extract the example
            start = prompt_content.find('<table_stream>')
            end = prompt_content.find('</table_stream>') + len('</table_stream>')
            
            if start != -1 and end != -1:
                table_example = prompt_content[start:end]
                print("\n📋 Table Example from Prompt:")
                print(table_example)
                
                # Check if it uses the correct format
                if '|' in table_example and 'Item Code' in table_example:
                    print("✅ Prompt uses correct pipe-delimited format")
                else:
                    print("❌ Prompt format may be incorrect")
            else:
                print("❌ Could not extract table example")
        else:
            print("❌ No <table_stream> tags found in prompt")
            
        return True
        
    except FileNotFoundError:
        print("❌ Prompt file not found")
        return False
    except Exception as e:
        print(f"❌ Error reading prompt file: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Direct LLM Test")
    print("=" * 70)
    
    # Analyze prompt file first
    prompt_ok = analyze_prompt_file()
    
    if prompt_ok:
        # Test LLM service
        llm_ok = test_llm_service_directly()
        
        print("\n" + "=" * 70)
        print("📋 TEST SUMMARY")
        print("=" * 70)
        
        if llm_ok:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Prompt file is correct")
            print("✅ LLM service is working")
            print("✅ Table parsing is functioning correctly")
        else:
            print("❌ TESTS FAILED")
            print("❌ Table parsing has issues")
            print("💡 Need to adjust the LLM prompt for better table formatting")
    else:
        print("❌ Prompt file analysis failed")
    
    print("\n💡 Next Steps:")
    print("1. If tests failed, adjust the LLM prompt")
    print("2. Test with the frontend application")
    print("3. Iterate until column alignment is perfect")
