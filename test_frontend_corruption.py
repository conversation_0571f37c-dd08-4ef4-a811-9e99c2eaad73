#!/usr/bin/env python3
"""
Test script to identify where frontend corruption occurs during streaming
"""
import requests
import json
import time

def test_frontend_corruption():
    """Test streaming and compare with direct API results"""
    
    test_data = """S/S 430  BA  NO PI
 
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#
.015(+/-.0015) X 19.68"(+/-.03125) X COIL                                         8,835#
.015(+/-.0015) X 47.438"(+/-.03125) X COIL                                      57,655#
.015(+/-.0015) X 47.000"(+/-.03125) X COIL                                      118,001#
.015(+/-.0015) X 35.438"(+/-.03125) X COIL                                      62,515#
 
S/S 430 #2B NO PI
 
.015(+/-.0015) X 16.938"(+/-.005) X COIL                                           5,000#
.016(+/-.0015) X 19.6875"(+/-.005) X COIL                                         725,321#
.016(+/-.0015) X 35.500"(+/-.03125) X COIL                                      122,083#
.016(+/-.0015) X 36.000"(+/-.03125) X COIL                                      234,265#
.016(+/-.0015) X 48.000"(+/-.03125) X COIL                                      201,347#
.018(+/-.0015) X 36.000"(+/-.03125) X COIL                                      33,841#"""

    url = "http://localhost:8000/api/llm/stream"
    payload = {
        "text": test_data,
        "function": "conversion,table",
        "unit_system": "metric"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    print("🔍 TESTING FRONTEND CORRUPTION ANALYSIS")
    print("=" * 80)
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True)
        
        if response.status_code == 200:
            print("✅ Testing streaming response corruption patterns...")
            
            accumulated_data = ""
            chunk_buffer = []
            
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    
                    if decoded_line.startswith('data: '):
                        try:
                            data_str = decoded_line[6:]
                            if data_str == '[DONE]':
                                break
                            
                            data = json.loads(data_str)
                            if 'content' in data:
                                chunk_content = data['content']
                                accumulated_data += chunk_content
                                chunk_buffer.append(chunk_content)
                                
                        except json.JSONDecodeError:
                            pass
            
            # Extract table content
            import re
            table_match = re.search(r'<table_stream>\s*([\s\S]*?)\s*</table_stream>', accumulated_data)
            if table_match:
                table_content = table_match.group(1).strip()
                
                print("📊 FINAL TABLE CONTENT:")
                print("=" * 50)
                print(table_content)
                print("=" * 50)
                
                # Analyze line structure
                lines = table_content.split('\n')
                print(f"\n🔍 LINE STRUCTURE ANALYSIS ({len(lines)} lines):")
                print("=" * 50)
                
                for i, line in enumerate(lines):
                    if line.strip():
                        pipe_count = line.count('|')
                        cells = line.split('|')
                        cell_lengths = [len(cell.strip()) for cell in cells]
                        
                        print(f"LINE {i:2d}: {pipe_count} pipes | {len(cells)} cells | Lengths: {cell_lengths}")
                        
                        if i == 0:  # Header
                            print(f"    Headers: {[cell.strip() for cell in cells]}")
                        else:  # Data rows
                            if len(cells) != 7:
                                print(f"    🚨 CORRUPTED ROW: Expected 7 cells, got {len(cells)}")
                                print(f"    Content: {[cell.strip()[:30] + '...' if len(cell.strip()) > 30 else cell.strip() for cell in cells]}")
                            else:
                                print(f"    ✅ Valid row: {[cell.strip()[:20] + '...' if len(cell.strip()) > 20 else cell.strip() for cell in cells]}")
                
                # Test chunk reconstruction patterns
                print(f"\n🔧 CHUNK RECONSTRUCTION TEST:")
                print("=" * 50)
                
                reconstructed = ''.join(chunk_buffer)
                table_match_reconstructed = re.search(r'<table_stream>\s*([\s\S]*?)\s*</table_stream>', reconstructed)
                
                if table_match_reconstructed:
                    reconstructed_table = table_match_reconstructed.group(1).strip()
                    if reconstructed_table == table_content:
                        print("✅ Chunk reconstruction matches accumulated data")
                    else:
                        print("🚨 CHUNK RECONSTRUCTION MISMATCH!")
                        print(f"Original length: {len(table_content)}")
                        print(f"Reconstructed length: {len(reconstructed_table)}")
                        
                        # Find differences
                        import difflib
                        diff = list(difflib.unified_diff(
                            table_content.splitlines(keepends=True),
                            reconstructed_table.splitlines(keepends=True),
                            fromfile='accumulated',
                            tofile='reconstructed',
                            n=3
                        ))
                        print("Differences:")
                        for line in diff:
                            print(line.rstrip())
                
            else:
                print("❌ No table_stream content found")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"💥 Request failed: {e}")

if __name__ == "__main__":
    test_frontend_corruption()