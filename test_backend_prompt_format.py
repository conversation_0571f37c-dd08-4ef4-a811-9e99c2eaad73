#!/usr/bin/env python3
"""
Test script to verify the backend prompt generates the correct table format
"""

def test_prompt_format():
    """Test that the backend prompt format matches frontend parsing expectations"""
    
    print("🧪 Testing Backend Prompt Format")
    print("=" * 50)
    
    # Read the updated prompt file
    try:
        with open('backend/llm_prompt.txt', 'r', encoding='utf-8') as f:
            prompt_content = f.read()
        
        print("✅ Successfully read backend prompt file")
        
        # Check for the new table_stream format
        if '<table_stream>' in prompt_content:
            print("✅ Found <table_stream> tags in prompt")
            
            # Extract the example table format
            start = prompt_content.find('<table_stream>')
            end = prompt_content.find('</table_stream>') + len('</table_stream>')
            
            if start != -1 and end != -1:
                table_example = prompt_content[start:end]
                print("\n📋 Table Example from Prompt:")
                print("-" * 30)
                print(table_example)
                print("-" * 30)
                
                # Parse the example to verify format
                lines = table_example.replace('<table_stream>', '').replace('</table_stream>', '').strip().split('\n')
                lines = [line.strip() for line in lines if line.strip()]
                
                if lines:
                    header_line = lines[0]
                    print(f"\n🔍 Analysis:")
                    print(f"Header line: {header_line}")
                    
                    # Check if it uses pipe delimiters
                    if '|' in header_line:
                        headers = header_line.split('|')
                        print(f"✅ Uses pipe delimiters")
                        print(f"✅ Header count: {len(headers)}")
                        print(f"✅ Headers: {headers}")
                        
                        # Check data rows
                        if len(lines) > 1:
                            data_rows = lines[1:]
                            print(f"✅ Data rows: {len(data_rows)}")
                            
                            for i, row in enumerate(data_rows):
                                cells = row.split('|')
                                print(f"   Row {i+1}: {len(cells)} cells")
                                if len(cells) != len(headers):
                                    print(f"   ⚠️  Column mismatch! Expected {len(headers)}, got {len(cells)}")
                                else:
                                    print(f"   ✅ Column count matches")
                        
                        # Check if format matches frontend expectations
                        expected_headers = ['Item Code', 'Description', 'Size (Original)', 'Customer QTY', 'Size (Converted)', 'Converted QTY', 'Remarks']
                        
                        if len(headers) == len(expected_headers):
                            print("✅ Header count matches expected (7 columns)")
                        else:
                            print(f"⚠️  Header count mismatch. Expected {len(expected_headers)}, got {len(headers)}")
                            
                    else:
                        print("❌ Does not use pipe delimiters")
                else:
                    print("❌ No table content found")
            else:
                print("❌ Could not extract table example")
        else:
            print("❌ No <table_stream> tags found in prompt")
        
        # Check for removal of old HEADERS:/ROW: format
        if 'HEADERS:' in prompt_content or 'ROW:' in prompt_content:
            print("⚠️  Old HEADERS:/ROW: format still present in prompt")
        else:
            print("✅ Old HEADERS:/ROW: format removed from prompt")
            
        # Check for the new format description
        if '管道符(|)分隔字段' in prompt_content or 'pipe delimiter' in prompt_content.lower():
            print("✅ New format description found in prompt")
        else:
            print("⚠️  New format description not found in prompt")
            
    except FileNotFoundError:
        print("❌ Backend prompt file not found")
        return False
    except Exception as e:
        print(f"❌ Error reading prompt file: {e}")
        return False
    
    return True

def test_frontend_parsing_compatibility():
    """Test that the new format is compatible with frontend parsing"""
    
    print("\n🔧 Testing Frontend Parsing Compatibility")
    print("=" * 50)
    
    # Simulate the new backend format
    test_table_stream = """<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015" x 2.343" x COIL|7190#|0.38mm x 59.51mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015" x 2.406" x COIL|8061#|0.38mm x 61.11mm x COIL|3656.7 kg|BA Finish
</table_stream>"""
    
    print("📋 Test Input:")
    print(test_table_stream)
    
    # Simulate frontend parsing logic
    import re
    
    # Extract table content
    match = re.search(r'<table_stream>(.*?)</table_stream>', test_table_stream, re.DOTALL)
    if match:
        table_content = match.group(1).strip()
        print("\n✅ Successfully extracted table content")
        
        # Parse lines
        lines = [line.strip() for line in table_content.split('\n') if line.strip()]
        print(f"✅ Parsed {len(lines)} lines")
        
        if lines:
            # Parse headers
            header_line = lines[0]
            headers = [h.strip() for h in header_line.split('|')]
            print(f"✅ Parsed {len(headers)} headers: {headers}")
            
            # Parse data rows
            rows = []
            for i, line in enumerate(lines[1:], 1):
                cells = [c.strip() for c in line.split('|')]
                print(f"✅ Row {i}: {len(cells)} cells")
                
                if len(cells) == len(headers):
                    row = {}
                    for j, header in enumerate(headers):
                        row[header] = cells[j] if j < len(cells) else ''
                    rows.append(row)
                    print(f"   ✅ Row {i} parsed successfully")
                else:
                    print(f"   ❌ Row {i} column mismatch: expected {len(headers)}, got {len(cells)}")
            
            print(f"\n📊 Final Results:")
            print(f"   Headers: {len(headers)}")
            print(f"   Rows: {len(rows)}")
            print(f"   Expected columns: 7")
            print(f"   Status: {'✅ PASS' if len(headers) == 7 and len(rows) > 0 else '❌ FAIL'}")
            
            return len(headers) == 7 and len(rows) > 0
    else:
        print("❌ Could not extract table content")
        return False

if __name__ == "__main__":
    print("🚀 Starting Backend Prompt Format Test")
    print("=" * 60)
    
    success1 = test_prompt_format()
    success2 = test_frontend_parsing_compatibility()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Backend prompt format is correct")
        print("✅ Frontend parsing compatibility verified")
        print("✅ Table parsing fixes should work correctly")
    else:
        print("❌ SOME TESTS FAILED")
        if not success1:
            print("❌ Backend prompt format issues detected")
        if not success2:
            print("❌ Frontend parsing compatibility issues detected")
    
    print("\n💡 Next Steps:")
    print("1. Start the backend server: cd backend && python3 main.py")
    print("2. Test with real API calls using the table function")
    print("3. Verify column alignment in the frontend application")
