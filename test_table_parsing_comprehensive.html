<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Table Parsing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-input {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            border-left: 4px solid #007bff;
        }
        .test-output {
            background: #e8f5e8;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .error {
            background: #ffe8e8;
            color: #d00;
            border-left: 4px solid #dc3545;
        }
        .success {
            background: #e8f5e8;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
            background: white;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            color: #333;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .debug-info {
            background: #f0f8ff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 14px;
            border-left: 4px solid #17a2b8;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        .status.pass { background: #d4edda; color: #155724; }
        .status.fail { background: #f8d7da; color: #721c24; }
        .header { 
            background: #007bff; 
            color: white; 
            padding: 20px; 
            border-radius: 8px; 
            margin-bottom: 20px;
        }
        .summary {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 Comprehensive Table Parsing Test Suite</h1>
        <p>Testing the fixed table parsing logic with various formats and edge cases</p>
    </div>

    <div class="summary" id="summary">
        <h3>Test Summary</h3>
        <div id="summary-content">Running tests...</div>
    </div>

    <div class="test-container">
        <h2>Test 1: Pipe-delimited with Outer Pipes (Fixed Format)</h2>
        <div class="test-input" id="test1-input">|Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks|
|001|S/S 430 BA NO PI|0.015(+/-0.0015)" x 2.343(+/-0.005)" x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish|
|002|S/S 430 BA NO PI|0.015(+/-0.0015)" x 2.406(+/-0.005)" x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish|
|003|S/S 430 BA NO PI|0.015(+/-0.0015)" x 16.50(+/-0.005)" x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish|</div>
        <div class="test-output" id="test1-output"></div>
        <div class="debug-info" id="test1-debug"></div>
    </div>

    <div class="test-container">
        <h2>Test 2: Backend Table Stream Format (New Format)</h2>
        <div class="test-input" id="test2-input"><table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015" x 2.343" x COIL|7190#|0.38mm x 59.51mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015" x 2.406" x COIL|8061#|0.38mm x 61.11mm x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015" x 16.50" x COIL|12550#|0.38mm x 419.1mm x COIL|5692.7 kg|BA Finish
</table_stream></div>
        <div class="test-output" id="test2-output"></div>
        <div class="debug-info" id="test2-debug"></div>
    </div>

    <div class="test-container">
        <h2>Test 3: Problematic Data (Edge Cases)</h2>
        <div class="test-input" id="test3-input">Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
006|S/S 430 BA NO PI|0.015(+/-0.0015)" x 47.000(+/-0.031|118001#|0.38(+/-0.04)mm x 1193.8(+/-0.79)mm x COIL|53524.9 kg|
007|S/S 430 BA NO PI|0.015(+/-0.0015)" x 35.438(+/-0.03125)" x COIL|62515#|0.38(+/-0.04)mm x 900.1(+/-0.79)mm x COIL|28356.8 kg|BA Finish
008|S/S 430 #2B NO PI|0.015(+/-0.0015)" x 16.938(+/-0.005)" x COIL|5000#|0.38(+/-0.04)mm x 430.2(+/-0.13)mm x COIL|2268.0 kg|#2B Finish</div>
        <div class="test-output" id="test3-output"></div>
        <div class="debug-info" id="test3-debug"></div>
    </div>

    <div class="test-container">
        <h2>Test 4: Tab-delimited Format</h2>
        <div class="test-input" id="test4-input">Item Code	Description	Size (Original)	Customer QTY	Size (Converted)	Converted QTY	Remarks
001	S/S 430 BA NO PI	0.015" x 2.343" x COIL	7190#	0.38mm x 59.51mm x COIL	3261.2 kg	BA Finish
002	S/S 430 BA NO PI	0.015" x 2.406" x COIL	8061#	0.38mm x 61.11mm x COIL	3656.7 kg	BA Finish</div>
        <div class="test-output" id="test4-output"></div>
        <div class="debug-info" id="test4-debug"></div>
    </div>

    <script>
        // Implement the exact parsing logic from our fixes
        function parseConvertedContent(content) {
            try {
                console.log('parseConvertedContent: Starting parse with content length:', content.length);

                // Check for table_stream tags first
                const tableStreamMatch = content.match(/<table_stream>([\s\S]*?)<\/table_stream>/);
                if (tableStreamMatch) {
                    const tableContent = tableStreamMatch[1].trim();
                    const remainingContent = content.replace(/<table_stream>[\s\S]*?<\/table_stream>/, '').trim();
                    
                    console.log('Found table_stream content:', tableContent);
                    
                    // Parse the table format using robust delimiter detection
                    const lines = tableContent.split('\n').filter(line => line.trim());
                    
                    if (lines.length >= 2) {
                        const headerLine = lines[0];
                        let headers = [];
                        let delimiter = '|';
                        
                        // Robust delimiter detection
                        if (headerLine.includes('|')) {
                            const cleanHeader = headerLine.replace(/^\||\|$/g, '');
                            headers = cleanHeader.split('|').map(h => h.trim());
                            delimiter = '|';
                        } else if (headerLine.includes('\t')) {
                            headers = headerLine.split('\t').map(h => h.trim());
                            delimiter = '\t';
                        } else if (headerLine.match(/\s{2,}/)) {
                            headers = headerLine.split(/\s{2,}/).map(h => h.trim());
                            delimiter = 'space';
                        } else {
                            headers = [headerLine.trim()];
                            delimiter = 'single';
                        }
                        
                        console.log('Parsed headers:', { headers, delimiter, count: headers.length });
                        
                        const rows = lines.slice(1).map((line, lineIndex) => {
                            let cells = [];
                            
                            if (delimiter === '|') {
                                const cleanLine = line.replace(/^\||\|$/g, '');
                                cells = cleanLine.split('|').map(c => c.trim());
                            } else if (delimiter === '\t') {
                                cells = line.split('\t').map(c => c.trim());
                            } else if (delimiter === 'space') {
                                cells = line.split(/\s{2,}/).map(c => c.trim());
                            } else {
                                cells = [line.trim()];
                            }
                            
                            console.log(`Row ${lineIndex}:`, { cells, count: cells.length });
                            
                            const row = {};
                            headers.forEach((header, index) => {
                                row[header] = cells[index] || '';
                            });
                            return row;
                        });
                        
                        return {
                            hasTable: true,
                            tableContent: '',
                            cleanContent: remainingContent,
                            tableData: { headers, rows }
                        };
                    }
                }
                
                // Fallback to direct table parsing
                const lines = content.split('\n').filter(line => line.trim());
                if (lines.length >= 2) {
                    const headerLine = lines[0];
                    let headers = [];
                    let delimiter = '|';
                    
                    if (headerLine.includes('|')) {
                        const cleanHeader = headerLine.replace(/^\||\|$/g, '');
                        headers = cleanHeader.split('|').map(h => h.trim());
                        delimiter = '|';
                    } else if (headerLine.includes('\t')) {
                        headers = headerLine.split('\t').map(h => h.trim());
                        delimiter = '\t';
                    } else if (headerLine.match(/\s{2,}/)) {
                        headers = headerLine.split(/\s{2,}/).map(h => h.trim());
                        delimiter = 'space';
                    }
                    
                    if (headers.length >= 6) {
                        const rows = lines.slice(1).map(line => {
                            let cells = [];
                            
                            if (delimiter === '|') {
                                const cleanLine = line.replace(/^\||\|$/g, '');
                                cells = cleanLine.split('|').map(c => c.trim());
                            } else if (delimiter === '\t') {
                                cells = line.split('\t').map(c => c.trim());
                            } else if (delimiter === 'space') {
                                cells = line.split(/\s{2,}/).map(c => c.trim());
                            }
                            
                            const row = {};
                            headers.forEach((header, index) => {
                                row[header] = cells[index] || '';
                            });
                            return row;
                        });
                        
                        return {
                            hasTable: true,
                            tableContent: '',
                            cleanContent: '',
                            tableData: { headers, rows }
                        };
                    }
                }
                
                return {
                    hasTable: false,
                    tableContent: '',
                    cleanContent: content
                };
                
            } catch (error) {
                console.error('Parse error:', error);
                return {
                    hasTable: false,
                    tableContent: '',
                    cleanContent: content,
                    error: error.message
                };
            }
        }

        function renderTable(tableData) {
            if (!tableData || !tableData.headers || !tableData.rows) {
                return '<div class="error">Invalid table data</div>';
            }

            let html = '<table><thead><tr>';
            tableData.headers.forEach(header => {
                html += `<th>${header}</th>`;
            });
            html += '</tr></thead><tbody>';

            tableData.rows.forEach((row, index) => {
                html += '<tr>';
                tableData.headers.forEach(header => {
                    const cellValue = row[header] || '';
                    html += `<td>${cellValue}</td>`;
                });
                html += '</tr>';
            });

            html += '</tbody></table>';
            return html;
        }

        function runTest(testId) {
            const input = document.getElementById(`${testId}-input`).textContent;
            const outputDiv = document.getElementById(`${testId}-output`);
            const debugDiv = document.getElementById(`${testId}-debug`);

            console.log(`\n=== Running ${testId} ===`);
            
            const result = parseConvertedContent(input);
            
            let status = 'fail';
            let statusText = 'FAIL';
            
            if (result.hasTable && result.tableData) {
                const { headers, rows } = result.tableData;
                
                // Check if we have the expected number of columns
                const expectedColumns = 7;
                const hasCorrectColumns = headers.length === expectedColumns;
                
                // Check if all rows have the correct number of cells
                const allRowsValid = rows.every(row => 
                    Object.keys(row).length === headers.length &&
                    headers.every(header => row.hasOwnProperty(header))
                );
                
                if (hasCorrectColumns && allRowsValid && rows.length > 0) {
                    status = 'pass';
                    statusText = 'PASS';
                    outputDiv.innerHTML = renderTable(result.tableData);
                } else {
                    outputDiv.innerHTML = `<div class="error">Table validation failed: ${hasCorrectColumns ? 'Columns OK' : 'Wrong column count'}, ${allRowsValid ? 'Rows OK' : 'Row validation failed'}</div>`;
                }
            } else {
                outputDiv.innerHTML = `<div class="error">No valid table found: ${result.error || 'Unknown error'}</div>`;
            }
            
            debugDiv.innerHTML = `
                <div class="status ${status}">${statusText}</div>
                <strong>Debug Info:</strong><br>
                Has Table: ${result.hasTable}<br>
                Headers: ${JSON.stringify(result.tableData?.headers || [])}<br>
                Header Count: ${result.tableData?.headers?.length || 0}<br>
                Row Count: ${result.tableData?.rows?.length || 0}<br>
                Expected Columns: 7<br>
                Error: ${result.error || 'None'}<br>
                First Row: ${JSON.stringify(result.tableData?.rows?.[0] || {})}
            `;
            
            return status === 'pass';
        }

        function updateSummary(results) {
            const passed = results.filter(r => r).length;
            const total = results.length;
            const percentage = Math.round((passed / total) * 100);
            
            const summaryDiv = document.getElementById('summary-content');
            summaryDiv.innerHTML = `
                <strong>Results: ${passed}/${total} tests passed (${percentage}%)</strong><br>
                ${passed === total ? 
                    '<span style="color: #28a745;">🎉 All tests passed! Table parsing fixes are working correctly.</span>' : 
                    '<span style="color: #dc3545;">⚠️ Some tests failed. Review the debug information above.</span>'
                }
            `;
        }

        // Run all tests when page loads
        window.onload = function() {
            console.log('🚀 Starting comprehensive table parsing tests...');
            
            const results = [
                runTest('test1'),
                runTest('test2'),
                runTest('test3'),
                runTest('test4')
            ];
            
            updateSummary(results);
            
            console.log('✅ All tests completed');
        };
    </script>
</body>
</html>
