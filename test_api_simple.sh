#!/bin/bash

# Simple API test script using curl
echo "🧪 Testing Steel Unit Converter API"
echo "=================================="

BASE_URL="http://localhost:8000"

# Test 1: Health check
echo ""
echo "📋 Test 1: Health Check"
echo "----------------------"
curl -s "$BASE_URL/health" | jq '.' 2>/dev/null || curl -s "$BASE_URL/health"

# Test 2: LLM conversion endpoint
echo ""
echo "📋 Test 2: LLM Conversion"
echo "------------------------"
echo "Testing: Convert 1 inch to mm"

curl -s -X POST "$BASE_URL/llm" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Convert 1 inch to mm",
    "unit_system": "metric",
    "function": "conversion"
  }' | jq '.result.converted_text' 2>/dev/null || echo "Response received (jq not available for formatting)"

# Test 3: Table conversion
echo ""
echo "📋 Test 3: Table Conversion"
echo "--------------------------"
echo "Testing table conversion with test data..."

curl -s -X POST "$BASE_URL/llm" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "S/S 430 BA NO PI\n.015(+/-.0015) X 2.343\"(+/-.005) X COIL                                              7190#\n.015(+/-.0015) X 2.406\"(+/-.005) X COIL                                              8,061#",
    "unit_system": "metric",
    "function": "conversion",
    "table_mode": true
  }' > test_table_response.json

echo "Table response saved to test_table_response.json"
echo "Checking for table indicators..."

if grep -q "table_stream\|hasTable.*true" test_table_response.json; then
    echo "✅ Table format detected in response"
else
    echo "⚠️ No table format detected"
fi

# Test 4: Streaming endpoint (basic test)
echo ""
echo "📋 Test 4: Streaming Endpoint"
echo "----------------------------"
echo "Testing streaming conversion..."

curl -s -X POST "$BASE_URL/llm/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Convert 2 feet to meters",
    "unit_system": "metric",
    "function": "conversion"
  }' > test_streaming_response.txt

echo "Streaming response saved to test_streaming_response.txt"
echo "Checking streaming response..."

if grep -q "data:" test_streaming_response.txt; then
    echo "✅ Streaming format detected"
    echo "Response chunks:"
    head -5 test_streaming_response.txt
else
    echo "⚠️ No streaming format detected"
    echo "Response:"
    head -3 test_streaming_response.txt
fi

echo ""
echo "🎉 API tests completed!"
echo ""
echo "📁 Generated files:"
echo "  - test_table_response.json (table conversion response)"
echo "  - test_streaming_response.txt (streaming response)"
echo ""
echo "🌐 Frontend should be accessible at: http://localhost:5173"
echo "🔧 Backend API is running at: http://localhost:8000"
