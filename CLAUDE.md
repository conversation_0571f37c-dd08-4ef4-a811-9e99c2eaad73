# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Steel Unit Converter (钢铁智联) is a comprehensive AI-powered unit conversion application specifically designed for the steel industry. It features a ChatGPT-style interface with streaming table generation, multilingual support (Chinese/English), and specialized steel industry terminology handling.

## Key Architecture Components

### Frontend (React + TypeScript)
- **Chat Interface**: ChatGPT-style streaming conversation with table rendering
- **Streaming Table Parser**: Real-time table parsing from LLM responses using `StreamingTableParser` class
- **Material-UI Components**: Custom table components with Excel export functionality
- **State Management**: React hooks + Context API for auth, chat, and language state
- **Table Processing Pipeline**: `frontend/src/utils/tableUtils.ts` handles streaming table parsing with 7-column validation

### Backend (FastAPI + Python)
- **LLM Integration**: Volcano Engine DeepSeek R1 model for steel industry conversions
- **Streaming Service**: Server-Sent Events (SSE) for real-time streaming responses
- **Prompt Engineering**: Specialized prompts in `backend/llm_prompt.txt` for steel industry terminology
- **MySQL RDS**: Production database with memory optimizations for low-end servers
- **Authentication**: JWT-based user system with email verification

### LLM Pipeline Architecture
1. **Input Processing**: Steel industry data recognition (S/S 430, BA finish, etc.)
2. **Streaming Response**: Chunked streaming with `<table_stream>` format
3. **Table Parsing**: Frontend parses streamed data into structured 7-column tables
4. **Format**: `Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks`

## Development Commands

### Backend
```bash
# Development (from backend directory)
cd backend && bash start.sh

# Production
cd backend && uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4

# Fix imports and dependencies
cd backend && python3 fix_imports.py && python3 install_missing_deps.py
```

### Frontend
```bash
# Development
cd frontend && npm run dev

# Production build (for low-memory servers)
cd frontend && npm run build:low-memory

# Linting
cd frontend && npm run lint
```

### Full Application
```bash
# Development (both frontend + backend)
./start.sh

# Backend only
./start.sh backend

# Frontend only
./start.sh frontend

# Production
./start.sh prod
```

### Testing
```bash
# Backend streaming API test
python3 debug_streaming_response.py

# Table corruption debugging
python3 debug_table_corruption.py

# Test specific endpoints
python3 backend/test_api_endpoints.py
```

## Critical Implementation Details

### Table Streaming Format
The LLM generates tables in a specific format that must be preserved:
```
<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015in x 2.343in x COIL|7190#|0.38mm x 59.51mm x COIL|3261.2 kg|BA Finish
</table_stream>
```

### Streaming Architecture
- **Backend**: `backend/llm/service.py` handles streaming from Volcano Engine
- **Frontend**: `frontend/src/services/streamingService.ts` processes SSE streams
- **Parser**: `frontend/src/utils/tableUtils.ts` contains `StreamingTableParser` class
- **Debug**: Enhanced logging tracks corruption at chunk, accumulation, and parsing levels

### Memory Optimization
Production deployment targets 2GB memory servers:
- Use `npm run build:low-memory` for frontend builds
- MySQL RDS optimizations in `backend/mysql_adapter.py`
- Streaming buffer limits (8KB) to prevent memory overflow

### Steel Industry Terminology
The application specifically handles:
- Steel grades: S/S 430, T304, 316L
- Surface finishes: BA, 2B, HRAP, NO PI
- Unit conversions: Imperial ↔ Metric with steel industry precision
- Weight calculations: lbs/# → kg, K units (1K = 1,000 lbs)

### Deployment Configuration
- **Production**: Uses MySQL RDS, nginx, Aliyun ECS
- **Environment**: Set via `.env.production` with Volcano Engine API keys
- **CORS**: Configured for steelnet.ai domain and localhost development
- **HTTPS**: Required for production domain to prevent mixed content errors

### Chat History & Session Management
- **Storage**: `frontend/src/services/chatSessionStorage.ts` handles persistent chat sessions
- **Streaming Content**: Critical bug fixed where `accumulatedStreamingContentRef.current` was reset before saving
- **Session Switching**: Proper cleanup of streaming state when switching between chat sessions

## Key Cursor Rules Integration
- Use `start-prod.sh` for production deployment
- Test with MCP browser-control server
- Build locally then deploy to production (2GB memory limit)
- Configure backend routes with server IP instead of localhost for production
- Apply color theory in UI design choices
- Use HTML table format for Excel compatibility in tabulation feature