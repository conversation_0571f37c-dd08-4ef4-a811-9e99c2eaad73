#!/usr/bin/env python3
"""
Test script to verify SMTP configuration is correctly loaded
"""

import os
import sys

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

# Import backend config at module level
try:
    from backend.config import *
    config_loaded = True
except Exception as e:
    print(f"Error loading backend config: {e}")
    config_loaded = False

def test_smtp_config():
    print("=== Testing SMTP Configuration Loading ===")
    print()
    
    if not config_loaded:
        print("✗ Backend config could not be loaded")
        return False
    
    print("✓ Backend config loaded successfully")
    
    # Check environment variables
    smtp_server = os.getenv("SMTP_SERVER")
    smtp_port = os.getenv("SMTP_PORT")
    smtp_username = os.getenv("SMTP_USERNAME") 
    smtp_password = os.getenv("SMTP_PASSWORD")
    
    print("Current environment variables:")
    print(f"  SMTP_SERVER: {smtp_server}")
    print(f"  SMTP_PORT: {smtp_port}")
    print(f"  SMTP_USERNAME: {smtp_username}")
    print(f"  SMTP_PASSWORD: {'*' * len(smtp_password) if smtp_password else 'NOT_SET'}")
    print()
    
    # Test the auth_utils function
    try:
        from backend.auth_utils import send_verification_email
        print("✓ Successfully imported send_verification_email")
        
        # Check what port the function would use
        actual_port = int(os.getenv("SMTP_PORT", "465"))
        print(f"✓ Function would use port: {actual_port}")
        
        if actual_port == 465:
            print("✅ Correct port 465 is configured!")
        else:
            print(f"❌ Wrong port {actual_port} is configured (should be 465)")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Error testing auth_utils: {e}")
        return False

def test_direct_smtp_connection():
    print("\n=== Testing Direct SMTP Connection to Port 465 ===")
    print()
    
    try:
        import socket
        import ssl
        
        smtp_server = os.getenv("SMTP_SERVER", "smtp.qiye.aliyun.com")
        smtp_port = int(os.getenv("SMTP_PORT", "465"))
        
        print(f"Testing connection to {smtp_server}:{smtp_port}")
        
        # Test socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        try:
            result = sock.connect_ex((smtp_server, smtp_port))
            if result == 0:
                print(f"✓ Socket connection to {smtp_server}:{smtp_port} successful")
                sock.close()
                return True
            else:
                print(f"✗ Socket connection failed with code {result}")
                return False
        except Exception as e:
            print(f"✗ Socket connection error: {e}")
            return False
        finally:
            sock.close()
            
    except Exception as e:
        print(f"✗ Connection test error: {e}")
        return False

if __name__ == "__main__":
    print("Steel Unit Converter - SMTP Configuration Test")
    print("=" * 55)
    
    # Test configuration loading
    config_test = test_smtp_config()
    
    # Test direct connection
    connection_test = test_direct_smtp_connection()
    
    print("\n" + "=" * 55)
    print("TEST RESULTS:")
    
    if config_test and connection_test:
        print("✅ All tests passed! SMTP is configured correctly with port 465.")
        print()
        print("📧 Ready to test email sending:")
        print("   1. Start the backend: ./start_dev_linux_with_url_access.sh --backend")
        print("   2. Test email sending with a real email address")
        print("   3. Check logs to confirm port 465 is being used")
    else:
        print("❌ Some tests failed. Please check the configuration.")
        
        if not config_test:
            print("- Configuration loading failed")
        if not connection_test:
            print("- Network connection to port 465 failed")
    
    print("=" * 55) 