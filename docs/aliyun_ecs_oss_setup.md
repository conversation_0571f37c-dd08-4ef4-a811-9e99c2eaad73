# 阿里云 ECS 和 OSS 配置指南

本文档提供了如何在阿里云 ECS（弹性计算服务）上部署应用并与阿里云 OSS（对象存储服务）集成的详细步骤。

## 1. 阿里云 ECS 配置

### 1.1 创建 ECS 实例

1. 登录[阿里云控制台](https://console.aliyun.com/)
2. 在左侧导航栏选择"云服务器 ECS"
3. 点击"创建实例"
4. 选择合适的配置：
   - 地域：选择离用户较近的地域（如华北2-北京）
   - 实例规格：推荐至少 2核4GB 内存
   - 操作系统：选择 Ubuntu 20.04 或更高版本
   - 存储：系统盘至少 40GB
   - 网络：选择已有 VPC 或创建新的 VPC
   - 安全组：开放以下端口：
     - 22 (SSH)
     - 80 (HTTP)
     - 443 (HTTPS)
     - 3000 (前端开发端口)
     - 8000 (后端 API 端口)
5. 设置登录密码或密钥对
6. 完成实例创建

### 1.2 连接到 ECS 实例

```bash
ssh root@<your-ecs-ip>
```

### 1.3 安装依赖

```bash
# 更新系统
apt update
apt upgrade -y

# 安装 Node.js 和 npm
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# 安装 Python 和相关工具
apt install -y python3 python3-pip python3-venv

# 安装 Git
apt install -y git

# 安装 PostgreSQL
apt install -y postgresql postgresql-contrib

# 安装 Nginx
apt install -y nginx
```

### 1.4 配置 PostgreSQL

```bash
# 切换到 postgres 用户
sudo -i -u postgres

# 创建数据库用户
createuser --interactive --pwprompt steelnet

# 创建数据库
createdb --owner=steelnet steelnet_db

# 退出 postgres 用户
exit
```

## 2. 阿里云 OSS 配置

### 2.1 创建 OSS Bucket

1. 登录[阿里云控制台](https://console.aliyun.com/)
2. 在左侧导航栏选择"对象存储 OSS"
3. 点击"创建 Bucket"
4. 配置 Bucket：
   - 名称：为您的 Bucket 选择一个唯一名称，如 `steelnet-chat-history`
   - 地域：选择与 ECS 相同的地域以降低延迟和费用
   - 存储类型：标准存储
   - 读写权限：私有（推荐用于保护用户数据）
   - 其他选项：保持默认或根据需要调整
5. 点击"确定"创建 Bucket

### 2.2 创建 RAM 用户和权限

为了安全起见，应该创建一个只具有 OSS 特定权限的 RAM 用户，而不是使用主账号的 AccessKey。

1. 在阿里云控制台左侧导航栏选择"访问控制 RAM"
2. 选择"用户" > "创建用户"
3. 配置用户：
   - 设置登录名和显示名
   - 访问方式：选择"编程访问"
4. 点击"确定"创建用户
5. 保存生成的 AccessKey ID 和 AccessKey Secret（这是唯一可以看到 Secret 的时机）
6. 为用户添加权限：
   - 选择刚创建的用户
   - 点击"添加权限"
   - 添加 `AliyunOSSFullAccess` 权限，或者创建自定义权限策略只允许访问特定 Bucket

## 3. 部署应用

### 3.1 克隆代码仓库

```bash
# 创建应用目录
mkdir -p /var/www/steelnet
cd /var/www/steelnet

# 克隆代码仓库
git clone <your-repository-url> .
```

### 3.2 配置后端

```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 创建环境变量文件
cp .env.example .env
# 编辑 .env 文件设置正确的配置
nano .env
```

确保在 `.env` 文件中填写以下关键配置：

```
DATABASE_URL=postgresql+asyncpg://steelnet:your-password@localhost/steelnet_db
ALIYUN_OSS_ACCESS_KEY_ID=your-access-key-id
ALIYUN_OSS_ACCESS_KEY_SECRET=your-access-key-secret
ALIYUN_OSS_ENDPOINT=https://oss-cn-your-region.aliyuncs.com
ALIYUN_OSS_BUCKET_NAME=steelnet-chat-history
```

### 3.3 配置前端

```bash
# 进入前端目录
cd ../frontend

# 安装依赖
npm install

# 创建生产环境配置
cp .env.example .env.production
# 编辑环境配置
nano .env.production

# 构建前端
npm run build
```

### 3.4 配置 Nginx

创建 Nginx 配置文件：

```bash
nano /etc/nginx/sites-available/steelnet
```

添加以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /var/www/steelnet/frontend/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    # 后端 API
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

启用站点并重启 Nginx：

```bash
ln -s /etc/nginx/sites-available/steelnet /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
```

### 3.5 使用 Gunicorn 和 Supervisor 运行后端

安装 Supervisor：

```bash
apt install -y supervisor
```

创建 Supervisor 配置文件：

```bash
nano /etc/supervisor/conf.d/steelnet.conf
```

添加以下配置：

```ini
[program:steelnet]
directory=/var/www/steelnet/backend
command=/var/www/steelnet/backend/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8000
autostart=true
autorestart=true
stderr_logfile=/var/log/steelnet/api.err.log
stdout_logfile=/var/log/steelnet/api.out.log
environment=
    PATH="/var/www/steelnet/backend/venv/bin",
    PYTHONPATH='/var/www/steelnet/backend'
```

创建日志目录并更新 Supervisor：

```bash
mkdir -p /var/log/steelnet
supervisorctl reread
supervisorctl update
```

## 4. OSS 性能优化最佳实践

### 4.1 OSS 对象命名约定

- 使用有组织的命名模式，例如 `chat_histories/user_id/session_id_timestamp.json`
- 避免以常用前缀开头，以防止 OSS 存储分区热点问题

### 4.2 对象访问优化

- 使用临时访问凭证而不是永久访问密钥
- 对于频繁访问的对象，考虑使用 OSS 的 CDN 加速
- 实现适当的缓存策略以减少重复请求

### 4.3 批量操作优化

- 使用 OSS 的批量操作功能而不是多次单独请求
- 对于大型列表操作，使用分页查询，每页大小合理设置

### 4.4 数据传输优化

- 对大对象使用分片上传
- 启用传输加速功能，特别是跨区域访问时
- 避免小文件频繁上传，可以考虑合并小文件

### 4.5 成本优化

- 定期清理不需要的历史聊天记录
- 为不同类型的数据使用不同的存储类型（标准、低频等）
- 设置生命周期规则自动转换不常访问的对象为低频访问或归档存储

## 5. 维护和监控

### 5.1 实施监控

- 使用阿里云的 CloudMonitor 监控 ECS 和 OSS 的性能和使用情况
- 设置警报以在任何问题发生时通知管理员

### 5.2 定期备份

- 定期备份数据库内容
- 考虑使用阿里云的数据库备份服务

### 5.3 日志管理

- 实施日志轮换以防止日志文件过大
- 考虑使用阿里云的日志服务收集和分析日志

## 6. 安全最佳实践

- 经常更新系统和依赖项
- 使用 HTTPS 加密通信
- 实施适当的防火墙规则
- 遵循最小权限原则
- 定期审核访问控制和权限

## 7. 故障排除

### OSS 连接问题

如果应用无法连接到 OSS，请检查：

1. 确保 ECS 安全组允许出站连接
2. 验证 RAM 用户有正确的权限
3. 确认 AccessKey 和 Secret 正确配置
4. 检查 OSS Bucket 的访问控制设置
5. 验证所使用的 endpoint 是否正确

### 数据库连接问题

如果应用无法连接到 PostgreSQL，请检查：

1. 确保 PostgreSQL 服务正在运行
2. 验证数据库用户和密码
3. 检查 pg_hba.conf 文件中的连接设置 