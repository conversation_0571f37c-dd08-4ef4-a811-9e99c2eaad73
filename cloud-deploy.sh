#!/bin/bash

# Cloud Deployment Script for Steel Unit Converter
# This script provides utilities to deploy the application to various cloud providers

set -e  # Exit on error

# Default configuration
ENV_FILE=".env"
DEPLOY_TARGET="aliyun"  # Default target is Aliyun
ACTION="deploy"        # Default action
DOCKER_REGISTRY=""     # Docker registry URL (if using container registry)

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Display help message
function show_help {
  echo -e "${BLUE}Steel Unit Converter - Cloud Deployment Script${NC}"
  echo ""
  echo "Usage: ./cloud-deploy.sh [OPTIONS]"
  echo ""
  echo "Options:"
  echo "  -t, --target TARGET    Deployment target (aliyun, aws, azure, gcp)"
  echo "  -a, --action ACTION    Action to perform (deploy, update, destroy)"
  echo "  -e, --env FILE         Environment file (default: .env)"
  echo "  -r, --registry URL     Docker registry URL"
  echo "  -h, --help             Show this help message"
  echo ""
  echo "Examples:"
  echo "  ./cloud-deploy.sh --target aliyun"
  echo "  ./cloud-deploy.sh --target aws --action update"
  echo "  ./cloud-deploy.sh --target azure --env .env.production"
  echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"

  case $key in
    -h|--help)
      show_help
      exit 0
      ;;
    -t|--target)
      DEPLOY_TARGET="$2"
      shift 2
      ;;
    -a|--action)
      ACTION="$2"
      shift 2
      ;;
    -e|--env)
      ENV_FILE="$2"
      shift 2
      ;;
    -r|--registry)
      DOCKER_REGISTRY="$2"
      shift 2
      ;;
    *)
      echo -e "${RED}Unknown option: $1${NC}"
      show_help
      exit 1
      ;;
  esac
done

# Load environment variables
if [ -f "$ENV_FILE" ]; then
  echo -e "${GREEN}Loading environment from $ENV_FILE${NC}"
  export $(grep -v '^#' "$ENV_FILE" | xargs)
else
  echo -e "${YELLOW}Warning: Environment file $ENV_FILE not found. Using default values.${NC}"
fi

# Check required tools
function check_tools {
  echo -e "${BLUE}Checking required tools...${NC}"
  
  if ! command -v docker >/dev/null 2>&1; then
    echo -e "${RED}Error: Docker is not installed. Please install Docker.${NC}"
    exit 1
  fi
  
  if ! command -v docker-compose >/dev/null 2>&1; then
    echo -e "${RED}Error: Docker Compose is not installed. Please install Docker Compose.${NC}"
    exit 1
  fi
  
  # Check for cloud provider-specific tools
  case "$DEPLOY_TARGET" in
    aws)
      if ! command -v aws >/dev/null 2>&1; then
        echo -e "${RED}Error: AWS CLI is not installed. Please install AWS CLI.${NC}"
        exit 1
      fi
      ;;
    azure)
      if ! command -v az >/dev/null 2>&1; then
        echo -e "${RED}Error: Azure CLI is not installed. Please install Azure CLI.${NC}"
        exit 1
      fi
      ;;
    gcp)
      if ! command -v gcloud >/dev/null 2>&1; then
        echo -e "${RED}Error: Google Cloud SDK is not installed. Please install Google Cloud SDK.${NC}"
        exit 1
      fi
      ;;
    aliyun)
      if ! command -v aliyun >/dev/null 2>&1; then
        echo -e "${YELLOW}Warning: Aliyun CLI is not installed. Some functionality may be limited.${NC}"
      fi
      ;;
  esac
  
  echo -e "${GREEN}All required tools are available.${NC}"
}

# Build Docker images
function build_images {
  echo -e "${BLUE}Building Docker images...${NC}"
  
  # Add version tag based on current date and git commit if available
  VERSION_TAG=$(date +"%Y%m%d")
  if command -v git >/dev/null 2>&1 && [ -d .git ]; then
    GIT_SHORT_HASH=$(git rev-parse --short HEAD)
    VERSION_TAG="${VERSION_TAG}-${GIT_SHORT_HASH}"
  fi
  
  echo -e "Using version tag: ${GREEN}${VERSION_TAG}${NC}"
  
  # Build images with version tag
  docker-compose build --build-arg VERSION="${VERSION_TAG}"
  
  # Tag images for registry if specified
  if [ -n "$DOCKER_REGISTRY" ]; then
    echo -e "${BLUE}Tagging images for registry: ${DOCKER_REGISTRY}${NC}"
    docker tag steel-converter-frontend:latest ${DOCKER_REGISTRY}/steel-converter-frontend:${VERSION_TAG}
    docker tag steel-converter-backend:latest ${DOCKER_REGISTRY}/steel-converter-backend:${VERSION_TAG}
    docker tag steel-converter-frontend:latest ${DOCKER_REGISTRY}/steel-converter-frontend:latest
    docker tag steel-converter-backend:latest ${DOCKER_REGISTRY}/steel-converter-backend:latest
  fi
  
  echo -e "${GREEN}Docker images built successfully.${NC}"
}

# Push Docker images to registry
function push_images {
  if [ -n "$DOCKER_REGISTRY" ]; then
    echo -e "${BLUE}Pushing images to registry: ${DOCKER_REGISTRY}${NC}"
    
    docker push ${DOCKER_REGISTRY}/steel-converter-frontend:latest
    docker push ${DOCKER_REGISTRY}/steel-converter-backend:latest
    docker push ${DOCKER_REGISTRY}/steel-converter-frontend:${VERSION_TAG}
    docker push ${DOCKER_REGISTRY}/steel-converter-backend:${VERSION_TAG}
    
    echo -e "${GREEN}Images pushed to registry successfully.${NC}"
  else
    echo -e "${YELLOW}No registry specified. Skipping image push.${NC}"
  fi
}

# Deploy to Aliyun ECS
function deploy_to_aliyun {
  echo -e "${BLUE}Deploying to Aliyun ECS...${NC}"
  
  if [ -z "$ALIYUN_ECS_IP" ]; then
    echo -e "${RED}Error: ALIYUN_ECS_IP environment variable not set. Please specify the ECS instance IP.${NC}"
    exit 1
  fi
  
  if [ -z "$ALIYUN_ECS_USER" ]; then
    ALIYUN_ECS_USER="root"
    echo -e "${YELLOW}ALIYUN_ECS_USER not set. Using default: ${ALIYUN_ECS_USER}${NC}"
  fi
  
  # Prepare deployment directory
  echo -e "${BLUE}Preparing deployment package...${NC}"
  
  DEPLOY_DIR="deploy-package"
  mkdir -p ${DEPLOY_DIR}
  
  cp docker-compose.yml ${DEPLOY_DIR}/
  cp .env ${DEPLOY_DIR}/.env
  
  # If using SSH key authentication
  SSH_KEY_OPTION=""
  if [ -n "$ALIYUN_SSH_KEY" ]; then
    SSH_KEY_OPTION="-i $ALIYUN_SSH_KEY"
  fi
  
  # Create deployment script
  cat > ${DEPLOY_DIR}/deploy.sh << 'EOL'
#!/bin/bash
set -e

# Create application directory
mkdir -p /app/steel-converter
cp docker-compose.yml /app/steel-converter/
cp .env /app/steel-converter/

# Navigate to application directory
cd /app/steel-converter

# Pull latest images and deploy
docker-compose pull
docker-compose down
docker-compose up -d

echo "Deployment completed successfully!"
EOL
  
  chmod +x ${DEPLOY_DIR}/deploy.sh
  
  # Compress the deployment package
  tar -czf deploy.tar.gz -C ${DEPLOY_DIR} .
  
  # Copy package to server and execute deployment script
  echo -e "${BLUE}Copying deployment package to ${ALIYUN_ECS_IP}...${NC}"
  scp ${SSH_KEY_OPTION} deploy.tar.gz ${ALIYUN_ECS_USER}@${ALIYUN_ECS_IP}:/tmp/
  
  echo -e "${BLUE}Executing deployment script...${NC}"
  ssh ${SSH_KEY_OPTION} ${ALIYUN_ECS_USER}@${ALIYUN_ECS_IP} << 'ENDSSH'
mkdir -p /tmp/deploy
tar -xzf /tmp/deploy.tar.gz -C /tmp/deploy
cd /tmp/deploy
chmod +x deploy.sh
./deploy.sh
rm -rf /tmp/deploy
rm /tmp/deploy.tar.gz
ENDSSH
  
  # Clean up local files
  rm -rf ${DEPLOY_DIR}
  rm deploy.tar.gz
  
  echo -e "${GREEN}Deployment to Aliyun ECS completed successfully!${NC}"
}

# Deploy to AWS
function deploy_to_aws {
  echo -e "${BLUE}Deploying to AWS...${NC}"
  # Implement AWS-specific deployment logic here
  echo -e "${YELLOW}AWS deployment is not fully implemented yet.${NC}"
}

# Deploy to Azure
function deploy_to_azure {
  echo -e "${BLUE}Deploying to Azure...${NC}"
  # Implement Azure-specific deployment logic here
  echo -e "${YELLOW}Azure deployment is not fully implemented yet.${NC}"
}

# Deploy to GCP
function deploy_to_gcp {
  echo -e "${BLUE}Deploying to Google Cloud Platform...${NC}"
  # Implement GCP-specific deployment logic here
  echo -e "${YELLOW}GCP deployment is not fully implemented yet.${NC}"
}

# Main execution logic
echo -e "${BLUE}Starting deployment process...${NC}"
echo -e "Target platform: ${GREEN}${DEPLOY_TARGET}${NC}"
echo -e "Action: ${GREEN}${ACTION}${NC}"

check_tools

case "$ACTION" in
  deploy)
    build_images
    push_images
    
    case "$DEPLOY_TARGET" in
      aliyun)
        deploy_to_aliyun
        ;;
      aws)
        deploy_to_aws
        ;;
      azure)
        deploy_to_azure
        ;;
      gcp)
        deploy_to_gcp
        ;;
      *)
        echo -e "${RED}Error: Unknown deployment target: ${DEPLOY_TARGET}${NC}"
        exit 1
        ;;
    esac
    ;;
  update)
    echo -e "${BLUE}Updating existing deployment...${NC}"
    build_images
    push_images
    # Add update logic based on target platform
    echo -e "${GREEN}Update completed.${NC}"
    ;;
  destroy)
    echo -e "${RED}Warning: This will destroy the deployment. Are you sure? (y/n)${NC}"
    read -r confirmation
    if [[ $confirmation == [yY] || $confirmation == [yY][eE][sS] ]]; then
      echo -e "${BLUE}Destroying deployment...${NC}"
      # Add destroy logic based on target platform
      echo -e "${GREEN}Destroy completed.${NC}"
    else
      echo -e "${BLUE}Destroy operation cancelled.${NC}"
    fi
    ;;
  *)
    echo -e "${RED}Error: Unknown action: ${ACTION}${NC}"
    exit 1
    ;;
esac

echo -e "${GREEN}Deployment process completed successfully!${NC}" 