#!/usr/bin/env python3
"""
Test script to directly call the table API and check the response
"""
import requests
import json
import time

def test_table_api():
    # Test data from testCase.txt
    test_data = """S/S 430  BA  NO PI
 
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#
.015(+/-.0015) X 19.68"(+/-.03125) X COIL                                         8,835#
.015(+/-.0015) X 47.438"(+/-.03125) X COIL                                      57,655#
.015(+/-.0015) X 47.000"(+/-.03125) X COIL                                      118,001#
.015(+/-.0015) X 35.438"(+/-.03125) X COIL                                      62,515#
 
S/S 430 #2B NO PI
 
.015(+/-.0015) X 16.938"(+/-.005) X COIL                                           5,000#
.016(+/-.0015) X 19.6875"(+/-.005) X COIL                                         725,321#
.016(+/-.0015) X 35.500"(+/-.03125) X COIL                                      122,083#
.016(+/-.0015) X 36.000"(+/-.03125) X COIL                                      234,265#
.016(+/-.0015) X 48.000"(+/-.03125) X COIL                                      201,347#
.018(+/-.0015) X 36.000"(+/-.03125) X COIL                                      33,841#"""

    # API endpoint
    url = "http://localhost:8000/api/llm/stream"
    
    # Request payload
    payload = {
        "text": test_data,
        "function": "table",
        "unit_system": "metric"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    print("Testing table API with streaming...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("-" * 50)
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print("-" * 50)
        
        if response.status_code == 200:
            print("Streaming response:")
            accumulated_data = ""
            
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    print(f"Raw line: {repr(decoded_line)}")
                    
                    if decoded_line.startswith('data: '):
                        try:
                            data_str = decoded_line[6:]  # Remove 'data: ' prefix
                            if data_str == '[DONE]':
                                print("Stream completed")
                                break
                            
                            data = json.loads(data_str)
                            print(f"Parsed data: {data}")
                            
                            if 'content' in data:
                                accumulated_data += data['content']
                                print(f"Accumulated so far: {len(accumulated_data)} chars")
                                
                                # Check for table patterns
                                if '<table_stream>' in accumulated_data:
                                    print("Found <table_stream> pattern!")
                                if '<table_data>' in accumulated_data:
                                    print("Found <table_data> pattern!")
                                    
                        except json.JSONDecodeError as e:
                            print(f"JSON decode error: {e} for line: {decoded_line}")
                    
            print("-" * 50)
            print(f"Final accumulated data ({len(accumulated_data)} chars):")
            print(accumulated_data)
            
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    test_table_api()