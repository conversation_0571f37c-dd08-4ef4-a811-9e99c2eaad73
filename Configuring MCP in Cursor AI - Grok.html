<!DOCTYPE html>
<!-- saved from url=(0017)https://grok.com/ -->
<html lang="en" class="light" style="color-scheme: light;"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><link rel="preload" href="https://grok.com/_next/static/media/97e0cb1ae144a2a9-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" href="https://grok.com/_next/static/media/a34f9d1faa5f3315-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" href="https://grok.com/_next/static/media/df0a9ae256c0569c-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="stylesheet" href="./Configuring MCP in Cursor AI - Grok_files/436bf36531cf1546.css" data-precedence="next"><link rel="stylesheet" href="./Configuring MCP in Cursor AI - Grok_files/dd89e605550f760e.css" data-precedence="next"><link rel="stylesheet" href="./Configuring MCP in Cursor AI - Grok_files/c3ac1cdbd523914d.css" data-precedence="next"><link rel="preload" as="script" fetchpriority="low" href="./Configuring MCP in Cursor AI - Grok_files/webpack-cc0bf9ac3a96aecc.js.下载"><script src="./Configuring MCP in Cursor AI - Grok_files/1e4b4fa1-7702f2548ee08eb8.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/2216583b-6895e63c2f43f265.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/3691-1934676a1100e034.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/main-app-582740a51501573d.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/9ffa21ba-dee464089eadcd73.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/d409a184-00fbd592e6a0d9eb.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/f6b89a79-838a38945c9e12fa.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/3766-01d2809b07e26439.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/1758-adb0e88bd04b7b55.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/7489-a9def2d8bce51b41.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/5539-46da12145a360fdd.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/4344-a174f8ce2bdf5027.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/6809-b9dfe63bd183f442.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/922-dd2074861dc76f15.js.下载" async=""></script><script src="./Configuring MCP in Cursor AI - Grok_files/1780-c212e0f93d119e41.js.下载" async=""></script><link rel="preload" href="./Configuring MCP in Cursor AI - Grok_files/js" as="script"><meta name="theme-color" media="(prefers-color-scheme: light)" content="#F8F7F5"><meta name="theme-color" media="(prefers-color-scheme: dark)" content="#1d1e20"><script src="./Configuring MCP in Cursor AI - Grok_files/polyfills-42372ed130431b0a.js.下载" nomodule=""></script><link rel="preload" href="https://grok.com/_next/static/media/26a46d62cd723877-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" href="https://grok.com/_next/static/media/3478b6abef19b3b3-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" href="https://grok.com/_next/static/media/55c55f0601d81cf3-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" href="https://grok.com/_next/static/media/581909926a08bbc8-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" href="https://grok.com/_next/static/media/6d93bde91c0c2823-s.p.woff2" as="font" crossorigin="" type="font/woff2"><style type="text/css">[vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1)}[vaul-drawer][vaul-drawer-direction=bottom]{transform:translate3d(0,100%,0)}[vaul-drawer][vaul-drawer-direction=top]{transform:translate3d(0,-100%,0)}[vaul-drawer][vaul-drawer-direction=left]{transform:translate3d(-100%,0,0)}[vaul-drawer][vaul-drawer-direction=right]{transform:translate3d(100%,0,0)}.vaul-dragging .vaul-scrollable [vault-drawer-direction=top]{overflow-y:hidden!important}.vaul-dragging .vaul-scrollable [vault-drawer-direction=bottom]{overflow-y:hidden!important}.vaul-dragging .vaul-scrollable [vault-drawer-direction=left]{overflow-x:hidden!important}.vaul-dragging .vaul-scrollable [vault-drawer-direction=right]{overflow-x:hidden!important}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[vaul-overlay]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[vaul-overlay][vaul-drawer-visible=true]{opacity:1}[vaul-drawer]::after{content:'';position:absolute;background:inherit;background-color:inherit}[vaul-drawer][vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[vaul-drawer][vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[vaul-drawer][vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[vaul-drawer][vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[vaul-handle]{display:block;position:relative;opacity:.8;margin-left:auto;margin-right:auto;height:5px;width:56px;border-radius:1rem;touch-action:pan-y;cursor:grab}[vaul-handle]:active,[vaul-handle]:hover{opacity:1}[vaul-handle]:active{cursor:grabbing}[vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}[vaul-overlay][vaul-snap-points=true]:not([vaul-snap-points-overlay=true]):not([data-state=closed]){opacity:0}[vaul-overlay][vaul-snap-points-overlay=true]:not([vaul-drawer-visible=false]){opacity:1}@media (hover:hover) and (pointer:fine){[vaul-drawer]{user-select:none}}@media (pointer:fine){[vaul-handle-hitarea]:{width:100%;height:100%}}</style><style type="text/css">:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
</style><style type="text/css">.transform-component-module_wrapper__SPB86 {
  position: relative;
  width: -moz-fit-content;
  width: fit-content;
  height: -moz-fit-content;
  height: fit-content;
  overflow: hidden;
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
  margin: 0;
  padding: 0;
}
.transform-component-module_content__FBWxo {
  display: flex;
  flex-wrap: wrap;
  width: -moz-fit-content;
  width: fit-content;
  height: -moz-fit-content;
  height: fit-content;
  margin: 0;
  padding: 0;
  transform-origin: 0% 0%;
}
.transform-component-module_content__FBWxo img {
  pointer-events: none;
}
</style><script async="async" type="text/javascript" src="./Configuring MCP in Cursor AI - Grok_files/editor.main.js.下载"></script><link rel="stylesheet" type="text/css" data-name="vs/editor/editor.main" href="./Configuring MCP in Cursor AI - Grok_files/editor.main.css"><script async="async" type="text/javascript" src="./Configuring MCP in Cursor AI - Grok_files/editor.main.nls.js.下载"></script><meta name="viewport" content="width=device-width, initial-scale=1, interactive-widget=resizes-content"><title>Configuring MCP in Cursor AI - Grok</title><meta name="description" content="Grok is a free AI assistant designed by xAI to maximize truth and objectivity. Grok offers real-time search, image generation, trend analysis, and more."><link rel="manifest" href="https://grok.com/manifest.webmanifest" crossorigin="use-credentials"><meta name="apple-itunes-app" content="app-id=6670324846"><meta property="og:title" content="Grok"><meta property="og:description" content="Grok is a free AI assistant designed by xAI to maximize truth and objectivity. Grok offers real-time search, image generation, trend analysis, and more."><meta property="og:image:type" content="image/png"><meta property="og:image:width" content="1200"><meta property="og:image:height" content="630"><meta property="og:image" content="https://grok.com/opengraph-image.png?436301709c39b1b6"><meta name="twitter:card" content="summary_large_image"><meta name="twitter:title" content="Grok"><meta name="twitter:description" content="Grok is a free AI assistant designed by xAI to maximize truth and objectivity. Grok offers real-time search, image generation, trend analysis, and more."><meta name="twitter:image:type" content="image/png"><meta name="twitter:image:width" content="1200"><meta name="twitter:image:height" content="630"><meta name="twitter:image" content="https://grok.com/twitter-image.png?436301709c39b1b6"><link rel="icon" href="https://grok.com/images/favicon-light.png" media="(prefers-color-scheme: light)"><link rel="icon" href="https://grok.com/images/favicon-dark.png" media="(prefers-color-scheme: dark)"><meta name="next-size-adjust"></head><body class="antialiased font-body text-primary bg-background overflow-x-hidden tracking-[-0.1px] __variable_a17b92 __variable_2b114f" dir="" style=""><script>!function(){try{var d=document.documentElement,c=d.classList;c.remove('light','dark');var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';c.add('dark')}else{d.style.colorScheme = 'light';c.add('light')}}else if(e){c.add(e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><script src="./Configuring MCP in Cursor AI - Grok_files/webpack-cc0bf9ac3a96aecc.js.下载" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/26a46d62cd723877-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/media/3478b6abef19b3b3-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n3:HL[\"/_next/static/media/55c55f0601d81cf3-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n4:HL[\"/_next/static/media/581909926a08bbc8-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n5:HL[\"/_next/static/media/6d93bde91c0c2823-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n6:HL[\"/_next/static/media/97e0cb1ae144a2a9-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n7:HL[\"/_next/static/media/a34f9d1faa5f3315-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n8:HL[\"/_next/static/media/df0a9ae256c0569c-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n9:HL[\"/_next/static/css/436bf36531cf1546.css\",\"style\"]\na:HL[\"/_next/static/css/dd89e605550f760e.css\",\"style\"]\nb:HL[\"/_next/static/css/c3ac1cdbd523914d.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"c:I[16200,[],\"\"]\ne:I[76991,[],\"ClientPageRoot\"]\nf:I[79763,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"1805\",\"static/chunks/f6b89a79-838a38945c9e12fa.js\",\"9782\",\"static/chunks/24cf1b50-e10fc3dde64946ce.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-adb0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"7489\",\"static/chunks/7489-a9def2d8bce51b41.js\",\"7862\",\"static/chunks/7862-7c564bd100c36cf0.js\",\"1796\",\"static/chunks/1796-977f4987b1748987.js\",\"9981\",\"static/chunks/9981-bcf9e8f0036acfc9.js\",\"4210\",\"static/chunks/4210-71f659d792cce01c.js\",\"829\",\"static/chunks/829-1934cffe4c66ad4d.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"6809\",\"static/chunks/6809-b9dfe63bd183f442.js\",\"922\",\"static/chunks/922-dd2074861dc76f15.js\",\"1780\",\"static/chunks/1780-c212e0f93d119e41.js\",\"5641\",\"static/chunks/5641-ef9ba9627663a9b1.js\",\"8593\",\"static/chunks/8593-0e5038c4b5b51b73.js\",\"8999\",\"static/chunks/8999-26c91b30e380921e.js\",\"1931\",\"static/chunks/app/page-2e1f9257a4dd5416.js\"],\"default\",1]\n12:I[55385,[\"6470\",\"static/chunks/app/global-error-2f595858aa8a7722.js\"],\"default\"]\n13:[]\n0:[\"$\",\"$Lc\",null,{\"buildId\":\"fwzzZbW16ci_qXSUZ676F\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"?q=how+to+config+mcp+on+cursor\"],\"initialTree\":[\"\",{\"children\":[\"__PAGE__?{\\\"q\\\":\\\"how to config mcp on cursor\\\"}\",{}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"__PAGE__\",{},[[\"$Ld\",[\"$\",\"$Le\",null,{\"props\":{\"params\":{},\"searchParams\":{\"q\":\"how to config mcp on cursor\"}},\"Component\":\"$f\"}],null],null],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/436bf36531cf1546.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/dd89e605550f760e.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"styles"])</script><script>self.__next_f.push([1,"heet\",\"href\":\"/_next/static/css/c3ac1cdbd523914d.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],\"$L10\"],null],null],\"couldBeIntercepted\":false,\"initialHead\":[null,\"$L11\"],\"globalErrorComponent\":\"$12\",\"missingSlots\":\"$W13\"}]\n"])</script><script>self.__next_f.push([1,"14:I[73805,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"9782\",\"static/chunks/24cf1b50-e10fc3dde64946ce.js\",\"421\",\"static/chunks/238c1eb1-b50d25f2ffabbce5.js\",\"8073\",\"static/chunks/7511aae1-387150aaefe36b63.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-adb0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"7489\",\"static/chunks/7489-a9def2d8bce51b41.js\",\"7862\",\"static/chunks/7862-7c564bd100c36cf0.js\",\"1796\",\"static/chunks/1796-977f4987b1748987.js\",\"2713\",\"static/chunks/2713-2d7f0e48eb46bbf4.js\",\"6439\",\"static/chunks/6439-925d83a2caa1a8d7.js\",\"1234\",\"static/chunks/1234-cf26467848cb148c.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"6809\",\"static/chunks/6809-b9dfe63bd183f442.js\",\"1780\",\"static/chunks/1780-c212e0f93d119e41.js\",\"8593\",\"static/chunks/8593-0e5038c4b5b51b73.js\",\"5794\",\"static/chunks/5794-ec6b0a5c3372dbfa.js\",\"3185\",\"static/chunks/app/layout-bcb26c95ca6642a1.js\"],\"GoogleAnalytics\"]\n15:I[34549,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"9782\",\"static/chunks/24cf1b50-e10fc3dde64946ce.js\",\"421\",\"static/chunks/238c1eb1-b50d25f2ffabbce5.js\",\"8073\",\"static/chunks/7511aae1-387150aaefe36b63.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-adb0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"7489\",\"static/chunks/7489-a9def2d8bce51b41.js\",\"7862\",\"static/chunks/7862-7c564bd100c36cf0.js\",\"1796\",\"static/chunks/1796-977f4987b1748987.js\",\"2713\",\"static/chunks/2713-2d7f0e48eb46bbf4.js\",\"6439\",\"static/chunks/6439-925d83a2caa1a8d7.js\",\"1234\",\"static/chunks/1234-cf26467848cb148c.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"6"])</script><script>self.__next_f.push([1,"809\",\"static/chunks/6809-b9dfe63bd183f442.js\",\"1780\",\"static/chunks/1780-c212e0f93d119e41.js\",\"8593\",\"static/chunks/8593-0e5038c4b5b51b73.js\",\"5794\",\"static/chunks/5794-ec6b0a5c3372dbfa.js\",\"3185\",\"static/chunks/app/layout-bcb26c95ca6642a1.js\"],\"default\"]\n16:I[81695,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"9782\",\"static/chunks/24cf1b50-e10fc3dde64946ce.js\",\"421\",\"static/chunks/238c1eb1-b50d25f2ffabbce5.js\",\"8073\",\"static/chunks/7511aae1-387150aaefe36b63.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-adb0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"7489\",\"static/chunks/7489-a9def2d8bce51b41.js\",\"7862\",\"static/chunks/7862-7c564bd100c36cf0.js\",\"1796\",\"static/chunks/1796-977f4987b1748987.js\",\"2713\",\"static/chunks/2713-2d7f0e48eb46bbf4.js\",\"6439\",\"static/chunks/6439-925d83a2caa1a8d7.js\",\"1234\",\"static/chunks/1234-cf26467848cb148c.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"6809\",\"static/chunks/6809-b9dfe63bd183f442.js\",\"1780\",\"static/chunks/1780-c212e0f93d119e41.js\",\"8593\",\"static/chunks/8593-0e5038c4b5b51b73.js\",\"5794\",\"static/chunks/5794-ec6b0a5c3372dbfa.js\",\"3185\",\"static/chunks/app/layout-bcb26c95ca6642a1.js\"],\"ThemeProvider\"]\n17:I[44024,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"9782\",\"static/chunks/24cf1b50-e10fc3dde64946ce.js\",\"421\",\"static/chunks/238c1eb1-b50d25f2ffabbce5.js\",\"8073\",\"static/chunks/7511aae1-387150aaefe36b63.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-adb0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"7489\",\"static/chunks/7489-a9def2d8bce51b41.js\",\"7862\",\"static/chunks/7862-7c564bd100c36cf0.js\",\"1796\",\"static/chunks/1796-977f4987b1748987.js\",\"2713\",\"static/chunks/2713-2d7f0e48e"])</script><script>self.__next_f.push([1,"b46bbf4.js\",\"6439\",\"static/chunks/6439-925d83a2caa1a8d7.js\",\"1234\",\"static/chunks/1234-cf26467848cb148c.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"6809\",\"static/chunks/6809-b9dfe63bd183f442.js\",\"1780\",\"static/chunks/1780-c212e0f93d119e41.js\",\"8593\",\"static/chunks/8593-0e5038c4b5b51b73.js\",\"5794\",\"static/chunks/5794-ec6b0a5c3372dbfa.js\",\"3185\",\"static/chunks/app/layout-bcb26c95ca6642a1.js\"],\"default\"]\n18:I[31352,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"9782\",\"static/chunks/24cf1b50-e10fc3dde64946ce.js\",\"421\",\"static/chunks/238c1eb1-b50d25f2ffabbce5.js\",\"8073\",\"static/chunks/7511aae1-387150aaefe36b63.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-adb0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"7489\",\"static/chunks/7489-a9def2d8bce51b41.js\",\"7862\",\"static/chunks/7862-7c564bd100c36cf0.js\",\"1796\",\"static/chunks/1796-977f4987b1748987.js\",\"2713\",\"static/chunks/2713-2d7f0e48eb46bbf4.js\",\"6439\",\"static/chunks/6439-925d83a2caa1a8d7.js\",\"1234\",\"static/chunks/1234-cf26467848cb148c.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"6809\",\"static/chunks/6809-b9dfe63bd183f442.js\",\"1780\",\"static/chunks/1780-c212e0f93d119e41.js\",\"8593\",\"static/chunks/8593-0e5038c4b5b51b73.js\",\"5794\",\"static/chunks/5794-ec6b0a5c3372dbfa.js\",\"3185\",\"static/chunks/app/layout-bcb26c95ca6642a1.js\"],\"default\"]\n19:I[68100,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"9782\",\"static/chunks/24cf1b50-e10fc3dde64946ce.js\",\"421\",\"static/chunks/238c1eb1-b50d25f2ffabbce5.js\",\"8073\",\"static/chunks/7511aae1-387150aaefe36b63.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-a"])</script><script>self.__next_f.push([1,"db0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"7489\",\"static/chunks/7489-a9def2d8bce51b41.js\",\"7862\",\"static/chunks/7862-7c564bd100c36cf0.js\",\"1796\",\"static/chunks/1796-977f4987b1748987.js\",\"2713\",\"static/chunks/2713-2d7f0e48eb46bbf4.js\",\"6439\",\"static/chunks/6439-925d83a2caa1a8d7.js\",\"1234\",\"static/chunks/1234-cf26467848cb148c.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"6809\",\"static/chunks/6809-b9dfe63bd183f442.js\",\"1780\",\"static/chunks/1780-c212e0f93d119e41.js\",\"8593\",\"static/chunks/8593-0e5038c4b5b51b73.js\",\"5794\",\"static/chunks/5794-ec6b0a5c3372dbfa.js\",\"3185\",\"static/chunks/app/layout-bcb26c95ca6642a1.js\"],\"default\"]\n1a:I[34010,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"9782\",\"static/chunks/24cf1b50-e10fc3dde64946ce.js\",\"421\",\"static/chunks/238c1eb1-b50d25f2ffabbce5.js\",\"8073\",\"static/chunks/7511aae1-387150aaefe36b63.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-adb0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"7489\",\"static/chunks/7489-a9def2d8bce51b41.js\",\"7862\",\"static/chunks/7862-7c564bd100c36cf0.js\",\"1796\",\"static/chunks/1796-977f4987b1748987.js\",\"2713\",\"static/chunks/2713-2d7f0e48eb46bbf4.js\",\"6439\",\"static/chunks/6439-925d83a2caa1a8d7.js\",\"1234\",\"static/chunks/1234-cf26467848cb148c.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"6809\",\"static/chunks/6809-b9dfe63bd183f442.js\",\"1780\",\"static/chunks/1780-c212e0f93d119e41.js\",\"8593\",\"static/chunks/8593-0e5038c4b5b51b73.js\",\"5794\",\"static/chunks/5794-ec6b0a5c3372dbfa.js\",\"3185\",\"static/chunks/app/layout-bcb26c95ca6642a1.js\"],\"default\"]\n1b:I[22901,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"9782\",\"static/chunks/24cf1"])</script><script>self.__next_f.push([1,"b50-e10fc3dde64946ce.js\",\"421\",\"static/chunks/238c1eb1-b50d25f2ffabbce5.js\",\"8073\",\"static/chunks/7511aae1-387150aaefe36b63.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-adb0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"7489\",\"static/chunks/7489-a9def2d8bce51b41.js\",\"7862\",\"static/chunks/7862-7c564bd100c36cf0.js\",\"1796\",\"static/chunks/1796-977f4987b1748987.js\",\"2713\",\"static/chunks/2713-2d7f0e48eb46bbf4.js\",\"6439\",\"static/chunks/6439-925d83a2caa1a8d7.js\",\"1234\",\"static/chunks/1234-cf26467848cb148c.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"6809\",\"static/chunks/6809-b9dfe63bd183f442.js\",\"1780\",\"static/chunks/1780-c212e0f93d119e41.js\",\"8593\",\"static/chunks/8593-0e5038c4b5b51b73.js\",\"5794\",\"static/chunks/5794-ec6b0a5c3372dbfa.js\",\"3185\",\"static/chunks/app/layout-bcb26c95ca6642a1.js\"],\"Loadout\"]\n1c:I[50601,[],\"\"]\n1d:I[89679,[],\"\"]\n1e:I[31978,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-adb0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"9160\",\"static/chunks/app/not-found-3f6a28771685d1e9.js\"],\"default\"]\n1f:I[53981,[\"6299\",\"static/chunks/9ffa21ba-dee464089eadcd73.js\",\"536\",\"static/chunks/d409a184-00fbd592e6a0d9eb.js\",\"9782\",\"static/chunks/24cf1b50-e10fc3dde64946ce.js\",\"421\",\"static/chunks/238c1eb1-b50d25f2ffabbce5.js\",\"8073\",\"static/chunks/7511aae1-387150aaefe36b63.js\",\"3766\",\"static/chunks/3766-01d2809b07e26439.js\",\"47\",\"static/chunks/47-6fe340b736b196e0.js\",\"1758\",\"static/chunks/1758-adb0e88bd04b7b55.js\",\"2754\",\"static/chunks/2754-ccc6339c565388b0.js\",\"7489\",\"static/chunks/7489-a9def"])</script><script>self.__next_f.push([1,"2d8bce51b41.js\",\"7862\",\"static/chunks/7862-7c564bd100c36cf0.js\",\"1796\",\"static/chunks/1796-977f4987b1748987.js\",\"2713\",\"static/chunks/2713-2d7f0e48eb46bbf4.js\",\"6439\",\"static/chunks/6439-925d83a2caa1a8d7.js\",\"1234\",\"static/chunks/1234-cf26467848cb148c.js\",\"5539\",\"static/chunks/5539-46da12145a360fdd.js\",\"4344\",\"static/chunks/4344-a174f8ce2bdf5027.js\",\"1403\",\"static/chunks/1403-e9206f880c1ff519.js\",\"6809\",\"static/chunks/6809-b9dfe63bd183f442.js\",\"1780\",\"static/chunks/1780-c212e0f93d119e41.js\",\"8593\",\"static/chunks/8593-0e5038c4b5b51b73.js\",\"5794\",\"static/chunks/5794-ec6b0a5c3372dbfa.js\",\"3185\",\"static/chunks/app/layout-bcb26c95ca6642a1.js\"],\"Toaster\"]\n10:[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"dark\",\"children\":[[\"$\",\"$L14\",null,{\"gaId\":\"G-8FEWB057YH\"}],[\"$\",\"head\",null,{\"children\":[[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"media\":\"(prefers-color-scheme: light)\",\"content\":\"#F8F7F5\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"media\":\"(prefers-color-scheme: dark)\",\"content\":\"#1d1e20\"}]]}],[\"$\",\"body\",null,{\"className\":\"antialiased font-body text-primary bg-background overflow-x-hidden tracking-[-0.1px] __variable_a17b92 __variable_2b114f\",\"children\":[\"$\",\"$L15\",null,{\"children\":[\"$\",\"$L16\",null,{\"attribute\":\"class\",\"enableSystem\":true,\"children\":[\"$\",\"$L17\",null,{\"envVars\":{\"CLOUD_CONSOLE_URL\":\"http://localhost:22000\",\"GROK_COM_URL\":\"https://grok.com\",\"WEBSITE_URL\":\"http://x.ai\",\"ACCOUNT_URL\":\"https://accounts.x.ai\",\"STATSIG_CLIENT_KEY\":\"client-geuXcIifCakavCD8XolWwQtHdvjMfttWpBkgg3JDTaF\",\"NODE_ENV\":\"production\",\"SUPPORT_URL\":\"https://discord.gg/kqCc86jM55\",\"ASSET_SERVER_URL\":\"https://assets.grok.com\",\"STRIPE_PUBLISHABLE_KEY\":\"pk_live_51PksddHJohyvID2czYoS55WPrVoy5tQ2a6QqoEFqeZV85CCGShKYpZ6rn5wzdY1HhNzcttFdZuTsCrwip8Qp3PSN00boS3LmEd\",\"STRIPE_CUSTOMER_PORTAL_URL\":\"https://billing.stripe.com/p/login/eVa4iNeNQ3kla9W6oo\",\"APP_STORE_URL\":\"https://apps.apple.com/us/app/grok/id6670324846\",\"GOOGLE_PLAY_URL\":\"https://play.google.com/store/apps/details?id=ai.x.grok\"},\"children\":[\"$\",\"$L18\",null,{\"children\":[\"$\",\"$L19\",null,{\"ini"])</script><script>self.__next_f.push([1,"tialData\":{},\"children\":[\"$\",\"$L1a\",null,{\"env\":\"production\",\"clientKey\":\"client-geuXcIifCakavCD8XolWwQtHdvjMfttWpBkgg3JDTaF\",\"user\":\"$undefined\",\"anonUser\":\"$undefined\",\"children\":[[\"$\",\"$L1b\",null,{\"children\":[\"$\",\"$L1c\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L1d\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"$L1e\",null,{}],\"notFoundStyles\":[]}]}],[\"$\",\"$L1f\",null,{\"position\":\"top-center\"}]]}]}]}]}]}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"11:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1, interactive-widget=resizes-content\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"Grok\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Grok is a free AI assistant designed by xAI to maximize truth and objectivity. Grok offers real-time search, image generation, trend analysis, and more.\"}],[\"$\",\"link\",\"4\",{\"rel\":\"manifest\",\"href\":\"/manifest.webmanifest\",\"crossOrigin\":\"use-credentials\"}],[\"$\",\"meta\",\"5\",{\"name\":\"apple-itunes-app\",\"content\":\"app-id=6670324846\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:title\",\"content\":\"Grok\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:description\",\"content\":\"Grok is a free AI assistant designed by xAI to maximize truth and objectivity. Grok offers real-time search, image generation, trend analysis, and more.\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image:type\",\"content\":\"image/png\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:image\",\"content\":\"https://grok.com/opengraph-image.png?436301709c39b1b6\"}],[\"$\",\"meta\",\"12\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:title\",\"content\":\"Grok\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:description\",\"content\":\"Grok is a free AI assistant designed by xAI to maximize truth and objectivity. Grok offers real-time search, image generation, trend analysis, and more.\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:image:type\",\"content\":\"image/png\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:image\",\"content\":\"https://grok.com/twitter-image.png?436301709c39b1b6\"}],[\"$\",\"link\",\"19\",{\"rel\":\"icon\",\"href\":\"/images/favicon-light.png\",\"media\":\"(prefers-color-scheme: light)\"}],[\"$\",\"link\",\"20\",{\"rel\":\"icon\",\"href\":\"/images/favicon-dark.png\",\"media\":\"(prefers-color-scheme: dark)\"}],[\"$\",\"meta\",\"21\",{\"name\":\"next-size-adjust\"}]]\n"])</script><script>self.__next_f.push([1,"d:null\n"])</script><script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'923a7805196d03b3',t:'MTc0MjUyODg1Ni4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;" src="./Configuring MCP in Cursor AI - Grok_files/saved_resource.html"></iframe><script defer="" src="./Configuring MCP in Cursor AI - Grok_files/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;923a7805196d03b3&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.1.0&quot;,&quot;token&quot;:&quot;115d22700e41497cb28a5ee6c20b51d7&quot;}" crossorigin="anonymous"></script>
<script src="./Configuring MCP in Cursor AI - Grok_files/loader.js.下载"></script><script id="_next-ga-init" data-nscript="afterInteractive">
          window['dataLayer'] = window['dataLayer'] || [];
          function gtag(){window['dataLayer'].push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-8FEWB057YH' );</script><script src="./Configuring MCP in Cursor AI - Grok_files/js" id="_next-ga" data-nscript="afterInteractive"></script><next-route-announcer style="position: absolute;"><template shadowrootmode="open"><div aria-live="assertive" id="__next-route-announcer__" role="alert" style="position: absolute; border: 0px; height: 1px; margin: -1px; padding: 0px; width: 1px; clip: rect(0px, 0px, 0px, 0px); overflow: hidden; white-space: nowrap; overflow-wrap: normal;"></div></template></next-route-announcer><iframe id="nr-ext-rsicon" style="position: absolute; display: none; width: 50px; height: 50px; z-index: 2147483647; border-style: none; background: transparent;" src="./Configuring MCP in Cursor AI - Grok_files/saved_resource(1).html"></iframe><div class="flex w-full h-full"><div class="flex w-full h-full @container/mainview"><main class="h-dvh flex-grow flex-shrink relative selection:bg-highlight w-0 @container"><div class="w-full relative @container/nav z-40"><div class="h-16 top-0 @[80rem]/nav:h-0 @[80rem]/nav:top-8 absolute z-10 flex flex-row items-center justify-center w-full bg-gradient-to-b from-background via-background via-80% to-transparent @[80rem]/nav:from-transparent @[80rem]/nav:via-transparent"><div class="absolute flex flex-row items-center start-1"><a aria-label="主页" class="ms-2 me-[0.5] rounded-lg focus:outline-none focus-visible:ring-1 focus-visible:ring-ring w-8 sm:w-auto" href="https://grok.com/"><svg width="88" height="33" viewBox="0 0 88 33" fill="none" xmlns="http://www.w3.org/2000/svg" class="opacity-80 hover:opacity-100 fill-black dark:fill-white [&amp;&gt;path]:hidden sm:[&amp;&gt;path]:block [&amp;&gt;#mark]:block [&amp;&gt;#furigana]:opacity-60"><path d="M76.4462 24.7077V8.41584H79.0216V19.1679L84.4685 12.9109H87.5908L82.6908 18.2731L87.6364 24.7077H84.5596L80.5539 19.1788L79.0216 19.1679V24.7077H76.4462Z" fill="currentColor"></path><path d="M68.6362 24.9815C64.8074 24.9815 62.7335 22.2662 62.7335 18.7979C62.7335 15.3068 64.8074 12.6143 68.6362 12.6143C72.4878 12.6143 74.5389 15.3068 74.5389 18.7979C74.5389 22.2662 72.4878 24.9815 68.6362 24.9815ZM65.4228 18.7979C65.4228 21.4904 66.8813 22.8366 68.6362 22.8366C70.4139 22.8366 71.8497 21.4904 71.8497 18.7979C71.8497 16.1054 70.4139 14.7363 68.6362 14.7363C66.8813 14.7363 65.4228 16.1054 65.4228 18.7979Z" fill="currentColor"></path><path d="M55.5659 24.7077V14.782L57.731 12.9109H62.3347V15.1014H58.1413V24.7077H55.5659Z" fill="currentColor"></path><path d="M45.7187 25.009C40.8101 25.009 37.8834 21.4448 37.8834 16.5846C37.8834 11.6788 40.9146 8.02795 45.8145 8.02795C49.6433 8.02795 52.4466 9.99027 53.1075 13.6411H50.1675C49.7345 11.5647 48.0024 10.401 45.8145 10.401C42.282 10.401 40.7322 13.4586 40.7322 16.5846C40.7322 19.7106 42.282 22.7454 45.8145 22.7454C49.1875 22.7454 50.6689 20.3039 50.7828 18.2731H45.7006V15.9105H53.381L53.3684 17.1457C53.3684 21.7359 51.4978 25.009 45.7187 25.009Z" fill="currentColor"></path><path d="M13.2371 21.0407L24.3186 12.8506C24.8619 12.4491 25.6384 12.6057 25.8973 13.2294C27.2597 16.5185 26.651 20.4712 23.9403 23.1851C21.2297 25.8989 17.4581 26.4941 14.0108 25.1386L10.2449 26.8843C15.6463 30.5806 22.2053 29.6665 26.304 25.5601C29.5551 22.3051 30.562 17.8683 29.6205 13.8673L29.629 13.8758C28.2637 7.99809 29.9647 5.64871 33.449 0.844576C33.5314 0.730667 33.6139 0.616757 33.6964 0.5L29.1113 5.09055V5.07631L13.2343 21.0436" fill="currentColor" id="mark"></path><path d="M10.9503 23.0313C7.07343 19.3235 7.74185 13.5853 11.0498 10.2763C13.4959 7.82722 17.5036 6.82767 21.0021 8.2971L24.7595 6.55998C24.0826 6.07017 23.215 5.54334 22.2195 5.17313C17.7198 3.31926 12.3326 4.24192 8.67479 7.90126C5.15635 11.4239 4.0499 16.8403 5.94992 21.4622C7.36924 24.9165 5.04257 27.3598 2.69884 29.826C1.86829 30.7002 1.0349 31.5745 0.36364 32.5L10.9474 23.0341" fill="currentColor" id="mark"></path></svg></a></div><div class="grow justify-center hidden max-w-[50%] @[640px]/nav:flex"></div><div class="absolute flex flex-row items-center gap-0.5 ml-auto end-3"><a class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-primary hover:bg-button-ghost-hover h-10 w-10 rounded-full" type="button" data-state="closed" href="https://grok.com/chat"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] " stroke-width="2"><path d="M10 4V4C8.13623 4 7.20435 4 6.46927 4.30448C5.48915 4.71046 4.71046 5.48915 4.30448 6.46927C4 7.20435 4 8.13623 4 10V13.6C4 15.8402 4 16.9603 4.43597 17.816C4.81947 18.5686 5.43139 19.1805 6.18404 19.564C7.03968 20 8.15979 20 10.4 20H14C15.8638 20 16.7956 20 17.5307 19.6955C18.5108 19.2895 19.2895 18.5108 19.6955 17.5307C20 16.7956 20 15.8638 20 14V14" stroke="currentColor" stroke-linecap="square"></path><path d="M12.4393 14.5607L19.5 7.5C20.3284 6.67157 20.3284 5.32843 19.5 4.5C18.6716 3.67157 17.3284 3.67157 16.5 4.5L9.43934 11.5607C9.15804 11.842 9 12.2235 9 12.6213V15H11.3787C11.7765 15 12.158 14.842 12.4393 14.5607Z" stroke="currentColor" stroke-linecap="square"></path></svg></a><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-primary hover:bg-button-ghost-hover h-10 w-10 rounded-full" type="button" aria-label="分享对话" data-state="closed"><span style="opacity: 1; transform: none;"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] " stroke-width="2"><path d="M6.99609 9L11.9961 4L16.9961 9M6.99609 9M12 16V4" stroke="currentColor"></path><path d="M4 15V16C4 18.2091 5.79086 20 8 20H16C18.2091 20 20 18.2091 20 16V15" stroke="currentColor"></path></svg></span></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-primary hover:bg-button-ghost-hover h-10 w-10 rounded-full" type="button" aria-label="历史记录" data-state="closed"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] "><path d="M3 5L19 5" stroke="currentColor" stroke-linecap="square" stroke-linejoin="round"></path><path d="M3 12H7" stroke="currentColor" stroke-linecap="square" stroke-linejoin="round"></path><circle cx="16" cy="15" r="4" stroke="currentColor"></circle><path d="M19 18L21 20" stroke="currentColor" stroke-linecap="square"></path><path d="M3 19H7" stroke="currentColor" stroke-linecap="square" stroke-linejoin="round"></path></svg></button><div class="flex flex-row items-center gap-2"><button class="items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-primary hover:bg-button-ghost-hover h-10 w-10 rounded-full hidden @sm:flex" type="button" id="radix-:rr:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] "><path fill-rule="evenodd" clip-rule="evenodd" d="M12.5 0.999756L13 3.05487C13.9171 3.15638 14.7934 3.39584 15.6072 3.75216L17.0669 2.22365L17.933 2.72358L17.3385 4.75346C18.066 5.29023 18.7096 5.9339 19.2464 6.66143L21.2762 6.06687L21.8006 6.97502L20.2477 8.39271C20.604 9.20642 20.8435 10.0828 20.945 10.9999L23 11.4998V12.4998L20.945 12.9999C20.8435 13.917 20.604 14.7933 20.2476 15.6071L21.8006 17.0248L21.2762 17.9329L19.2463 17.3384C18.7096 18.0659 18.0659 18.7095 17.3384 19.2462L17.933 21.2761L17.067 21.7761L15.6071 20.2476C14.7934 20.6039 13.9171 20.8434 13 20.9449L12.5 22.9998L11.4516 22.9998L11 20.9449C10.0829 20.8434 9.20657 20.6039 8.39288 20.2476L6.93298 21.7762L6.06694 21.2762L6.66159 19.2463C5.93404 18.7095 5.29035 18.0658 4.75357 17.3383L2.72367 17.9329L2.22356 17.0669L3.75225 15.607C3.39594 14.7933 3.15649 13.9169 3.05498 12.9998L1 12.4998V11.4998L3.05499 10.9998C3.1565 10.0828 3.39593 9.20646 3.75222 8.3928L2.22373 6.93291L2.74804 6.02477L4.7535 6.66149C5.29029 5.93392 5.93399 5.29022 6.66155 4.75343L6.06697 2.72357L6.933 2.22356L8.39285 3.75213C9.20655 3.39582 10.0829 3.15637 11 3.05487L11.4516 0.999756H12.5ZM9.94493 5.30609C9.65381 5.39533 9.53714 5.73409 9.68938 5.99779L11.275 8.74408C11.3808 8.92747 11.5907 9.02015 11.802 9.00632C11.8675 9.00203 11.9335 8.99986 12 8.99986C13.1809 8.99986 14.2025 9.68223 14.692 10.6742C14.7857 10.8643 14.971 10.9999 15.1829 10.9999H18.3527C18.657 10.9999 18.892 10.7298 18.8242 10.4332C18.6668 9.74417 18.4077 9.09446 18.0638 8.50004C17.4489 7.43743 16.5625 6.55088 15.4999 5.93608C14.4711 5.34084 13.2769 4.99986 12 4.99986C11.2841 4.99986 10.5942 5.10703 9.94493 5.30609ZM18.8242 13.5666C18.892 13.27 18.657 12.9999 18.3527 12.9999H15.1829C14.9709 12.9999 14.7857 13.1355 14.6919 13.3255C14.2025 14.3175 13.1809 14.9999 12 14.9999C11.9335 14.9999 11.8675 14.9977 11.802 14.9934C11.5907 14.9796 11.3808 15.0723 11.2749 15.2557L9.6894 18.002C9.53716 18.2657 9.65383 18.6044 9.94495 18.6937C10.5943 18.8927 11.2841 18.9999 12 18.9999C13.2769 18.9999 14.4711 18.6589 15.4997 18.0637C16.5624 17.4488 17.4489 16.5624 18.0637 15.4998C18.4077 14.9053 18.6667 14.2556 18.8242 13.5666ZM7.23117 17.1242C7.45419 17.3318 7.80626 17.2637 7.95862 16.9998L9.54293 14.2556C9.64891 14.072 9.6241 13.8437 9.5061 13.6677C9.18676 13.1911 8.99998 12.6175 8.99998 11.9999C8.99998 11.3823 9.18678 10.8086 9.50612 10.3321C9.62411 10.156 9.64893 9.9277 9.54295 9.74414L7.95857 6.99994C7.80621 6.73605 7.45416 6.66788 7.23115 6.87552C6.72333 7.34835 6.28579 7.89573 5.93614 8.50005C5.34095 9.52876 4.99998 10.723 4.99998 11.9999C4.99998 13.2768 5.34096 14.471 5.93617 15.4997C6.28582 16.104 6.72335 16.6513 7.23117 17.1242ZM11.5006 12.8666C11.6468 12.9512 11.8162 12.9999 12 12.9999C12.5522 12.9999 13 12.5521 13 11.9999C13 11.4476 12.5522 10.9999 12 10.9999C11.8161 10.9999 11.6466 11.0486 11.5006 11.1331C11.1991 11.3076 11 11.6311 11 11.9999C11 12.3687 11.1992 12.6922 11.5006 12.8666Z" fill="currentColor"></path></svg></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 bg-button-primary text-background hover:bg-button-primary-hover rounded-full py-2 h-8 px-3 text-sm" type="button"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg><div>注册</div></button><button class="items-center justify-center gap-2 whitespace-nowrap font-medium cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 rounded-full py-2 hidden @sm:flex h-8 px-3 text-sm text-primary bg-transparent ring-1 ring-inset ring-toggle-border hover:bg-button-secondary-hover hover:border-text-secondary" type="button"><div>登录</div></button></div></div></div></div><div class="relative flex flex-col items-center h-full @container/main"><div class="w-full h-full overflow-y-auto overflow-x-hidden scrollbar-gutter-stable flex flex-col items-center px-5"><div class="relative w-full flex flex-col items-center pt-20 pb-4"><div></div><div class="w-full max-w-3xl flex flex-col"><div><div class="relative group flex flex-col justify-center w-full max-w-3xl md:px-4 pb-2 gap-2 items-end"><div dir="auto" class="message-bubble rounded-3xl prose dark:prose-invert break-words text-primary min-h-7 prose-p:opacity-95 prose-strong:opacity-100 bg-foreground border border-input-border max-w-[100%] sm:max-w-[90%] px-4 py-2.5 rounded-br-lg"><span class="whitespace-pre-wrap">how to config mcp on cursor</span></div><div class="order-first sticky hidden @md/mainview:block @xl/mainview:top-0 top-12 h-0 opacity-0 group-focus-within:opacity-100 group-hover:opacity-100 -mt-2 -mr-[36px] @lg/mainview:-mr-12"><div class="min-h-7 py-4 flex flex-col gap-[2px]"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary hover:bg-button-ghost-hover disabled:hover:text-secondary disabled:hover:bg-inherit h-8 w-8 rounded-full" type="button" data-state="closed"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-4"><path d="M18.25 5.75C16.8693 4.36929 14.6307 4.36929 13.25 5.75L10.125 8.875L5.52404 13.476C4.86236 14.1376 4.45361 15.0104 4.36889 15.9423L4 20.0001L8.0578 19.6311C8.98967 19.5464 9.86234 19.1377 10.524 18.476L18.25 10.75C19.6307 9.36929 19.6307 7.13071 18.25 5.75V5.75Z" stroke="currentColor"></path><path d="M12.5 7.5L16.5 11.5" stroke="currentColor"></path></svg></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary hover:bg-button-ghost-hover disabled:hover:text-secondary disabled:hover:bg-inherit h-8 w-8 rounded-full" type="button" data-state="closed"><span style="opacity: 1; transform: none;"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-4"><rect x="3" y="8" width="13" height="13" rx="4" stroke="currentColor"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13 2.00004L12.8842 2.00002C12.0666 1.99982 11.5094 1.99968 11.0246 2.09611C9.92585 2.31466 8.95982 2.88816 8.25008 3.69274C7.90896 4.07944 7.62676 4.51983 7.41722 5.00004H9.76392C10.189 4.52493 10.7628 4.18736 11.4147 4.05768C11.6802 4.00488 12.0228 4.00004 13 4.00004H14.6C15.7366 4.00004 16.5289 4.00081 17.1458 4.05121C17.7509 4.10066 18.0986 4.19283 18.362 4.32702C18.9265 4.61464 19.3854 5.07358 19.673 5.63807C19.8072 5.90142 19.8994 6.24911 19.9488 6.85428C19.9992 7.47112 20 8.26343 20 9.40004V11C20 11.9773 19.9952 12.3199 19.9424 12.5853C19.8127 13.2373 19.4748 13.8114 19 14.2361V16.5829C20.4795 15.9374 21.5804 14.602 21.9039 12.9755C22.0004 12.4907 22.0002 11.9334 22 11.1158L22 11V9.40004V9.35725C22 8.27346 22 7.3993 21.9422 6.69141C21.8826 5.96256 21.7568 5.32238 21.455 4.73008C20.9757 3.78927 20.2108 3.02437 19.27 2.545C18.6777 2.24322 18.0375 2.1174 17.3086 2.05785C16.6007 2.00002 15.7266 2.00003 14.6428 2.00004L14.6 2.00004H13Z" fill="currentColor"></path></svg></span></button></div></div><div class="flex items-center gap-[2px] w-max opacity-0 group-focus-within:opacity-100 group-hover:opacity-100 transition-opacity rounded-lg text-xs bg-background pb-2 px-2 end-4 -mr-2" style="bottom: 128px;"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary hover:bg-button-ghost-hover disabled:hover:text-secondary disabled:hover:bg-inherit h-8 w-8 rounded-full" type="button" data-state="closed"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-4"><path d="M18.25 5.75C16.8693 4.36929 14.6307 4.36929 13.25 5.75L10.125 8.875L5.52404 13.476C4.86236 14.1376 4.45361 15.0104 4.36889 15.9423L4 20.0001L8.0578 19.6311C8.98967 19.5464 9.86234 19.1377 10.524 18.476L18.25 10.75C19.6307 9.36929 19.6307 7.13071 18.25 5.75V5.75Z" stroke="currentColor"></path><path d="M12.5 7.5L16.5 11.5" stroke="currentColor"></path></svg></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary hover:bg-button-ghost-hover disabled:hover:text-secondary disabled:hover:bg-inherit h-8 w-8 rounded-full" type="button" data-state="closed"><span style="opacity: 1; transform: none;"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-4"><rect x="3" y="8" width="13" height="13" rx="4" stroke="currentColor"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13 2.00004L12.8842 2.00002C12.0666 1.99982 11.5094 1.99968 11.0246 2.09611C9.92585 2.31466 8.95982 2.88816 8.25008 3.69274C7.90896 4.07944 7.62676 4.51983 7.41722 5.00004H9.76392C10.189 4.52493 10.7628 4.18736 11.4147 4.05768C11.6802 4.00488 12.0228 4.00004 13 4.00004H14.6C15.7366 4.00004 16.5289 4.00081 17.1458 4.05121C17.7509 4.10066 18.0986 4.19283 18.362 4.32702C18.9265 4.61464 19.3854 5.07358 19.673 5.63807C19.8072 5.90142 19.8994 6.24911 19.9488 6.85428C19.9992 7.47112 20 8.26343 20 9.40004V11C20 11.9773 19.9952 12.3199 19.9424 12.5853C19.8127 13.2373 19.4748 13.8114 19 14.2361V16.5829C20.4795 15.9374 21.5804 14.602 21.9039 12.9755C22.0004 12.4907 22.0002 11.9334 22 11.1158L22 11V9.40004V9.35725C22 8.27346 22 7.3993 21.9422 6.69141C21.8826 5.96256 21.7568 5.32238 21.455 4.73008C20.9757 3.78927 20.2108 3.02437 19.27 2.545C18.6777 2.24322 18.0375 2.1174 17.3086 2.05785C16.6007 2.00002 15.7266 2.00003 14.6428 2.00004L14.6 2.00004H13Z" fill="currentColor"></path></svg></span></button></div><div style="height: 1px; position: absolute; bottom: 0px; z-index: 1000;"></div></div></div><div><div class="relative group flex flex-col justify-center w-full max-w-3xl md:px-4 pb-2 gap-2 items-start"><div dir="auto" class="message-bubble rounded-3xl prose dark:prose-invert break-words text-primary min-h-7 prose-p:opacity-95 prose-strong:opacity-100 w-full max-w-none"><div class="flex -ml-1 text-sm gap-2 mb-3"><div class="flex flex-row items-center pr-4 py-1 rounded-full cursor-pointer bg-foreground border border-card-border hover:bg-card-hover hover:border-card-border-focus"><div style="z-index: 3;"><div class="align-text-bottom select-none my-0 rounded-full opacity-100 ml-1.5 flex bg-popover border border-background z-3"><img class="size-[18px] m-0 rounded-full" src="./Configuring MCP in Cursor AI - Grok_files/AuQiWkVc_normal.jpg" alt=""></div></div><div style="z-index: 2;"><div class="align-text-bottom select-none my-0 rounded-full opacity-100 flex bg-popover border border-background z-2 -ml-2"><img class="size-[18px] m-0 rounded-full" src="./Configuring MCP in Cursor AI - Grok_files/gGSDe5DF_normal.jpg" alt=""></div></div><div style="z-index: 1;"><div class="align-text-bottom select-none my-0 rounded-full opacity-100 flex bg-popover border border-background z-1 -ml-2"><img class="size-[18px] m-0 rounded-full" src="./Configuring MCP in Cursor AI - Grok_files/gGSDe5DF_normal.jpg" alt=""></div></div><div class="truncate ml-2">10个𝕏帖子</div></div><div class="flex flex-row items-center pr-4 py-1 rounded-full cursor-pointer bg-foreground border border-card-border hover:bg-card-hover hover:border-card-border-focus"><div style="z-index: 3;"><div class="align-text-bottom items-center justify-center select-none my-0 rounded-md opacity-100 ml-1.5 flex bg-popover overflow-hidden border border-background z-3"><img class="size-[18px] m-0 rounded-md" src="./Configuring MCP in Cursor AI - Grok_files/image.png" alt=""></div></div><div style="z-index: 2;"><div class="align-text-bottom items-center justify-center select-none my-0 rounded-md opacity-100 flex bg-popover overflow-hidden border border-background z-2 -ml-2"><img class="size-[18px] m-0 rounded-md" src="./Configuring MCP in Cursor AI - Grok_files/image(1).png" alt=""></div></div><div style="z-index: 1;"><div class="align-text-bottom items-center justify-center select-none my-0 rounded-md opacity-100 flex bg-popover overflow-hidden border border-background z-1 -ml-2"><img class="size-[18px] m-0 rounded-md" src="./Configuring MCP in Cursor AI - Grok_files/image(2).png" alt=""></div></div><div class="truncate ml-2">25个网页</div></div></div><p class="break-words" style="white-space: pre-wrap;">To configure Multi-Currency Pricing (MCP) on Cursor, it seems there might be some confusion in your question. Based on the context available, "MCP" in relation to Cursor likely refers to "Multi-Agent Control Plane" or a similar custom tool configuration within the Cursor AI code editor, rather than Multi-Currency Pricing, which is more common in e-commerce platforms. Cursor, developed by Anysphere, is an AI-powered code editor, and MCP in this context is often discussed in posts on X as a way to extend its functionality with custom tools or servers.</p>
<p class="break-words" style="white-space: pre-wrap;">Here’s a step-by-step guide to configure MCP in Cursor, based on current user sentiment and practices shared on X as of March 20, 2025:</p>
<h3>Steps to Configure MCP in Cursor</h3>
<ol class="marker:text-secondary">
<li class="break-words"><strong>Open Cursor Settings</strong>
<ul class="marker:text-secondary">
<li class="break-words">Launch the Cursor editor on your system (available for Windows, macOS, or Linux).</li>
<li class="break-words">Navigate to the settings menu. You can typically access this by clicking the gear icon or going to <span class="text-sm px-1 rounded-sm !font-mono bg-sunset/10 text-rust dark:bg-dawn/10 dark:text-dawn">File &gt; Settings</span> (similar to VS Code, which Cursor is based on).</li>
</ul>
</li>
<li class="break-words"><strong>Locate the MCP Configuration Section</strong>
<ul class="marker:text-secondary">
<li class="break-words">In the settings, look for a section labeled "MCP" or "Features" (as noted in posts on X). This might be under an advanced or experimental features tab, depending on your Cursor version (e.g., 0.46 or later, as mentioned in X posts).</li>
<li class="break-words">If you’re using a version like 0.46, you may also configure MCP via a file like <span class="text-sm px-1 rounded-sm !font-mono bg-sunset/10 text-rust dark:bg-dawn/10 dark:text-dawn">.cursor/mcp.json</span> in your project directory for sharable configurations.</li>
</ul>
</li>
<li class="break-words"><strong>Add a New MCP</strong>
<ul class="marker:text-secondary">
<li class="break-words">Click on an option like "Add MCP" or "Add Custom Tool."</li>
<li class="break-words">You’ll need to specify the type of MCP. Common options include:
<ul class="marker:text-secondary">
<li class="break-words"><strong>Command</strong>: Runs a local script or command.</li>
<li class="break-words"><strong>Server</strong>: Connects to an external MCP server via a URL (e.g., an SSE URL from a service like Composio).</li>
</ul>
</li>
</ul>
</li>
<li class="break-words"><strong>Configure the MCP Details</strong>
<ul class="marker:text-secondary">
<li class="break-words"><strong>For a Command-Based MCP</strong>:
<ul class="marker:text-secondary">
<li class="break-words">Enter a command to execute. For example, as shared on X:
<div class="not-prose"><div class="relative [&amp;_div+div]:!mt-0 mt-3 mb-3 -mx-4 -mr-2 @md:-mr-4"><div class="flex flex-row px-4 py-2 h-10 items-center rounded-t-xl bg-popover"><span class="font-mono text-xs">text</span></div><div class="sticky right-2 z-10 @[1280px]/mainview:z-40 @[1280px]/mainview:top-10 top-28"><div class="absolute bottom-1 right-1 flex flex-row gap-0.5"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary disabled:hover:text-secondary disabled:hover:bg-inherit bg-popover dark:hover:bg-neutral-600 hover:bg-neutral-200 h-8 rounded-lg px-3 text-xs" type="button"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevrons-down-up size-4"><path d="m7 20 5-5 5 5"></path><path d="m7 4 5 5 5-5"></path></svg><span class="hidden @sm:block">收起</span></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary disabled:hover:text-secondary disabled:hover:bg-inherit bg-popover dark:hover:bg-neutral-600 hover:bg-neutral-200 h-8 rounded-lg px-3 text-xs" type="button"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-wrap-text size-4"><line x1="3" x2="21" y1="6" y2="6"></line><path d="M3 12h15a3 3 0 1 1 0 6h-4"></path><polyline points="16 16 14 18 16 20"></polyline><line x1="3" x2="10" y1="18" y2="18"></line></svg><span class="hidden @sm:block">自动换行</span></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary disabled:hover:text-secondary disabled:hover:bg-inherit bg-popover dark:hover:bg-neutral-600 hover:bg-neutral-200 h-8 rounded-lg px-3 text-xs" type="button"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-4"><rect x="3" y="8" width="13" height="13" rx="4" stroke="currentColor"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13 2.00004L12.8842 2.00002C12.0666 1.99982 11.5094 1.99968 11.0246 2.09611C9.92585 2.31466 8.95982 2.88816 8.25008 3.69274C7.90896 4.07944 7.62676 4.51983 7.41722 5.00004H9.76392C10.189 4.52493 10.7628 4.18736 11.4147 4.05768C11.6802 4.00488 12.0228 4.00004 13 4.00004H14.6C15.7366 4.00004 16.5289 4.00081 17.1458 4.05121C17.7509 4.10066 18.0986 4.19283 18.362 4.32702C18.9265 4.61464 19.3854 5.07358 19.673 5.63807C19.8072 5.90142 19.8994 6.24911 19.9488 6.85428C19.9992 7.47112 20 8.26343 20 9.40004V11C20 11.9773 19.9952 12.3199 19.9424 12.5853C19.8127 13.2373 19.4748 13.8114 19 14.2361V16.5829C20.4795 15.9374 21.5804 14.602 21.9039 12.9755C22.0004 12.4907 22.0002 11.9334 22 11.1158L22 11V9.40004V9.35725C22 8.27346 22 7.3993 21.9422 6.69141C21.8826 5.96256 21.7568 5.32238 21.455 4.73008C20.9757 3.78927 20.2108 3.02437 19.27 2.545C18.6777 2.24322 18.0375 2.1174 17.3086 2.05785C16.6007 2.00002 15.7266 2.00003 14.6428 2.00004L14.6 2.00004H13Z" fill="currentColor"></path></svg><span class="hidden @sm:block">复制</span></button></div></div><div style="display: block; overflow-x: auto; padding: 16px; color: black; background: var(--foreground); border-radius: 0px 0px 12px 12px; margin-top: 0px; font-size: 0.9em; line-height: 1.5em;"><code style="white-space: pre; line-height: inherit; font-size: inherit;"><span>env ELEVEN_LABS_API_KEY="your_key" node "absolute_path_to_index.js"</span></code></div></div></div>
Replace <span class="text-sm px-1 rounded-sm !font-mono bg-sunset/10 text-rust dark:bg-dawn/10 dark:text-dawn">"your_key"</span> with your API key and provide the full path to your script.</li>
</ul>
</li>
<li class="break-words"><strong>For a Server-Based MCP</strong>:
<ul class="marker:text-secondary">
<li class="break-words">Obtain an SSE (Server-Sent Events) URL from a provider (e.g., Composio’s MCP directory for tools like Google Docs integration).</li>
<li class="break-words">Paste the URL into the provided field.</li>
</ul>
</li>
<li class="break-words">Add any required authentication (e.g., API keys) if prompted.</li>
</ul>
</li>
<li class="break-words"><strong>Save the Configuration</strong>
<ul class="marker:text-secondary">
<li class="break-words">Save your settings. If using <span class="text-sm px-1 rounded-sm !font-mono bg-sunset/10 text-rust dark:bg-dawn/10 dark:text-dawn">.cursor/mcp.json</span>, edit the JSON file directly with your configuration (e.g., command or server details) and save it in your project root.</li>
</ul>
</li>
<li class="break-words"><strong>Test the MCP in Composer</strong>
<ul class="marker:text-secondary">
<li class="break-words">Open Cursor’s Composer mode (a feature for AI-assisted coding).</li>
<li class="break-words">Switch to "Agent Mode" if available, as suggested in X posts.</li>
<li class="break-words">Type a prompt related to your MCP (e.g., "Generate a PRD in Google Docs" if using a Google Docs MCP).</li>
<li class="break-words">Cursor will execute the MCP tool based on your configuration and prompt.</li>
</ul>
</li>
<li class="break-words"><strong>Share or Refine (Optional)</strong>
<ul class="marker:text-secondary">
<li class="break-words">If using <span class="text-sm px-1 rounded-sm !font-mono bg-sunset/10 text-rust dark:bg-dawn/10 dark:text-dawn">.cursor/mcp.json</span>, you can share this file with teammates to replicate your setup.</li>
<li class="break-words">Test and tweak the configuration as needed based on the output.</li>
</ul>
</li>
</ol>
<h3>Example Use Cases from X</h3>
<ul class="marker:text-secondary">
<li class="break-words"><strong>Google Docs Integration</strong>: Users have set up MCP to draft PRDs in Google Docs by linking to a Composio SSE URL.</li>
<li class="break-words"><strong>Custom Scripts</strong>: Running Node.js scripts with API keys (e.g., Eleven Labs) to extend functionality.</li>
<li class="break-words"><strong>Workflow Boosts</strong>: MCPs for reading browser logs, generating UI assets via Replicate, or interfacing with Supabase/Figma.</li>
</ul>
<h3>Notes</h3>
<ul class="marker:text-secondary">
<li class="break-words"><strong>Version Dependency</strong>: Some features (e.g., <span class="text-sm px-1 rounded-sm !font-mono bg-sunset/10 text-rust dark:bg-dawn/10 dark:text-dawn">.cursor/mcp.json</span>) are specific to newer versions like 0.46, released before March 20, 2025, per X posts.</li>
<li class="break-words"><strong>Resources</strong>: Check Cursor’s official documentation (docs.cursor.com) or community forums for the latest MCP server options. X posts also mention a Composio MCP directory for pre-built tools.</li>
<li class="break-words"><strong>Troubleshooting</strong>: Ensure API keys or URLs are valid, and paths are absolute if using local scripts.</li>
</ul>
<p class="break-words" style="white-space: pre-wrap;">Since "Multi-Currency Pricing" doesn’t align with Cursor’s purpose, I’ve assumed you meant MCP as a custom tool feature. If you intended something else, please clarify! This guide reflects the most practical interpretation based on available data.</p></div><div class="order-first sticky hidden @md/mainview:block @xl/mainview:top-0 top-12 h-0 opacity-0 group-focus-within:opacity-100 group-hover:opacity-100 -mt-2 -ml-[36px] @lg/mainview:-ml-12"><div class="min-h-7 py-4 flex flex-col gap-[2px]"></div></div><div class="flex items-center gap-[2px] w-max opacity-0 group-focus-within:opacity-100 group-hover:opacity-100 transition-opacity rounded-lg text-xs bg-background pb-2 px-2 start-0 md:start-3 -ml-4 group-last-of-type:opacity-100" style="bottom: 128px;"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary hover:bg-button-ghost-hover disabled:hover:text-secondary disabled:hover:bg-inherit h-8 w-8 rounded-full" type="button" aria-label="重新生成" data-state="closed"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-4"><path d="M4 20V15H4.31241M4.31241 15H9M4.31241 15C5.51251 18.073 8.50203 20.25 12 20.25C15.8582 20.25 19.0978 17.6016 20 14.0236M20 4V9H19.6876M19.6876 9H15M19.6876 9C18.4875 5.92698 15.498 3.75 12 3.75C8.14184 3.75 4.90224 6.3984 4 9.9764" stroke="currentColor"></path></svg></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary hover:bg-button-ghost-hover disabled:hover:text-secondary disabled:hover:bg-inherit h-8 w-8 rounded-full" type="button" data-state="closed"><span style="opacity: 1; transform: none;"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-4"><rect x="3" y="8" width="13" height="13" rx="4" stroke="currentColor"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13 2.00004L12.8842 2.00002C12.0666 1.99982 11.5094 1.99968 11.0246 2.09611C9.92585 2.31466 8.95982 2.88816 8.25008 3.69274C7.90896 4.07944 7.62676 4.51983 7.41722 5.00004H9.76392C10.189 4.52493 10.7628 4.18736 11.4147 4.05768C11.6802 4.00488 12.0228 4.00004 13 4.00004H14.6C15.7366 4.00004 16.5289 4.00081 17.1458 4.05121C17.7509 4.10066 18.0986 4.19283 18.362 4.32702C18.9265 4.61464 19.3854 5.07358 19.673 5.63807C19.8072 5.90142 19.8994 6.24911 19.9488 6.85428C19.9992 7.47112 20 8.26343 20 9.40004V11C20 11.9773 19.9952 12.3199 19.9424 12.5853C19.8127 13.2373 19.4748 13.8114 19 14.2361V16.5829C20.4795 15.9374 21.5804 14.602 21.9039 12.9755C22.0004 12.4907 22.0002 11.9334 22 11.1158L22 11V9.40004V9.35725C22 8.27346 22 7.3993 21.9422 6.69141C21.8826 5.96256 21.7568 5.32238 21.455 4.73008C20.9757 3.78927 20.2108 3.02437 19.27 2.545C18.6777 2.24322 18.0375 2.1174 17.3086 2.05785C16.6007 2.00002 15.7266 2.00003 14.6428 2.00004L14.6 2.00004H13Z" fill="currentColor"></path></svg></span></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary hover:bg-button-ghost-hover disabled:hover:text-secondary disabled:hover:bg-inherit h-8 w-8 rounded-full" type="button" aria-label="分享对话" data-state="closed"><span style="opacity: 1; transform: none;"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-4"><path d="M6.99609 9L11.9961 4L16.9961 9M6.99609 9M12 16V4" stroke="currentColor"></path><path d="M4 15V16C4 18.2091 5.79086 20 8 20H16C18.2091 20 20 18.2091 20 16V15" stroke="currentColor"></path></svg></span></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary hover:bg-button-ghost-hover disabled:hover:text-secondary disabled:hover:bg-inherit h-8 w-8 rounded-full" type="button" data-state="closed"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-4"><path d="M7 20H3V9H5.5C6.32843 9 7 9.67157 7 10.5V20ZM7 20H16.2062C17.8403 20 19.257 18.8692 19.6192 17.2757L20.8059 12.0541C21.1614 10.4896 19.9724 9 18.3681 9H14.5L15.1142 5.31454C15.3162 4.10294 14.3818 3 13.1535 3C12.4402 3 11.7816 3.38222 11.4277 4.00155L8.43188 9.24421C8.16482 9.71157 7.6678 10 7.12952 10H6.91465" stroke="currentColor"></path></svg></button><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-secondary hover:text-primary hover:bg-button-ghost-hover disabled:hover:text-secondary disabled:hover:bg-inherit h-8 w-8 rounded-full" type="button" data-state="closed"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-4"><path d="M7 4H3V15H5.5C6.32843 15 7 14.3284 7 13.5V4ZM7 4H16.2062C17.8403 4 19.257 5.13082 19.6192 6.72433L20.8059 11.9459C21.1614 13.5104 19.9724 15 18.3681 15H14.5L15.1142 18.6855C15.3162 19.8971 14.3818 21 13.1535 21C12.4402 21 11.7816 20.6178 11.4277 19.9984L8.43188 14.7558C8.16482 14.2884 7.6678 14 7.12952 14H6.91465" stroke="currentColor"></path></svg></button></div><div style="height: 1px; position: absolute; bottom: 0px; z-index: 1000;"></div></div></div><div style="padding-bottom: 144px; width: 100%;"></div></div></div></div><div class="absolute bottom-0 mx-auto inset-x-0 max-w-[50rem] z-50"><div class="relative z-40 flex flex-col items-center w-full"><div class="relative w-full px-2 pb-3 sm:pb-4"><form class="bottom-0 w-full text-base flex flex-col gap-2 items-center justify-center relative z-10"><div class="flex flex-row gap-2 justify-center w-full relative"><input class="hidden" multiple="" type="file" name="files"><div class="query-bar group bg-input-background duration-100 relative w-full max-w-[50rem] ring-1 ring-input-border ring-inset overflow-hidden @container/input hover:ring-card-border-focus hover:bg-input-background-hover focus-within:ring-1 focus-within:ring-input-border-focus hover:focus-within:ring-input-border-focus pb-12 px-2 @[480px]/input:px-3 rounded-3xl shadow shadow-input-border/10"><div class="w-full flex-row gap-2 mt-3 whitespace-nowrap flex-wrap hidden will-change-[mask-image] @sm:[mask-image:none] [mask-image:linear-gradient(to_right,transparent_0,black_0px,black_calc(100%_-_40px),transparent_100%)]" style="opacity: 1; transform: none;"></div><div class="relative z-10"><span class="absolute px-2 @[480px]/input:px-3 py-5 text-secondary pointer-events-none">需要Grok帮什么忙？</span><textarea dir="auto" aria-label="向Grok提任何问题" class="w-full px-2 @[480px]/input:px-3 pt-5 mb-5 bg-transparent focus:outline-none text-primary align-bottom" style="resize: none; height: 44px !important;"></textarea></div><div class="flex gap-1.5 absolute inset-x-0 bottom-0 border-2 border-transparent p-2 @[480px]/input:p-3 max-w-full"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 h-9 rounded-full py-2 relative px-2 transition-all duration-150 bg-transparent border w-9 aspect-square border-toggle-border hover:border-input-button-border-hover text-secondary hover:text-primary hover:bg-toggle-hover" type="button" aria-label="请登录以添加附件" disabled="" tabindex="0" data-state="closed"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] "><path d="M10 9V15C10 16.1046 10.8954 17 12 17V17C13.1046 17 14 16.1046 14 15V7C14 4.79086 12.2091 3 10 3V3C7.79086 3 6 4.79086 6 7V15C6 18.3137 8.68629 21 12 21V21C15.3137 21 18 18.3137 18 15V8" stroke="currentColor"></path></svg></button><div class=" flex grow gap-1.5 max-w-full" style="transform: none; opacity: 1;"><div class="grow flex gap-1.5 max-w-full"><div class="flex border rounded-full items-center max-h-[36px] box-border transition-colors duration-100 relative overflow-hidden border-toggle-border hover:border-input-button-border-hover"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-primary h-9 rounded-full px-3.5 py-2 group/ds-toggle transition-colors duration-100 focus-visible:ring-transparent box-border relative overflow-hidden rounded-r-none pr-3 bg-transparent hover:bg-toggle-hover focus-visible:bg-toggle-hover" type="button" tabindex="0" aria-pressed="false" aria-label="DeepSearch" data-state="closed"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] text-secondary group-hover/ds-toggle:text-primary"><path d="M19.2987 8.84667C15.3929 1.86808 5.44409 5.76837 7.08971 11.9099C8.01826 15.3753 12.8142 14.8641 13.2764 12.8592C13.6241 11.3504 10.2964 12.3528 10.644 10.844C11.1063 8.839 15.9022 8.32774 16.8307 11.793C18.5527 18.2196 7.86594 22.4049 4.71987 15.2225" stroke-width="5" stroke-linecap="round" class="stroke-black/10 dark:stroke-white/20 transition-all duration-200 origin-center opacity-0 scale-0"></path><path d="M2 13.8236C4.5 22.6927 18 21.3284 18 14.0536C18 9.94886 11.9426 9.0936 10.7153 11.1725C9.79198 12.737 14.208 12.6146 13.2847 14.1791C12.0574 16.2581 6 15.4029 6 11.2982C6 3.68585 20.5 2.2251 22 11.0945" stroke="currentColor" class="transition-transform duration-200 eas-out origin-center rotate-0"></path></svg><span>DeepSearch</span></button><div class="h-4 w-[1px] bg-toggle-border focus:outline-none" tabindex="-1"></div><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-primary h-9 rounded-full px-3.5 py-2 transition-colors duration-100 relative overflow-hidden focus-visible:ring-transparent rounded-l-none pl-2 pr-3 bg-transparent hover:bg-toggle-hover focus-visible:bg-toggle-hover" type="button" id="radix-:r6g:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] text-secondary"><path d="M6 9L12 15L18 9" stroke="currentColor" stroke-linecap="square"></path></svg></button></div><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-primary h-9 rounded-full px-3.5 py-2 group/think-toggle transition-colors duration-100 relative overflow-hidden border bg-transparent border-toggle-border hover:border-input-button-border-hover hover:bg-toggle-hover" type="button" tabindex="0" aria-pressed="false" aria-label="Think" data-state="closed"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] group-hover/think-toggle:text-primary text-secondary"><path d="M19 9C19 12.866 15.866 17 12 17C8.13398 17 4.99997 12.866 4.99997 9C4.99997 5.13401 8.13398 3 12 3C15.866 3 19 5.13401 19 9Z" class="fill-yellow-100 dark:fill-yellow-400/40 origin-center transition-all duration-100 scale-0 opacity-0"></path><path d="M15 16.1378L14.487 15.2794L14 15.5705V16.1378H15ZM8.99997 16.1378H9.99997V15.5705L9.51293 15.2794L8.99997 16.1378ZM18 9C18 11.4496 16.5421 14.0513 14.487 15.2794L15.5129 16.9963C18.1877 15.3979 20 12.1352 20 9H18ZM12 4C13.7598 4 15.2728 4.48657 16.3238 5.33011C17.3509 6.15455 18 7.36618 18 9H20C20 6.76783 19.082 4.97946 17.5757 3.77039C16.0931 2.58044 14.1061 2 12 2V4ZM5.99997 9C5.99997 7.36618 6.64903 6.15455 7.67617 5.33011C8.72714 4.48657 10.2401 4 12 4V2C9.89382 2 7.90681 2.58044 6.42427 3.77039C4.91791 4.97946 3.99997 6.76783 3.99997 9H5.99997ZM9.51293 15.2794C7.4578 14.0513 5.99997 11.4496 5.99997 9H3.99997C3.99997 12.1352 5.81225 15.3979 8.48701 16.9963L9.51293 15.2794ZM9.99997 19.5001V16.1378H7.99997V19.5001H9.99997ZM10.5 20.0001C10.2238 20.0001 9.99997 19.7763 9.99997 19.5001H7.99997C7.99997 20.8808 9.11926 22.0001 10.5 22.0001V20.0001ZM13.5 20.0001H10.5V22.0001H13.5V20.0001ZM14 19.5001C14 19.7763 13.7761 20.0001 13.5 20.0001V22.0001C14.8807 22.0001 16 20.8808 16 19.5001H14ZM14 16.1378V19.5001H16V16.1378H14Z" fill="currentColor"></path><path d="M9 16.0001H15" stroke="currentColor"></path><path d="M12 16V12" stroke="currentColor" stroke-linecap="square"></path><g><path d="M20 7L19 8" stroke="currentColor" stroke-linecap="round" class="transition-all duration-100 ease-in-out translate-x-0 translate-y-0 opacity-0"></path><path d="M20 9L19 8" stroke="currentColor" stroke-linecap="round" class="transition-all duration-100 ease-in-out translate-x-0 translate-y-0 opacity-0"></path><path d="M4 7L5 8" stroke="currentColor" stroke-linecap="round" class="transition-all duration-100 ease-in-out translate-x-0 translate-y-0 opacity-0"></path><path d="M4 9L5 8" stroke="currentColor" stroke-linecap="round" class="transition-all duration-100 ease-in-out translate-x-0 translate-y-0 opacity-0"></path></g></svg><span>Think</span></button></div><div class="flex items-center"><button class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-50 disabled:cursor-default [&amp;_svg]:pointer-events-none [&amp;_svg]:shrink-0 [&amp;_svg]:-mx-0.5 text-primary hover:bg-button-ghost-hover rounded-full px-3.5 py-2 flex-row pl-3 pr-2.5 h-9 sm:px-3 border border-button-outline-border sm:border-0" type="button" id="radix-:r2v:" aria-haspopup="menu" aria-expanded="false" data-state="closed"><span class="inline-block text-primary text-xs @[400px]/input:text-sm">Grok 3</span><svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] size-3 sm:size-4 text-secondary transition-transform false"><path d="M6 9L12 15L18 9" stroke="currentColor" stroke-linecap="square"></path></svg></button></div></div><div class="ml-auto flex flex-row items-end gap-1"><button class="group flex flex-col justify-center rounded-full focus:outline-none focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring" type="submit" aria-label="提交" disabled="" style="opacity: 1;"><div class="h-9 relative aspect-square flex flex-col items-center justify-center rounded-full ring-inset before:absolute before:inset-0 before:rounded-full before:bg-primary before:ring-0 before:transition-all duration-500 bg-button-secondary text-secondary before:[clip-path:circle(0%_at_50%_50%)] ring-0"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="stroke-[2] relative"><path d="M5 11L12 4M12 4L19 11M12 4V21" stroke="currentColor"></path></svg></div></button></div></div></div></div></form><div class="absolute bottom-0 w-[calc(100%-1rem)] h-full rounded-t-[40px] bg-background"></div></div></div></div></div></main></div></div><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section><textarea tabindex="-1" aria-hidden="true" style="min-height: 0px !important; max-height: none !important; height: 0px !important; visibility: hidden !important; overflow: hidden !important; position: absolute !important; z-index: -1000 !important; top: 0px !important; right: 0px !important; display: block !important; border-width: 0px; box-sizing: border-box; font-family: __Inter_2b114f, __Inter_Fallback_2b114f; font-size: 16px; font-style: normal; font-weight: 400; letter-spacing: -0.1px; line-height: 24px; padding: 20px 12px 0px; tab-size: 4; text-indent: 0px; text-rendering: auto; text-transform: none; width: 768px; word-break: normal; word-spacing: 0px; scrollbar-gutter: auto;"></textarea></body></html>