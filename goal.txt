1. 用户界面与对话流程 (全部页面根据地域需要中英文版, 或者右上角可选中英文)
a. 用户输入框与交互设计
对话框设计：用户进入网站后，首先看到的是一个对话窗口，窗口顶部显示网站的名称和简短的欢迎信息。例如： 
o"欢迎来到钢铁智联！给STEELNET发送消息 请输入你想转换的钢材数据，或者点击上传文件（csv文件）。" 暂时不上传文件
对话气泡： 
o用户的输入和AI的输出都以对话气泡的形式展示，模拟即时通讯的体验。（可以和gpt页面完全一致）
o用户输入的内容右对齐，AI的回答左对齐，保持清晰和直观。
单位选择器： 
o提供一个单位选择下拉菜单或者切换按钮, 用户可以选择单位转换方向，英制转成公制or公制转成英制，明确转换方向，在接下来的对话中，所有转换都会基于此方向进行。设立按键 A单位转换(有下拉互转切换)和B制表 , 位置类似替换DS的R1和联网搜索选项
o如果用户未选择单位，AI将提示用户选择单位并提供预设选项。
输入格式支持： 
o用户可以以不同格式输入数据
例如：钢材宽度 10英寸，长度 5英尺，厚度 2mm
10 inch，5 inch，2mm
10”,5 inch,2mm
oAI能够识别并提取有效数据，即使用户使用不同的格式，AI也能正确理解。
b. 对话流程细节
欢迎对话： 
oAI引导用户：“您好！请告诉我您想转换的数据，输入‘help’可以查看帮助指导信息。” 
o我觉得用户应该不太会这么写, 懂钢铁一般都很专业, 直接请用户整理数据, 比如 0.75” x 10” x 5’ 
数据识别： 
oAI解析数据并回复：“宽度254mm，长度1524mm，厚度2mm” ai回复格式保持不变，直接把数据部分转换掉。对应上面,直接把用户的数字部分转换掉好了, 钢铁用户应该能看懂,目前只为钢铁客户服务.
转换输出： 
o输出内容可以通过对话框直接显示。比如： 
o转换完成！以下是您的转换结果：
o| Steel Type | Length (mm) | Width (mm) | Thickness (mm) | Weight (kg) |
o|------------|-------------|------------|----------------|-------------|
o| Steel A    | 1524        | 254        | 2              | 5.23        |
o表格样式：以简洁的文本格式显示表格，字段明确，单位标注清晰。
个性化反馈： 
o” 先不设置反馈. 个性化内容可以做个隐藏, 设个按键, 同DS GPT的计算说明一起不显示在页面中, 但可以点击展开说明. 
c. 交互增强功能
自然语言理解：用户可以以各种自然语言表述输入数据，如：“我的钢材宽度10英寸，长度5英尺，厚度2毫米，帮我转换成公制。” AI会理解这些多种表达方式。
多轮对话： 
o如果数据不完全，AI会与用户展开多轮对话。例如，用户输入钢材长度和宽度，但没有提供厚度，AI会提示：“请提供钢材厚度，或者我们将默认5mm。”
调整数据： 
o如果用户在对话中需要修改数据，AI会允许用户输入修改内容并实时更新转换结果。例如：“请将钢材厚度改为3mm。”

2. 转换与输出功能
a. 单位转换算法
单位转换规则： 
o钢材相关的常见单位包括：长度（英寸/毫米）、宽度（英寸/毫米）、厚度（英寸/毫米）、重量（磅/千克）、厚度单位（GA/毫米）
o转换公式（例如，英寸转毫米：1 inch = 25.4 mm；英尺转米：1 foot = 0.3048 meters）。
实时转换： 
o用户输入数据后，AI根据用户选择的单位和钢材特性（如钢种）进行实时转换，输出结果包括： 
长度、宽度、厚度：英制转换为公制或反向转换。
重量：如果用户提供了钢材的规格，AI将根据密度或其他公式计算出重量。
b. 结果展示-- 结果下方需要添加几个功能键: 复制/赞/错误/建议. 
表格展示： 
o将转换结果直接展示在对话框中，表格包含以下内容： 
o| Steel Type | Length (mm) | Width (mm) | Thickness (mm) | Weight (kg) |
o|------------|-------------|------------|----------------|-------------|
o| Steel A    | 1524        | 254        | 2              | 5.23        |
格式化： 
o表格格式清晰，字段对齐，单位标注（如mm、kg）准确，用户可以直接复制
o不需要任何解释和任何过程。
c. 导出与邮箱发送
一键发送到邮箱功能： 
o在转换结果下方，提供“发送到邮箱”按钮，用户点击后，AI将询问输入邮箱地址：“请输入您的邮箱地址，我们将把转换结果发送给您。”
o邮件发送后，用户将收到包含转换结果和表格的邮件，方便保存和后续参考。

3. 用户管理与邮件服务
a. 邮箱采集与用户管理
注册与订阅： 
注册就先注册用户信息,(输入邮件-->密码-->发送邮件验证码-->名字/公司名/国家) --过程同GPT.
       不用其他问题了 
o暂时没有新闻
b. 邮件服务
邮件模板设计： 
o为邮件发送设计清晰的模板，包含用户的原始数据及转换结果的表格+钢材行业新闻、市场分析等简单介绍信息。
定期新闻推送： 
o用户订阅后，可定期通过邮件接收钢铁行业新闻、原料价格变动等。
o允许用户选择是否接收不同类型的新闻（如原材料价格、市场趋势等）。
c. 自动化邮件发送
转换完成后邮件发送： 
o一旦用户请求“发送到邮箱”，系统会自动生成邮件并将转换结果发送给用户，同时记录用户的邮箱地址。
邮件日志： 
o系统会记录每次邮件发送的状态，确保邮件顺利到达用户邮箱，避免丢失或滞后。

目前功能全都免费, 预先设立两种用户数据库:  A.付费用户 B.免费用户

所有用户输入的数据需保留,
结果呈现最简单表格形式, (隐藏不必要的步骤, 但可展开,说明解释)
尽量流程简短,迅速展现结果


最终项目要部署到阿里云的ecs上，并使用阿里云的数据库


 