#!/usr/bin/env python3
"""
Analyze the corrupted table output to understand parsing issues
"""

def analyze_corrupted_table():
    """Analyze the corrupted table data you provided"""
    
    print("🔍 Analyzing Corrupted Table Output")
    print("=" * 60)
    
    # Based on the corrupted table you showed, let me reconstruct what the LLM might have returned
    # The table shows severe column misalignment, suggesting the LLM response was malformed
    
    # Possible corrupted LLM response that could cause this issue:
    corrupted_responses = [
        # Case 1: Missing pipes or malformed delimiters
        """<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.343in(+/-0.005in) x COIL|7190#|0.38mm(+/-0.04mm) x 59.51mm(+/-0.13mm) x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.406in(+/-0.005in) x COIL|8061#|0.38mm(+/-0.04mm) x 61.11mm(+/-0.13mm) x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 16.50in(+/-0.005in) x COIL|12550#|0.38mm(+/-0.04mm) x 419.1mm(+/-0.13mm) x COIL|5692.7 kg|BA Finish 430 BA NO PI
005|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 47.438in(+/-0.03125in) x COIL|57655#|0.38mm(+/-0.04mm) x 120(+/-0.79mm) x52.6 kg|BA Finish
430 BA NO PI|0.015in(+/-5in) x in(+/-0.03125in)|118001#|0.38mm(+/-0. x 1193.8mm(+/-mm) x COIL|53524.4 kg|BA|S/S 430 BA NO PI
008|S/S B NO PI|0.015in(+/-0.001 x 16.938in(+/-0in) x COIL|5000#|38mm(+/-0.04mm) x.23mm(+/-0.13mm)IL|2268.0 kg|2B009
010|S/S 430B NO PI|0.016in(+/-0015in) x 35.50in0.03125in)IL|122083#|0.41mm(+/-0.) x 901.7/-0.79mm) x COIL|.9 kg|2B Finish
011|S/S 430||||||
</table_stream>""",
        
        # Case 2: Pipes within content causing parsing issues
        """<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.343in(+/-0.005in) x COIL|7190#|0.38mm(+/-0.04mm) x 59.51mm(+/-0.13mm) x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.406in(+/-0.005in) x COIL|8061#|0.38mm(+/-0.04mm) x 61.11mm(+/-0.13mm) x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 16.50in(+/-0.005in) x COIL|12550#|0.38mm(+/-0.04mm) x 419.1mm(+/-0.13mm) x COIL|5692.7 kg|BA Finish | 430 BA NO PI
005|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 47.438in(+/-0.03125in) x COIL|57655#|0.38mm(+/-0.04mm) x 120(+/-0.79mm) x|52.6 kg|BA Finish
430 BA NO PI|0.015in(+/-5in) x | in(+/-0.03125in)|118001#|0.38mm(+/-0. x 1193.8mm(+/-mm) x COIL|53524.4 kg|BA|S/S 430 BA NO PI
</table_stream>""",
        
        # Case 3: Incomplete or truncated response
        """<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.343in(+/-0.005in) x COIL|7190#|0.38mm(+/-0.04mm) x 59.51mm(+/-0.13mm) x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.406in(+/-0.005in) x COIL|8061#|0.38mm(+/-0.04mm) x 61.11mm(+/-0.13mm) x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 16.50in(+/-0.005in) x COIL|12550#|0.38mm(+/-0.04mm) x 419.1mm(+/-0.13mm) x COIL|5692.7 kg|BA Finish 430 BA NO PI
005|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 47.438in(+/-0.03125in) x COIL|57655#|0.38mm(+/-0.04mm) x 120(+/-0.79mm) x52.6 kg|BA Finish|
430 BA NO PI|0.015in(+/-5in) x in(+/-0.03125in)|118001#|0.38mm(+/-0. x 1193.8mm(+/-mm) x COIL|53524.4 kg|BA|S/S 430 BA NO PI
008|S/S B NO PI|0.015in(+/-0.001 x 16.938in(+/-0in) x COIL|5000#|38mm(+/-0.04mm) x.23mm(+/-0.13mm)IL|2268.0 kg|2B009
010|S/S 430B NO PI|0.016in(+/-0015in) x 35.50in0.03125in)IL|122083#|0.41mm(+/-0.) x 901.7/-0.79mm) x COIL|.9 kg|2B Finish
011|S/S 430
"""
    ]
    
    for i, response in enumerate(corrupted_responses, 1):
        print(f"\n🧪 Testing Corrupted Response Case {i}")
        print("-" * 50)
        
        # Extract table content
        import re
        match = re.search(r'<table_stream>(.*?)</table_stream>', response, re.DOTALL)
        if match:
            table_content = match.group(1).strip()
        else:
            table_content = response.strip()
        
        print("Raw table content:")
        print(repr(table_content))
        print()
        
        # Parse lines
        lines = [line.strip() for line in table_content.split('\n') if line.strip()]
        
        print(f"Lines found: {len(lines)}")
        
        if lines:
            # Parse header
            header_line = lines[0]
            print(f"Header line: {repr(header_line)}")
            
            if '|' in header_line:
                clean_header = re.sub(r'^\||\|$', '', header_line)
                headers = [h.strip() for h in clean_header.split('|')]
                print(f"Headers ({len(headers)}): {headers}")
                
                # Parse data rows
                data_rows = lines[1:]
                print(f"Data rows: {len(data_rows)}")
                
                issues_found = []
                
                for j, row in enumerate(data_rows):
                    print(f"\nRow {j+1}: {repr(row)}")
                    
                    if '|' in row:
                        clean_row = re.sub(r'^\||\|$', '', row)
                        cells = [c.strip() for c in clean_row.split('|')]
                        print(f"  Cells ({len(cells)}): {cells}")
                        
                        if len(cells) != len(headers):
                            issue = f"Row {j+1}: Expected {len(headers)} cells, got {len(cells)}"
                            issues_found.append(issue)
                            print(f"  ❌ {issue}")
                            
                            # Show the mismatch
                            for k, header in enumerate(headers):
                                cell_value = cells[k] if k < len(cells) else "MISSING"
                                print(f"    {header}: {cell_value}")
                        else:
                            print(f"  ✅ Column count matches")
                    else:
                        issues_found.append(f"Row {j+1}: No pipes found")
                        print(f"  ❌ No pipes found in row")
                
                print(f"\n📊 Issues Summary for Case {i}:")
                if issues_found:
                    for issue in issues_found:
                        print(f"  ❌ {issue}")
                else:
                    print("  ✅ No issues found")
            else:
                print("❌ Header line has no pipes")
        else:
            print("❌ No lines found")

def suggest_prompt_improvements():
    """Suggest improvements to the LLM prompt based on the analysis"""
    
    print("\n🔧 SUGGESTED PROMPT IMPROVEMENTS")
    print("=" * 60)
    
    improvements = [
        "1. Add explicit instruction to NOT use pipes (|) within cell content",
        "2. Add validation requirement: each row must have exactly 7 fields",
        "3. Add instruction to use alternative characters for measurements (e.g., 'x' instead of '|')",
        "4. Add explicit instruction to complete all rows properly",
        "5. Add instruction to avoid line breaks within cells",
        "6. Add instruction to use consistent formatting for all measurements",
        "7. Add validation instruction to check column count before outputting"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")
    
    print("\n📝 ENHANCED PROMPT SUGGESTION:")
    print("-" * 40)
    
    enhanced_prompt = """
表格输出 (制表功能) - 严格格式要求：
当数据有多项条目或需要整理成表格时，必须严格遵循以下格式：

<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.343in(+/-0.005in) x COIL|7190#|0.38mm(+/-0.04mm) x 59.51mm(+/-0.13mm) x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.406in(+/-0.005in) x COIL|8061#|0.38mm(+/-0.04mm) x 61.11mm(+/-0.13mm) x COIL|3656.7 kg|BA Finish
</table_stream>

关键格式规则 (必须严格遵守):
1. 每行必须有且仅有7个字段，用单个管道符(|)分隔
2. 绝对禁止在字段内容中使用管道符(|)
3. 使用 'x' 而不是 '|' 来表示尺寸 (例如: 2.343in x 16.50in)
4. 每行必须完整，不允许截断或不完整的行
5. 不允许在字段内容中换行
6. 每行输出前必须验证字段数量为7个
7. 如果字段为空，保留空白但保持管道符分隔结构
8. 所有数据必须在对应的正确列中，严禁列错位

输出前验证清单:
- 检查每行是否有7个字段
- 检查是否有管道符在字段内容中
- 检查所有行是否完整
- 检查列对齐是否正确
"""
    
    print(enhanced_prompt)

if __name__ == "__main__":
    print("🚀 Analyzing Corrupted Table Output")
    print("=" * 80)
    
    analyze_corrupted_table()
    suggest_prompt_improvements()
    
    print("\n" + "=" * 80)
    print("📋 ANALYSIS SUMMARY")
    print("=" * 80)
    
    print("🔍 Key Issues Identified:")
    print("  1. LLM is likely including pipes (|) within cell content")
    print("  2. Some rows are incomplete or truncated")
    print("  3. Column alignment is being corrupted during generation")
    print("  4. Field content is spilling into adjacent columns")
    
    print("\n💡 Recommended Actions:")
    print("  1. Update the LLM prompt with stricter format rules")
    print("  2. Add explicit prohibition of pipes in content")
    print("  3. Add validation requirements to the prompt")
    print("  4. Test with the enhanced prompt")
    
    print("\n🚀 Next Steps:")
    print("  1. Update backend/llm_prompt.txt with enhanced rules")
    print("  2. Test with real LLM responses")
    print("  3. Monitor for continued parsing issues")
    print("  4. Iterate until perfect column alignment is achieved")
