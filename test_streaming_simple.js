/**
 * Simple test to verify streaming API functionality
 */

async function testStreamingAPI() {
    console.log('🧪 Testing Streaming API');
    console.log('========================');
    
    try {
        const response = await fetch('http://localhost:8000/api/llm/stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: 'Convert 1 inch to mm',
                unit_system: 'metric',
                function: 'conversion'
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        console.log('✅ Streaming response started');
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedContent = '';
        let chunkCount = 0;

        while (true) {
            const { done, value } = await reader.read();
            
            if (done) {
                console.log('✅ Streaming completed');
                break;
            }

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n');
            
            for (const line of lines) {
                if (line.trim().startsWith('data: ')) {
                    const data = line.trim().substring(6);
                    
                    if (data.trim() === '[DONE]') {
                        console.log('✅ Received [DONE] signal');
                        break;
                    }
                    
                    try {
                        const parsed = JSON.parse(data);
                        chunkCount++;
                        
                        console.log(`📦 Chunk ${chunkCount}:`, {
                            type: parsed.type,
                            contentLength: parsed.content ? parsed.content.length : 0,
                            hasResult: !!parsed.result
                        });
                        
                        if (parsed.type === 'content') {
                            accumulatedContent += parsed.content || '';
                        } else if (parsed.type === 'completion') {
                            console.log('🎉 Completion received:', {
                                resultType: parsed.result ? typeof parsed.result : 'none',
                                convertedText: parsed.result?.converted_text ? 'present' : 'missing'
                            });
                            
                            if (parsed.result?.converted_text) {
                                console.log('📝 Final converted text:', parsed.result.converted_text.substring(0, 100) + '...');
                            }
                        }
                    } catch (parseError) {
                        console.warn('⚠️ Failed to parse chunk:', data.substring(0, 50));
                    }
                }
            }
        }
        
        console.log('\n📊 Summary:');
        console.log(`Total chunks: ${chunkCount}`);
        console.log(`Accumulated content length: ${accumulatedContent.length}`);
        console.log(`Content preview: ${accumulatedContent.substring(0, 100)}...`);
        
        return true;
        
    } catch (error) {
        console.error('❌ Streaming test failed:', error);
        return false;
    }
}

// Run the test
testStreamingAPI().then(success => {
    if (success) {
        console.log('\n🎉 Streaming API test completed successfully!');
    } else {
        console.log('\n❌ Streaming API test failed!');
    }
    process.exit(success ? 0 : 1);
});
