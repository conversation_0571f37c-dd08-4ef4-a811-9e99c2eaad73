<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Frontend Debug Test</h1>
    <p>This page tests the frontend table parsing functionality by making direct API calls.</p>

    <div class="test-section">
        <h2>API Test</h2>
        <button class="btn" onclick="testAPI()">Test Table API</button>
        <button class="btn" onclick="clearConsole()">Clear Console</button>
        
        <div id="status"></div>
        
        <h3>Console Output:</h3>
        <div class="console-output" id="console-output">
            Click "Test Table API" to start testing...
        </div>
    </div>

    <div class="test-section">
        <h2>Progressive Table Parser Test</h2>
        <button class="btn" onclick="testProgressiveParsing()">Test Progressive Parsing</button>
        
        <div id="parser-output"></div>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type.toUpperCase()}: ${args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ')}\n`;
            consoleOutput.textContent += message;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('error', ...args);
        };
        
        function clearConsole() {
            consoleOutput.textContent = '';
        }
        
        function setStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        // StreamingTableParser implementation (copied from frontend)
        class StreamingTableParser {
            constructor() {
                this.headers = [];
                this.rows = [];
                this.isHeaderParsed = false;
                this.buffer = '';
            }

            addChunk(chunk) {
                this.buffer += chunk;
                console.log('Adding chunk to parser:', chunk);
                
                // Look for table_stream tags
                const streamMatch = this.buffer.match(/<table_stream>([\s\S]*?)(?:<\/table_stream>|$)/);
                if (streamMatch) {
                    const tableContent = streamMatch[1];
                    console.log('Found table_stream content:', tableContent);
                    return this.parseStreamingContent(tableContent);
                }
                
                return { headers: this.headers, rows: this.rows, isValid: false };
            }

            parseStreamingContent(content) {
                const lines = content.split('\n').filter(line => line.trim());
                console.log('Parsing lines:', lines);
                
                if (lines.length === 0) {
                    return { headers: this.headers, rows: this.rows, isValid: false };
                }

                // Parse headers if not done yet
                if (!this.isHeaderParsed && lines.length > 0) {
                    this.headers = lines[0].split('|').map(h => h.trim()).filter(h => h.length > 0);
                    this.isHeaderParsed = true;
                    console.log('Headers parsed:', this.headers);
                }

                // Parse data rows (skip header line)
                const dataLines = lines.slice(1);
                this.rows = dataLines.map(line => {
                    const cells = line.split('|').map(c => c.trim());
                    const row = {};
                    this.headers.forEach((header, index) => {
                        row[header] = cells[index] || '';
                    });
                    return row;
                });

                const isValid = this.headers.length > 0;
                console.log('Parsed table:', { headers: this.headers, rows: this.rows, isValid });
                return { 
                    headers: this.headers, 
                    rows: this.rows, 
                    isValid,
                    error: isValid ? undefined : 'Incomplete table data'
                };
            }

            reset() {
                this.headers = [];
                this.rows = [];
                this.isHeaderParsed = false;
                this.buffer = '';
            }
        }

        async function testAPI() {
            try {
                setStatus('Testing API...', 'success');
                console.log('Starting API test...');
                
                const response = await fetch('http://localhost:8000/api/llm/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: 'Convert 0.015 inch x 2.343 inch to mm',
                        function: 'table',
                        unit_system: 'metric'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                console.log('Response received, reading stream...');
                setStatus('Reading stream...', 'success');
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullResponse = '';
                let chunkCount = 0;
                
                // Test progressive parsing
                const parser = new StreamingTableParser();
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    const chunk = decoder.decode(value, { stream: true });
                    fullResponse += chunk;
                    chunkCount++;
                    
                    console.log(`Chunk ${chunkCount}:`, JSON.stringify(chunk));
                    
                    // Test progressive parsing
                    const tableData = parser.addChunk(chunk);
                    if (tableData.isValid) {
                        console.log('Progressive table data:', tableData);
                    }
                }
                
                console.log('\n=== FULL RESPONSE ===');
                console.log(fullResponse);
                
                // Check what format the response is in
                if (fullResponse.includes('<table_stream>')) {
                    console.log('✓ Response contains <table_stream> format');
                    setStatus('✓ Found <table_stream> format!', 'success');
                } else if (fullResponse.includes('<table_data>')) {
                    console.log('✓ Response contains <table_data> format');
                    setStatus('✓ Found <table_data> format', 'success');
                } else if (fullResponse.includes('|') && fullResponse.includes('\n|')) {
                    console.log('✓ Response contains markdown table format');
                    setStatus('✓ Found markdown table format', 'success');
                } else {
                    console.log('✗ No table format detected');
                    setStatus('✗ No table format detected', 'error');
                }
                
            } catch (error) {
                console.error('Error testing API:', error);
                setStatus(`Error: ${error.message}`, 'error');
            }
        }

        function testProgressiveParsing() {
            const output = document.getElementById('parser-output');
            
            // Test data with streaming format
            const testData = `<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015" x 2.343" x COIL|7190#|0.38mm x 59.51mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015" x 2.406" x COIL|8061#|0.38mm x 61.11mm x COIL|3656.7 kg|BA Finish
</table_stream>`;

            console.log('Testing progressive parsing with test data...');
            
            const parser = new StreamingTableParser();
            const chunks = testData.split('');
            
            let html = '<h4>Progressive Parsing Test:</h4>';
            
            chunks.forEach((chunk, index) => {
                const result = parser.addChunk(chunk);
                if (result.isValid && result.headers.length > 0) {
                    html += `<p><strong>After chunk ${index + 1}:</strong> ${result.headers.length} headers, ${result.rows.length} rows</p>`;
                }
            });
            
            const finalResult = parser.addChunk('');
            html += `<h4>Final Result:</h4>`;
            html += `<p>Headers: ${JSON.stringify(finalResult.headers)}</p>`;
            html += `<p>Rows: ${finalResult.rows.length}</p>`;
            html += `<p>Valid: ${finalResult.isValid}</p>`;
            
            output.innerHTML = html;
            console.log('Progressive parsing test completed');
        }

        // Auto-run tests on page load
        window.onload = function() {
            console.log('Page loaded, ready for testing');
        };
    </script>
</body>
</html>
