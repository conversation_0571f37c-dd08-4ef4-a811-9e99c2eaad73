# Steel Unit Converter - Production Startup Guide

This guide provides instructions for starting and managing the Steel Unit Converter application in production mode.

## Quick Start

To start the application in production mode:

```bash
./start-prod.sh
```

This script will:
- Check for required dependencies (Python and Node.js)
- Kill any existing processes
- Start the backend with <PERSON><PERSON>
- B<PERSON> and start the frontend with Vite preview
- Save process IDs for management

## Command Line Options

The `start-prod.sh` script supports several command line options:

```bash
./start-prod.sh [OPTIONS]
```

### Options

- `--restart`: Stop existing processes and start new ones
- `--stop`: Stop all processes and exit
- `--status`: Check if services are running
- `--logs [COMP]`: Show logs (backend, frontend, or all)
- `--backend`: Only operate on backend
- `--frontend`: Only operate on frontend
- `--show-logs`: Show logs after starting
- `--help`: Show help message

### Examples

```bash
# Start or restart the application
./start-prod.sh --restart

# Stop the application
./start-prod.sh --stop

# Check status of services
./start-prod.sh --status

# Show backend logs
./start-prod.sh --logs backend

# Start only the backend
./start-prod.sh --backend

# Restart frontend and show logs
./start-prod.sh --restart --frontend --show-logs
```

## Features

### 1. Process Management

The script provides comprehensive process management:
- Automatically detects and kills existing processes
- Starts services with optimized production settings
- Saves process IDs for later management
- Provides status checking and log viewing

### 2. Backend Optimization

The backend is started with Gunicorn for production performance:
- Uses multiple workers and threads
- Configures max requests and timeouts
- Sets up proper logging
- Uses the uvicorn worker class for ASGI support

### 3. Frontend Optimization

The frontend is built and served for production:
- Skips TypeScript checking for reliable builds
- Uses 'serve' for reliable static file serving (with fallback to Vite preview)
- Configures proper host and port settings
- Provides detailed logs

### 4. Nginx Integration

The script automatically sets up Nginx as a reverse proxy if available:
- Creates and configures Nginx virtual host
- Sets up SSL certificates
- Configures proper proxy settings for both frontend and backend
- Provides access via domain name (steelnet.ai)

## Troubleshooting

### Application Not Starting

If the application fails to start, check the status and logs:

```bash
# Check status
./start-prod.sh --status

# View logs
./start-prod.sh --logs all
```

### Process Management Issues

If you're having issues with process management:

1. Stop all processes:

```bash
./start-prod.sh --stop
```

2. Check if any processes are still running:

```bash
ps aux | grep "uvicorn\|npm run preview"
```

3. Restart the application:

```bash
./start-prod.sh --restart
```

### Log Viewing

You can view logs for specific components:

```bash
# View backend logs
./start-prod.sh --logs backend

# View frontend logs
./start-prod.sh --logs frontend

# View all logs
./start-prod.sh --logs all
```

### Nginx Configuration Issues

If you're having issues with the Nginx configuration:

1. Check the Nginx configuration:

```bash
sudo nginx -t
```

2. Check the Nginx error logs:

```bash
sudo tail -f /var/log/nginx/error.log
```

3. Make sure the domain is properly configured:

```bash
# Check if the domain resolves to your server
ping steelnet.ai
```

4. Manually configure Nginx:

```bash
# Edit the Nginx configuration
sudo nano /etc/nginx/conf.d/steelnet.conf

# Restart Nginx
sudo systemctl restart nginx
```

### Domain Access Issues

If you can't access the application via the domain name:

1. Make sure the domain is properly configured in your DNS settings
2. Check if the domain resolves to your server's IP address
3. Verify that Nginx is properly configured and running
4. Check if your firewall allows traffic on ports 80 and 443

## Notes

- The script is designed to be similar to the development `start.sh` script but optimized for production
- The application will be accessible at:
  - Via Nginx: https://steelnet.ai (if configured)
  - Direct backend: http://localhost:8000
  - Direct frontend: http://localhost:3000
  - Direct API: http://localhost:8000/api
- The script automatically detects and configures Nginx if available
- For best results, make sure Nginx is installed and properly configured
