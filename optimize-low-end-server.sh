#!/bin/bash

# Low-End Cloud Server Optimization Script
# This script optimizes the Steel Unit Converter for deployment on low-end cloud servers

set -e

echo "🚀 Starting Low-End Server Optimization..."

# Function to check available memory
check_memory() {
    local available_memory
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        available_memory=$(free -m | awk '/^Mem:/{print $2}')
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        available_memory=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024)}')
    else
        echo "⚠️  Warning: Cannot determine memory on this OS"
        available_memory=2048  # Default assumption
    fi
    echo "$available_memory"
}

# Function to optimize backend
optimize_backend() {
    echo "🔧 Optimizing backend..."
    
    # Create optimized requirements.txt for production
    cat > backend/requirements-optimized.txt << 'EOF'
# Optimized requirements for low-end servers
fastapi==0.97.0
uvicorn[standard]==0.22.0
pydantic==1.10.9
python-dotenv==1.0.0
sqlalchemy==2.0.16
pymysql==1.1.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
email-validator==2.0.0
httpx
six

# Optional dependencies for production
# aliyun-python-sdk-core==2.13.36
# oss2==2.17.0
EOF

    # Create optimized gunicorn config
    cat > backend/gunicorn_optimized.conf.py << 'EOF'
# Optimized Gunicorn configuration for low-end servers
import multiprocessing
import os

# Calculate optimal workers based on available memory
def calculate_workers():
    try:
        # Get available memory in MB
        if os.path.exists('/proc/meminfo'):
            with open('/proc/meminfo', 'r') as f:
                for line in f:
                    if line.startswith('MemAvailable:'):
                        mem_mb = int(line.split()[1]) // 1024
                        break
                else:
                    mem_mb = 2048  # Default
        else:
            mem_mb = 2048  # Default for non-Linux systems
        
        # Reserve 512MB for system, each worker uses ~200MB
        if mem_mb < 1024:
            return 1
        elif mem_mb < 2048:
            return 2
        else:
            return min(4, max(1, (mem_mb - 512) // 200))
    except:
        return 1

# Server socket
bind = "0.0.0.0:8000"
backlog = 256

# Worker processes
workers = calculate_workers()
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 100
max_requests = 500
max_requests_jitter = 50

# Threads
threads = 4

# Timeout
timeout = 60
keepalive = 2

# Memory optimization
preload_app = True
worker_tmp_dir = "/tmp"

# Logging
accesslog = "logs/access.log"
errorlog = "logs/error.log"
loglevel = "info"

# Process naming
proc_name = "steel-converter-backend"

# Restart workers after this many requests
max_requests = 1000

# Restart workers after this many seconds
max_worker_age = 3600

# Graceful timeout
graceful_timeout = 30

# Keep-alive timeout
keepalive_timeout = 5

# Maximum size of HTTP request line
limit_request_line = 4094

# Maximum size of HTTP request header
limit_request_field_size = 8190

# Maximum number of header fields
limit_request_fields = 100
EOF

    echo "✅ Backend optimization completed"
}

# Function to optimize frontend
optimize_frontend() {
    echo "🔧 Optimizing frontend..."
    
    # Create optimized package.json scripts
    local package_json="frontend/package.json"
    if [[ -f "$package_json" ]]; then
        # Add low-memory build scripts
        local temp_file=$(mktemp)
        jq '.scripts |= . + {
            "build:low-memory": "NODE_OPTIONS=\"--max-old-space-size=512\" vite build --mode production",
            "build:minimal": "NODE_OPTIONS=\"--max-old-space-size=256\" vite build --mode production --minify=false",
            "build:ultra-low": "NODE_OPTIONS=\"--max-old-space-size=128\" vite build --mode production --minify=false --sourcemap=false"
        }' "$package_json" > "$temp_file" && mv "$temp_file" "$package_json"
    fi
    
    # Create optimized Vite config
    cat > frontend/vite.config.low-memory.js << 'EOF'
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  build: {
    // Optimize for low memory usage
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/icons-material'],
          utils: ['axios', 'uuid', 'date-fns']
        }
      }
    },
    chunkSizeWarningLimit: 500,
    assetsInlineLimit: 2048,
    sourcemap: false,
    reportCompressedSize: false,
  },
  server: {
    port: 3000,
    host: '0.0.0.0',
    hmr: {
      overlay: false
    }
  },
  optimizeDeps: {
    include: ['react', 'react-dom', '@mui/material', '@mui/icons-material']
  }
});
EOF

    echo "✅ Frontend optimization completed"
}

# Function to create optimized Docker configurations
create_docker_optimizations() {
    echo "🔧 Creating optimized Docker configurations..."
    
    # Create multi-stage optimized Dockerfile
    cat > Dockerfile.optimized << 'EOF'
# Multi-stage optimized Dockerfile for low-end servers
FROM node:18-alpine AS frontend-builder

# Set memory limits
ENV NODE_OPTIONS="--max-old-space-size=512"

WORKDIR /app/frontend

# Copy package files
COPY frontend/package*.json ./
RUN npm ci --only=production --no-audit --no-fund

# Copy source and build
COPY frontend/ ./
RUN npm run build:low-memory

# Python backend stage
FROM python:3.9-slim AS backend

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    default-libmysqlclient-dev \
    pkg-config && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app/backend

# Copy requirements and install
COPY backend/requirements-optimized.txt ./requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy backend code
COPY backend/ ./

# Copy built frontend
COPY --from=frontend-builder /app/frontend/dist ./static

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Start command
CMD ["gunicorn", "-c", "gunicorn_optimized.conf.py", "main:app"]
EOF

    # Create optimized docker-compose
    cat > docker-compose.optimized.yml << 'EOF'
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.optimized
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
      - PYTHON_ENV=production
    deploy:
      resources:
        limits:
          memory: 768M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
EOF

    echo "✅ Docker optimization completed"
}

# Function to create system optimization script
create_system_optimizations() {
    echo "🔧 Creating system optimization script..."
    
    cat > system-optimize.sh << 'EOF'
#!/bin/bash

# System-level optimizations for low-end servers
echo "🔧 Applying system optimizations..."

# Set memory optimization parameters
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux-specific optimizations
    
    # Reduce swappiness (prefer RAM over swap)
    echo "vm.swappiness=10" >> /etc/sysctl.conf
    
    # Optimize memory allocation
    echo "vm.overcommit_memory=1" >> /etc/sysctl.conf
    echo "vm.overcommit_ratio=50" >> /etc/sysctl.conf
    
    # Optimize TCP settings
    echo "net.core.rmem_max = 134217728" >> /etc/sysctl.conf
    echo "net.core.wmem_max = 134217728" >> /etc/sysctl.conf
    echo "net.core.netdev_max_backlog = 5000" >> /etc/sysctl.conf
    
    # Apply settings
    sysctl -p
    
    # Set up log rotation
    cat > /etc/logrotate.d/steel-converter << 'LOGROTATE'
/app/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 appuser appuser
}
LOGROTATE

    echo "✅ System optimizations applied"
else
    echo "⚠️  System optimizations are Linux-specific"
fi
EOF

    chmod +x system-optimize.sh
    
    echo "✅ System optimization script created"
}

# Function to create monitoring script
create_monitoring_script() {
    echo "🔧 Creating monitoring script..."
    
    cat > monitor-resources.sh << 'EOF'
#!/bin/bash

# Resource monitoring script for low-end servers
echo "📊 Steel Unit Converter Resource Monitor"
echo "======================================"

# Function to check memory usage
check_memory() {
    echo "Memory Usage:"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        free -h
        echo
        echo "Top Memory Consumers:"
        ps aux --sort=-%mem | head -10
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        vm_stat
        echo
        echo "Top Memory Consumers:"
        ps aux -m | head -10
    fi
    echo
}

# Function to check disk usage
check_disk() {
    echo "Disk Usage:"
    df -h
    echo
    echo "Largest Files:"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        find . -type f -size +10M -exec ls -lh {} \; 2>/dev/null | sort -k5 -hr | head -10
    fi
    echo
}

# Function to check process status
check_processes() {
    echo "Application Processes:"
    if pgrep -f "gunicorn\|uvicorn\|node" > /dev/null; then
        ps aux | grep -E "(gunicorn|uvicorn|node)" | grep -v grep
    else
        echo "No application processes found"
    fi
    echo
}

# Function to check logs
check_logs() {
    echo "Recent Errors:"
    if [[ -f "logs/error.log" ]]; then
        tail -10 logs/error.log
    else
        echo "No error log found"
    fi
    echo
}

# Run checks
check_memory
check_disk
check_processes
check_logs

echo "Monitoring completed at $(date)"
EOF

    chmod +x monitor-resources.sh
    
    echo "✅ Monitoring script created"
}

# Function to create optimized startup script
create_startup_script() {
    echo "🔧 Creating optimized startup script..."
    
    cat > start-optimized.sh << 'EOF'
#!/bin/bash

# Optimized startup script for low-end servers
set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Configuration
BACKEND_PORT=8000
FRONTEND_PORT=3000
LOG_DIR="logs"
PID_DIR="pids"

# Create directories
mkdir -p "$LOG_DIR" "$PID_DIR"

# Function to check available memory
check_memory() {
    local available_memory
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        available_memory=$(free -m | awk '/^Mem:/{print $2}')
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        available_memory=$(sysctl -n hw.memsize | awk '{print int($1/1024/1024)}')
    else
        available_memory=2048  # Default
    fi
    echo "$available_memory"
}

# Function to start backend
start_backend() {
    echo "🚀 Starting optimized backend..."
    
    cd backend
    
    # Determine optimal configuration based on memory
    local memory=$(check_memory)
    local workers=1
    local threads=4
    
    if [[ $memory -gt 1024 ]]; then
        workers=2
        threads=4
    fi
    
    if [[ $memory -gt 2048 ]]; then
        workers=3
        threads=4
    fi
    
    echo "💾 Available memory: ${memory}MB"
    echo "⚙️  Using $workers workers with $threads threads each"
    
    # Start with gunicorn
    gunicorn -c gunicorn_optimized.conf.py main:app \
        --workers $workers \
        --threads $threads \
        --bind 0.0.0.0:$BACKEND_PORT \
        --daemon \
        --pid "../$PID_DIR/backend.pid" \
        --access-logfile "../$LOG_DIR/access.log" \
        --error-logfile "../$LOG_DIR/error.log" \
        --log-level info
    
    cd ..
    echo "✅ Backend started on port $BACKEND_PORT"
}

# Function to start frontend
start_frontend() {
    echo "🚀 Starting optimized frontend..."
    
    cd frontend
    
    # Build if needed
    if [[ ! -d "dist" ]] || [[ ! -f "dist/index.html" ]]; then
        echo "🔨 Building frontend..."
        NODE_OPTIONS="--max-old-space-size=512" npm run build:low-memory
    fi
    
    # Start with serve
    NODE_OPTIONS="--max-old-space-size=256" \
    nohup npx serve -s dist -l $FRONTEND_PORT > "../$LOG_DIR/frontend.log" 2>&1 &
    echo $! > "../$PID_DIR/frontend.pid"
    
    cd ..
    echo "✅ Frontend started on port $FRONTEND_PORT"
}

# Function to stop services
stop_services() {
    echo "🛑 Stopping services..."
    
    # Stop backend
    if [[ -f "$PID_DIR/backend.pid" ]]; then
        kill -TERM $(cat "$PID_DIR/backend.pid") 2>/dev/null || true
        rm -f "$PID_DIR/backend.pid"
    fi
    
    # Stop frontend
    if [[ -f "$PID_DIR/frontend.pid" ]]; then
        kill -TERM $(cat "$PID_DIR/frontend.pid") 2>/dev/null || true
        rm -f "$PID_DIR/frontend.pid"
    fi
    
    echo "✅ Services stopped"
}

# Function to show status
show_status() {
    echo "📊 Service Status:"
    
    # Check backend
    if [[ -f "$PID_DIR/backend.pid" ]] && kill -0 $(cat "$PID_DIR/backend.pid") 2>/dev/null; then
        echo "✅ Backend: Running (PID: $(cat "$PID_DIR/backend.pid"))"
    else
        echo "❌ Backend: Not running"
    fi
    
    # Check frontend
    if [[ -f "$PID_DIR/frontend.pid" ]] && kill -0 $(cat "$PID_DIR/frontend.pid") 2>/dev/null; then
        echo "✅ Frontend: Running (PID: $(cat "$PID_DIR/frontend.pid"))"
    else
        echo "❌ Frontend: Not running"
    fi
    
    echo
    echo "Memory Usage:"
    ps aux --sort=-%mem | grep -E "(gunicorn|serve|node)" | grep -v grep | head -5
}

# Main script logic
case "${1:-start}" in
    start)
        echo "🚀 Starting Steel Unit Converter (Optimized)"
        start_backend
        start_frontend
        echo "✅ All services started successfully"
        echo "🌐 Access the application at: http://localhost:$FRONTEND_PORT"
        ;;
    stop)
        stop_services
        ;;
    restart)
        stop_services
        sleep 3
        start_backend
        start_frontend
        echo "✅ Services restarted"
        ;;
    status)
        show_status
        ;;
    logs)
        echo "📝 Recent logs:"
        echo "--- Backend Error Log ---"
        tail -20 "$LOG_DIR/error.log" 2>/dev/null || echo "No backend errors"
        echo "--- Frontend Log ---"
        tail -20 "$LOG_DIR/frontend.log" 2>/dev/null || echo "No frontend logs"
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs}"
        exit 1
        ;;
esac
EOF

    chmod +x start-optimized.sh
    
    echo "✅ Optimized startup script created"
}

# Main execution
main() {
    local memory=$(check_memory)
    echo "💾 Available memory: ${memory}MB"
    
    if [[ $memory -lt 512 ]]; then
        echo "⚠️  Warning: Very low memory detected. Consider upgrading your server."
    elif [[ $memory -lt 1024 ]]; then
        echo "⚠️  Warning: Low memory detected. Applying aggressive optimizations."
    fi
    
    # Create logs directory
    mkdir -p logs pids
    
    # Run optimizations
    optimize_backend
    optimize_frontend
    create_docker_optimizations
    create_system_optimizations
    create_monitoring_script
    create_startup_script
    
    echo
    echo "🎉 Low-end server optimization completed!"
    echo
    echo "📋 Next steps:"
    echo "1. To start the optimized application: ./start-optimized.sh"
    echo "2. To monitor resources: ./monitor-resources.sh"
    echo "3. To apply system optimizations (Linux): sudo ./system-optimize.sh"
    echo "4. To use Docker: docker-compose -f docker-compose.optimized.yml up"
    echo
    echo "💡 Tips for low-end servers:"
    echo "- Monitor memory usage regularly"
    echo "- Set up swap space if needed"
    echo "- Consider using a CDN for static assets"
    echo "- Enable gzip compression in your web server"
    echo "- Schedule regular log rotation"
    echo
}

# Run main function
main "$@"