<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend Table Rendering</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-radius: 4px;
            margin: 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        #console {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Frontend Table Rendering Test</h1>
        
        <div id="status" class="status">
            Ready to test table rendering...
        </div>
        
        <button onclick="testTableRendering()">Test Table Rendering</button>
        <button onclick="testStreamingAPI()">Test Streaming API</button>
        <button onclick="clearConsole()">Clear Console</button>
        
        <div id="tableContainer">
            <!-- Table will be rendered here -->
        </div>
        
        <div id="console">Console output will appear here...</div>
    </div>

    <script>
        function log(message) {
            const console = document.getElementById('console');
            const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
            console.textContent += `[${timestamp}] ${message}\n`;
            console.scrollTop = console.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console').textContent = '';
        }
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function testTableRendering() {
            log('🧪 Testing table rendering...');
            
            // Simulate the exact data format from the backend
            const testData = {
                headers: ['Item Code', 'Description', 'Size (Original)', 'Customer QTY', 'Size (Converted)', 'Converted QTY', 'Remarks'],
                rows: [
                    ['001', 'S/S 430 BA NO PI', '0.015(+/-0.0015)" x 2.343(+/-0.005)" x COIL', '7190#', '0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL', '3261.2 kg', 'BA Finish'],
                    ['002', 'S/S 430 BA NO PI', '0.015(+/-0.0015)" x 2.406(+/-0.005)" x COIL', '8061#', '0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL', '3656.7 kg', 'BA Finish'],
                    ['003', 'S/S 430 BA NO PI', '0.015(+/-0.0015)" x 16.50(+/-0.005)" x COIL', '12550#', '0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL', '5692.7 kg', 'BA Finish']
                ]
            };
            
            log(`📊 Headers (${testData.headers.length}): ${testData.headers.join(', ')}`);
            log(`📊 Data rows: ${testData.rows.length}`);
            
            // Create table HTML
            let tableHTML = '<table>';
            
            // Add headers
            tableHTML += '<thead><tr>';
            testData.headers.forEach(header => {
                tableHTML += `<th>${header}</th>`;
            });
            tableHTML += '</tr></thead>';
            
            // Add data rows
            tableHTML += '<tbody>';
            testData.rows.forEach((row, index) => {
                tableHTML += '<tr>';
                row.forEach(cell => {
                    tableHTML += `<td>${cell}</td>`;
                });
                tableHTML += '</tr>';
                log(`✅ Row ${index + 1}: ${row.length} cells`);
            });
            tableHTML += '</tbody>';
            
            tableHTML += '</table>';
            
            // Display the table
            document.getElementById('tableContainer').innerHTML = tableHTML;
            
            // Validate the table
            const table = document.querySelector('table');
            const headerCells = table.querySelectorAll('th');
            const bodyRows = table.querySelectorAll('tbody tr');
            
            log(`🎯 Table rendered with ${headerCells.length} header columns`);
            log(`🎯 Table rendered with ${bodyRows.length} data rows`);
            
            // Check each row
            bodyRows.forEach((row, index) => {
                const cells = row.querySelectorAll('td');
                log(`📋 Row ${index + 1}: ${cells.length} cells`);
                if (cells.length !== testData.headers.length) {
                    log(`⚠️  Row ${index + 1} has ${cells.length} cells, expected ${testData.headers.length}`);
                }
            });
            
            if (headerCells.length === 7) {
                setStatus('✅ SUCCESS: 7-column table rendered correctly!', 'success');
                log('🎉 SUCCESS: Table rendering working correctly!');
            } else {
                setStatus(`❌ ERROR: Expected 7 columns, got ${headerCells.length}`, 'error');
                log(`💥 ERROR: Expected 7 columns, got ${headerCells.length}`);
            }
        }
        
        async function testStreamingAPI() {
            log('🌐 Testing streaming API...');
            setStatus('Testing streaming API...', 'info');
            
            const testData = {
                text: `S/S 430  BA  NO PI
 
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#`,
                function: 'table',
                unit_system: 'metric'
            };
            
            try {
                const response = await fetch('http://localhost:8000/api/llm/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                log('📡 Streaming response received');
                const reader = response.body.getReader();
                let accumulatedData = '';
                let chunkCount = 0;
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = new TextDecoder().decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const dataStr = line.substring(6);
                            if (dataStr === '[DONE]') {
                                log('🏁 Stream completed');
                                break;
                            }
                            
                            try {
                                const data = JSON.parse(dataStr);
                                if (data.content) {
                                    accumulatedData += data.content;
                                    chunkCount++;
                                    
                                    if (chunkCount % 50 === 0) {
                                        log(`📈 Progress: ${chunkCount} chunks, ${accumulatedData.length} chars`);
                                    }
                                }
                            } catch (e) {
                                // Ignore JSON parse errors
                            }
                        }
                    }
                }
                
                log(`📊 Final data length: ${accumulatedData.length} characters`);
                
                // Parse the streaming table
                const match = accumulatedData.match(/<table_stream>\s*([\s\S]*?)\s*<\/table_stream>/);
                if (match) {
                    const tableContent = match[1].trim();
                    const lines = tableContent.split('\n');
                    
                    if (lines.length > 0) {
                        const headerLine = lines[0];
                        const headers = headerLine.split('|').map(h => h.trim());
                        
                        log(`✅ Parsed headers (${headers.length}): ${headers.join(', ')}`);
                        
                        const dataRows = lines.slice(1).filter(line => line.trim());
                        log(`📊 Data rows: ${dataRows.length}`);
                        
                        // Render the streaming table
                        const tableData = {
                            headers: headers,
                            rows: dataRows.map(row => row.split('|').map(cell => cell.trim()))
                        };
                        
                        // Display the table
                        let tableHTML = '<h3>📊 Streaming API Result:</h3><table>';
                        
                        // Add headers
                        tableHTML += '<thead><tr>';
                        tableData.headers.forEach(header => {
                            tableHTML += `<th>${header}</th>`;
                        });
                        tableHTML += '</tr></thead>';
                        
                        // Add data rows
                        tableHTML += '<tbody>';
                        tableData.rows.forEach(row => {
                            tableHTML += '<tr>';
                            row.forEach(cell => {
                                tableHTML += `<td>${cell}</td>`;
                            });
                            tableHTML += '</tr>';
                        });
                        tableHTML += '</tbody>';
                        
                        tableHTML += '</table>';
                        
                        document.getElementById('tableContainer').innerHTML = tableHTML;
                        
                        if (headers.length === 7) {
                            setStatus('✅ SUCCESS: Streaming API returned 7-column table!', 'success');
                            log('🎉 SUCCESS: Streaming API working correctly!');
                        } else {
                            setStatus(`⚠️  WARNING: Expected 7 columns, got ${headers.length}`, 'error');
                            log(`⚠️  WARNING: Expected 7 columns, got ${headers.length}`);
                        }
                    }
                } else {
                    log('❌ No table_stream content found in response');
                    setStatus('❌ No table found in streaming response', 'error');
                }
                
            } catch (error) {
                log(`💥 API Error: ${error.message}`);
                setStatus(`❌ API Error: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        log('🚀 Frontend table test initialized');
        log('Click "Test Table Rendering" to test static table rendering');
        log('Click "Test Streaming API" to test live streaming API');
    </script>
</body>
</html>