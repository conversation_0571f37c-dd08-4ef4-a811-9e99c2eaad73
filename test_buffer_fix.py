#!/usr/bin/env python3
"""
Test the buffer fix for streaming corruption
"""
import requests
import json
import time

def test_buffer_fix():
    """Test streaming with enhanced debugging to verify buffer fix"""
    
    test_data = """S/S 430  BA  NO PI
 
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#
.015(+/-.0015) X 19.68"(+/-.03125) X COIL                                         8,835#
.015(+/-.0015) X 47.438"(+/-.03125) X COIL                                      57,655#
.015(+/-.0015) X 47.000"(+/-.03125) X COIL                                      118,001#
.015(+/-.0015) X 35.438"(+/-.03125) X COIL                                      62,515#
 
S/S 430 #2B NO PI
 
.015(+/-.0015) X 16.938"(+/-.005) X COIL                                           5,000#
.016(+/-.0015) X 19.6875"(+/-.005) X COIL                                         725,321#
.016(+/-.0015) X 35.500"(+/-.03125) X COIL                                      122,083#
.016(+/-.0015) X 36.000"(+/-.03125) X COIL                                      234,265#
.016(+/-.0015) X 48.000"(+/-.03125) X COIL                                      201,347#
.018(+/-.0015) X 36.000"(+/-.03125) X COIL                                      33,841#"""

    url = "http://localhost:8000/api/llm/stream"
    payload = {
        "text": test_data,
        "function": "conversion,table",
        "unit_system": "metric"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    print("🔧 TESTING BUFFER FIX FOR STREAMING CORRUPTION")
    print("=" * 80)
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True)
        
        if response.status_code == 200:
            print("✅ Testing with buffer corruption fix...")
            
            accumulated_data = ""
            
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    
                    if decoded_line.startswith('data: '):
                        try:
                            data_str = decoded_line[6:]
                            if data_str == '[DONE]':
                                break
                            
                            data = json.loads(data_str)
                            if 'content' in data:
                                chunk_content = data['content']
                                accumulated_data += chunk_content
                                
                        except json.JSONDecodeError:
                            pass
            
            # Extract and verify table
            import re
            table_match = re.search(r'<table_stream>\s*([\s\S]*?)\s*</table_stream>', accumulated_data)
            if table_match:
                table_content = table_match.group(1).strip()
                lines = table_content.split('\n')
                
                print(f"📊 VERIFICATION RESULTS:")
                print(f"   Total lines: {len(lines)}")
                print(f"   Expected: 14 lines (1 header + 13 data)")
                
                corruption_found = False
                for i, line in enumerate(lines):
                    if line.strip():
                        pipe_count = line.count('|')
                        cells = line.split('|')
                        
                        # Check for corruption patterns
                        has_mixed_data = any(
                            'S/S 430' in cell and ('kg' in cell or 'mm' in cell) and 'COIL' in cell
                            for cell in cells
                        )
                        
                        if pipe_count != 6 or len(cells) != 7 or has_mixed_data:
                            print(f"🚨 CORRUPTION in line {i}: {pipe_count} pipes, {len(cells)} cells")
                            print(f"   Content: {line[:100]}...")
                            corruption_found = True
                        elif i < 5 or i > len(lines) - 3:  # Show first and last few lines
                            print(f"✅ Line {i}: {pipe_count} pipes, {len(cells)} cells - OK")
                
                if not corruption_found:
                    print("🎉 NO CORRUPTION DETECTED - Buffer fix successful!")
                else:
                    print("⚠️ Corruption still present - may need additional fixes")
                    
            else:
                print("❌ No table_stream content found")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"💥 Request failed: {e}")

if __name__ == "__main__":
    test_buffer_fix()