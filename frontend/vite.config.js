import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react({
    // Configure the @emotion/babel-plugin
    babel: {
      plugins: [
        [
          '@emotion/babel-plugin',
          {
            // These options are required for proper emotion bundling
            sourceMap: false,
            autoLabel: 'dev-only',
            labelFormat: '[local]',
            cssPropOptimization: true,
          },
        ],
      ],
    },
  })],
  build: {
    outDir: 'dist',
    minify: false, // Disable minification to avoid issues
    sourcemap: false,
    target: 'es2015',
    cssCodeSplit: true,
    assetsInlineLimit: 4096,
    emptyOutDir: true,
    rollupOptions: {
      output: {
        // Modify the chunk strategy to avoid circular dependencies
        manualChunks: (id) => {
          // Put React and ReactDOM in the vendor chunk
          if (id.includes('node_modules/react/') || 
              id.includes('node_modules/react-dom/')) {
            return 'vendor';
          }
          
          // Put React Router in its own chunk
          if (id.includes('node_modules/react-router') || 
              id.includes('node_modules/react-router-dom')) {
            return 'router';
          }
          
          // Put Material UI components in their own chunk
          if (id.includes('node_modules/@mui/material')) {
            return 'material';
          }
          
          // Put Material UI icons in their own chunk
          if (id.includes('node_modules/@mui/icons-material')) {
            return 'icons';
          }
          
          // Important: Keep emotion libraries together in one chunk
          if (id.includes('node_modules/@emotion/')) {
            return 'emotion';
          }
        }
      }
    }
  }
});
