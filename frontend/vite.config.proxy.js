import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import fs from 'fs';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    host: '0.0.0.0',
    strictPort: true,
    cors: true,
    // Disable HMR WebSocket to avoid connection issues
    hmr: false,
    // Allow specific hosts
    allowedHosts: ['localhost', '127.0.0.1', 'steelnet.ai', '************'],
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: true,  // Allow secure connections
        ws: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
      '/conversion': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: true,  // Allow secure connections
        ws: true,
      },
      '/auth': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: true,  // Allow secure connections
        ws: true,
      },
      '/health': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: true,  // Allow secure connections
        ws: true,
      }
    },
  },
});
