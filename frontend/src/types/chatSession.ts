import { Message } from './chat';

// Unique identifier for a chat session
export interface ChatSession {
  id: string;             // Unique identifier for the session
  title: string;          // A title for the session (derived from first message or custom)
  lastUpdated: Date;      // When the session was last updated
  messages: Message[];    // Messages in this chat session
  function?: string;      // The primary function used in this session (conversion, table, general)
  preview?: string;       // A short preview of the last message
}

// Type for session list (used when listing all sessions)
export interface ChatSessionSummary {
  id: string;
  title: string;
  lastUpdated: Date;
  messageCount: number;
  function?: string;
  preview?: string;
  messages: Message[]; // Include the full messages array
}