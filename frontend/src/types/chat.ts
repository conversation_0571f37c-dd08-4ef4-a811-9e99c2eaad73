export interface ConversionResult {
  from?: string;
  to?: string;
  value?: number;
  converted_value?: number;
  formula?: string;
  original_text: string;
  converted_text: string;
  hasTable?: boolean;
  function?: string;
  table_mode?: boolean;
}

export interface TableData {
  steelType?: string;
  length?: { value: number; unit: string };
  width?: { value: number; unit: string };
  thickness?: { value: number; unit: string };
  weight?: { value: number; unit: string };
}

export interface Message {
  type: 'user' | 'assistant';
  content: string;
  result?: ConversionResult;
  tableData?: TableData;
  timestamp: Date;
  isTable?: boolean;
  function?: string;
}

export interface Function {
  id: string;
  name: string;
  description: string;
  icon: string;
} 