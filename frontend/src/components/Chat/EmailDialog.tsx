import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  CircularProgress,
  Alert,
  Typography,
  Box
} from '@mui/material';
import EmailIcon from '@mui/icons-material/Email';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

interface EmailDialogProps {
  open: boolean;
  onClose: () => void;
  resultData: any; // The data to be sent via email
  resultType: 'conversion' | 'table';
}

const EmailDialog: React.FC<EmailDialogProps> = ({ open, onClose, resultData, resultType }) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const { user } = useAuth();
  const { t } = useLanguage();

  // Pre-fill email if user is logged in
  React.useEffect(() => {
    if (user && user.email) {
      setEmail(user.email);
    }
  }, [user, open]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !email.includes('@')) {
      setError(t('emailError'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/email/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(user && { Authorization: `Bearer ${localStorage.getItem('token')}` }),
        },
        body: JSON.stringify({
          email,
          data: resultData,
          type: resultType,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${await response.text()}`);
      }

      setSuccess(true);
      setTimeout(() => {
        onClose();
        setSuccess(false);
      }, 2000);
    } catch (error) {
      console.error('Email sending error:', error);
      setError(t('emailError'));
    } finally {
      setLoading(false);
    }
  };

  const renderPreviewContent = () => {
    if (resultType === 'conversion') {
      return `${resultData.value} ${resultData.from} = ${resultData.converted_value} ${resultData.to}`;
    } else {
      const { steelType, length, width, thickness, weight } = resultData;
      return `${t('steelType')}: ${steelType || 'Carbon Steel'}
${t('length')}: ${length.value.toFixed(2)} ${length.unit}
${t('width')}: ${width.value.toFixed(2)} ${width.unit}
${t('thickness')}: ${thickness.value.toFixed(2)} ${thickness.unit}
${t('weight')}: ${weight?.value.toFixed(2) || 'N/A'} ${weight?.unit || ''}`;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <EmailIcon color="primary" />
        {t('emailTitle')}
      </DialogTitle>
      
      <form onSubmit={handleSubmit}>
        <DialogContent>
          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
          {success && <Alert severity="success" sx={{ mb: 2 }}>{t('emailSuccess')}</Alert>}
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {t('emailDescription')}
          </Typography>
          
          <TextField
            autoFocus
            margin="dense"
            label={t('emailAddress')}
            type="email"
            fullWidth
            variant="outlined"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={loading || success}
            required
          />
          
          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>{t('emailPreview')}:</Typography>
            <Typography variant="body2" color="text.secondary" sx={{ whiteSpace: 'pre-wrap' }}>
              {renderPreviewContent()}
            </Typography>
          </Box>
        </DialogContent>
        
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={onClose} disabled={loading}>
            {t('cancel')}
          </Button>
          <Button 
            type="submit" 
            variant="contained" 
            disabled={loading || success || !email}
            startIcon={loading ? <CircularProgress size={16} /> : <EmailIcon />}
          >
            {loading ? t('sending') : t('send')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default EmailDialog; 