import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Container,
  TextField,
  Button,
  Paper,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import { Send as SendIcon, FileCopy as CopyIcon, Email as EmailIcon } from '@mui/icons-material';

interface ConversionResult {
  steelType?: string;
  length: { value: number; unit: string };
  width: { value: number; unit: string };
  thickness: { value: number; unit: string };
  weight?: { value: number; unit: string };
}

interface Message {
  type: 'user' | 'ai';
  content: string;
  result?: ConversionResult;
}

const UnitConverter: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [unitSystem, setUnitSystem] = useState<'imperial' | 'metric'>('metric');
  const [welcomeOpen, setWelcomeOpen] = useState(true);
  const messagesEndRef = useRef<null | HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSend = () => {
    if (!input.trim()) return;

    const newMessages = [...messages, { type: 'user', content: input }];
    setMessages(newMessages);
    setInput('');

    // Simulate AI response with conversion
    const result: ConversionResult = {
      steelType: 'Carbon Steel',
      length: { value: 1524, unit: 'mm' },
      width: { value: 254, unit: 'mm' },
      thickness: { value: 2, unit: 'mm' },
      weight: { value: 15.6, unit: 'kg' },
    };

    setTimeout(() => {
      setMessages([...newMessages, {
        type: 'ai',
        content: 'Here are your conversion results:',
        result
      }]);
    }, 500);
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <Container maxWidth="md" sx={{ height: '100vh', py: 2, display: 'flex', flexDirection: 'column' }}>
      {/* Welcome Dialog */}
      <Dialog open={welcomeOpen} onClose={() => setWelcomeOpen(false)}>
        <DialogTitle>Welcome to SteelNet!</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Enter the steel data you want to convert or upload a file (CSV).
            Type 'help' for assistance.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setWelcomeOpen(false)}>Get Started</Button>
        </DialogActions>
      </Dialog>

      {/* Unit System Selection */}
      <Box sx={{ mb: 2 }}>
        <FormControl fullWidth>
          <InputLabel>Unit System</InputLabel>
          <Select
            value={unitSystem}
            label="Unit System"
            onChange={(e) => setUnitSystem(e.target.value as 'imperial' | 'metric')}
          >
            <MenuItem value="imperial">Imperial to Metric</MenuItem>
            <MenuItem value="metric">Metric to Imperial</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Messages Container */}
      <Paper 
        elevation={3} 
        sx={{ 
          flex: 1, 
          mb: 2, 
          p: 2, 
          overflow: 'auto',
          display: 'flex',
          flexDirection: 'column',
          gap: 2
        }}
      >
        {messages.map((message, index) => (
          <Box
            key={index}
            sx={{
              display: 'flex',
              justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
            }}
          >
            <Paper
              elevation={1}
              sx={{
                p: 2,
                maxWidth: '80%',
                bgcolor: message.type === 'user' ? 'rgba(255,255,255,0.15)' : 'background.paper',
                color: message.type === 'user' ? 'white' : 'text.primary',
              }}
            >
              <Typography>{message.content}</Typography>
              {message.result && (
                <TableContainer component={Paper} sx={{ mt: 2 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Property</TableCell>
                        <TableCell align="right">Value</TableCell>
                        <TableCell align="right">Unit</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.entries(message.result).map(([key, value]) => (
                        value && typeof value === 'object' && (
                          <TableRow key={key}>
                            <TableCell>{key.charAt(0).toUpperCase() + key.slice(1)}</TableCell>
                            <TableCell align="right">{value.value}</TableCell>
                            <TableCell align="right">{value.unit}</TableCell>
                          </TableRow>
                        )
                      ))}
                    </TableBody>
                  </Table>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
                    <IconButton onClick={() => handleCopy(JSON.stringify(message.result))}>
                      <CopyIcon />
                    </IconButton>
                    <IconButton>
                      <EmailIcon />
                    </IconButton>
                  </Box>
                </TableContainer>
              )}
            </Paper>
          </Box>
        ))}
        <div ref={messagesEndRef} />
      </Paper>

      {/* Input Area */}
      <Box sx={{ display: 'flex', gap: 1 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Enter steel dimensions (e.g., Width 10 inches, Length 5 feet, Thickness 2mm)"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && handleSend()}
        />
        <Button
          variant="contained"
          onClick={handleSend}
          endIcon={<SendIcon />}
        >
          Send
        </Button>
      </Box>
    </Container>
  );
};

export default UnitConverter;
