import React from 'react';
import { Icon<PERSON><PERSON>on, <PERSON>u, <PERSON>uI<PERSON>, Tooltip, useTheme, Box } from '@mui/material';
import LanguageIcon from '@mui/icons-material/Language';
import { useLanguage, Language } from '../contexts/LanguageContext';

interface LanguageSwitcherProps {
  fixed?: boolean;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ fixed = false }) => {
  const { language, setLanguage, t } = useLanguage();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const theme = useTheme();

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageSelect = (lang: Language) => {
    setLanguage(lang);
    handleClose();
  };

  return (
    <Box sx={fixed ? {
      position: 'fixed',
      top: 16,
      right: 16,
      zIndex: theme.zIndex.drawer - 1
    } : {}}>
      <Tooltip title={t('changeLanguage')}>
        <IconButton
          onClick={handleClick}
          size="small"
          sx={{
            color: theme.palette.text.primary,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.8)',
            boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
            '&:hover': {
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.9)',
            }
          }}
        >
          <LanguageIcon />
        </IconButton>
      </Tooltip>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem 
          selected={language === 'zh'} 
          onClick={() => handleLanguageSelect('zh')}
        >
          {t('chinese')}
        </MenuItem>
        <MenuItem 
          selected={language === 'en'} 
          onClick={() => handleLanguageSelect('en')}
        >
          {t('english')}
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default LanguageSwitcher; 