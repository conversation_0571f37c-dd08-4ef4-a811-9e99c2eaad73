import React, { useState } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  Link,
  CircularProgress,
  IconButton,
  <PERSON>per,
  Step,
  StepLabel,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { authApi } from '../../services/api';
import { useLanguage } from '../../contexts/LanguageContext';

interface ResetPasswordFormProps {
  onClose: () => void;
  onBackToLogin: () => void;
}

const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({ onClose, onBackToLogin }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const { t, language } = useLanguage();

  const steps = [
    t('requestResetCode'),
    t('verifyAndReset'),
  ];

  const handleRequestReset = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      setError(t('emailRequired'));
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError(t('invalidEmail'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      await authApi.requestPasswordReset(email, language);
      setSuccess(t('resetCodeSent'));
      setActiveStep(1);
    } catch (err: any) {
      console.error('Reset request error:', err.response || err);
      setError(err.response?.data?.detail || t('resetRequestFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmReset = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!code) {
      setError(t('verificationCodeRequired'));
      return;
    }

    if (!newPassword) {
      setError(t('newPasswordRequired'));
      return;
    }

    if (newPassword !== confirmPassword) {
      setError(t('passwordsDoNotMatch'));
      return;
    }

    // Password strength validation
    if (newPassword.length < 6) {
      setError(t('passwordHelp'));
      return;
    }

    // Check for uppercase letters
    if (!/[A-Z]/.test(newPassword)) {
      setError(t('passwordHelp'));
      return;
    }
    
    // Check for lowercase letters
    if (!/[a-z]/.test(newPassword)) {
      setError(t('passwordHelp'));
      return;
    }
    
    // Check for numbers
    if (!/\d/.test(newPassword)) {
      setError(t('passwordHelp'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      await authApi.confirmPasswordReset(email, code, newPassword);
      setSuccess(t('passwordResetSuccess'));
      // Redirect to login after 2 seconds
      setTimeout(() => {
        onBackToLogin();
      }, 2000);
    } catch (err: any) {
      console.error('Reset confirmation error:', err.response || err);
      setError(err.response?.data?.detail || t('resetConfirmFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, maxWidth: 400, mx: 'auto', mt: 4, position: 'relative' }}>
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          right: 8,
          top: 8,
        }}
      >
        <CloseIcon />
      </IconButton>

      <Typography variant="h5" gutterBottom align="center">
        {t('resetPassword')}
      </Typography>

      <Stepper activeStep={activeStep} sx={{ mb: 4, mt: 2 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      {activeStep === 0 ? (
        <Box component="form" noValidate onSubmit={handleRequestReset} sx={{ mt: 2 }}>
          <Typography variant="body2" sx={{ mb: 2 }}>
            {t('resetPasswordInstructions')}
          </Typography>
          
          <TextField
            fullWidth
            label={t('email')}
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            margin="normal"
            required
            placeholder={t('email')}
            error={!!error && !email}
            disabled={loading}
          />

          {error && (
            <Typography color="error" sx={{ mt: 2 }}>
              {error}
            </Typography>
          )}

          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : t('sendResetCode')}
          </Button>
        </Box>
      ) : (
        <Box component="form" noValidate onSubmit={handleConfirmReset} sx={{ mt: 2 }}>
          <Typography variant="body2" sx={{ mb: 2 }}>
            {t('enterVerificationCodeAndNewPassword')}
          </Typography>
          
          <TextField
            fullWidth
            label={t('verificationCode')}
            value={code}
            onChange={(e) => setCode(e.target.value)}
            margin="normal"
            required
            error={!!error && !code}
            disabled={loading}
          />

          <TextField
            fullWidth
            label={t('newPassword')}
            type="password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            margin="normal"
            required
            error={!!error && !newPassword}
            disabled={loading}
          />

          <TextField
            fullWidth
            label={t('confirmPassword')}
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            margin="normal"
            required
            error={!!error && !confirmPassword}
            disabled={loading}
          />

          {error && (
            <Typography color="error" sx={{ mt: 2 }}>
              {error}
            </Typography>
          )}

          {success && (
            <Typography color="success" sx={{ mt: 2 }}>
              {success}
            </Typography>
          )}

          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : t('resetPassword')}
          </Button>

          <Button
            fullWidth
            variant="outlined"
            onClick={() => setActiveStep(0)}
            sx={{ mb: 2 }}
            disabled={loading}
          >
            {t('back')}
          </Button>
        </Box>
      )}

      <Box sx={{ textAlign: 'center' }}>
        <Link
          component="button"
          variant="body2"
          onClick={onBackToLogin}
          sx={{ cursor: 'pointer' }}
          disabled={loading}
        >
          {t('backToLogin')}
        </Link>
      </Box>
    </Paper>
  );
};

export default ResetPasswordForm; 