import React, { useEffect, useState } from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Box,
  IconButton,
  Tooltip,
} from '@mui/material';
import { Refresh as RefreshIcon, Email as EmailIcon } from '@mui/icons-material';
import { conversionApi, ConversionHistory as ConversionHistoryType } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const ConversionHistory: React.FC = () => {
  const [history, setHistory] = useState<ConversionHistoryType[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  const loadHistory = async () => {
    try {
      setLoading(true);
      const data = await conversionApi.getHistory();
      setHistory(data);
    } catch (error) {
      console.error('Failed to load conversion history:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadHistory();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Paper elevation={3} sx={{ p: 2, mt: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" component="h2">
          {user ? 'Your Conversion History' : 'Recent Conversions'}
        </Typography>
        <IconButton onClick={loadHistory} disabled={loading}>
          <RefreshIcon />
        </IconButton>
      </Box>
      
      <TableContainer>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>Date</TableCell>
              <TableCell>Steel Type</TableCell>
              <TableCell>Length</TableCell>
              <TableCell>Width</TableCell>
              <TableCell>Thickness</TableCell>
              <TableCell>Weight</TableCell>
              <TableCell>Units</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {history.map((item) => (
              <TableRow key={item.id}>
                <TableCell>{formatDate(item.created_at)}</TableCell>
                <TableCell>{item.steel_type || 'N/A'}</TableCell>
                <TableCell>{item.input_length || 'N/A'}</TableCell>
                <TableCell>{item.input_width || 'N/A'}</TableCell>
                <TableCell>{item.input_thickness || 'N/A'}</TableCell>
                <TableCell>{item.input_weight || 'N/A'}</TableCell>
                <TableCell>
                  {item.input_unit_system} → {item.output_unit_system}
                </TableCell>
                <TableCell>
                  <Tooltip title="Email Results">
                    <IconButton size="small">
                      <EmailIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default ConversionHistory;
