// Environment-specific configuration
const isDevelopment = import.meta.env.MODE === 'development';

// Get the current hostname or IP address and protocol
const currentHost = window.location.hostname;
const currentProtocol = window.location.protocol;

// For production servers, always use HTTP for backend API calls
// since the backend doesn't support HTTPS yet
const apiProtocol = (currentHost === '************' || currentHost === 'steelnet.ai')
  ? 'http:'
  : currentProtocol;

// Check if we're on HTTPS and need to use a proxy
const isHttpsOnProduction = currentProtocol === 'https:' &&
                           (currentHost === '************' || currentHost === 'steelnet.ai');

// API base URL configuration
// Use HTTP for production servers, otherwise use the same protocol as the frontend
// If we're on HTTPS on production, we'll use the proxy in the components
export const API_BASE_URL = isHttpsOnProduction
  ? `${currentProtocol}//${currentHost}/api` // Use proxy path
  : `${apiProtocol}//${currentHost}:8000/api`; // Direct access

console.log('Using dynamic API base URL:', API_BASE_URL);
console.log('Frontend protocol:', currentProtocol, 'API protocol:', apiProtocol);
console.log('Using HTTPS proxy:', isHttpsOnProduction);

export default {
  API_BASE_URL
};