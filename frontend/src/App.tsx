import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { ThemeProvider, CssBaseline, Box, Dialog } from '@mui/material';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import ChatLayout from './components/Layout/ChatLayout';
import Sidebar from './components/Sidebar/Sidebar';
import ConversionChat from './components/Chat/ConversionChat';
import LoginForm from './components/Auth/LoginForm';
import RegisterForm from './components/Auth/RegisterForm';
import ResetPasswordForm from './components/Auth/ResetPasswordForm';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import PublicRoute from './components/Auth/PublicRoute';
import HistoryView from './components/History/HistoryView';
import SettingsView from './components/Settings/SettingsView';
import theme from './theme';

const MainLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ChatLayout sidebar={<Sidebar />}>{children}</ChatLayout>
);

// Create a component that will handle auth forms
const AuthContainer: React.FC<{ initialShowLogin?: boolean }> = ({ initialShowLogin = true }) => {
  const [showLogin, setShowLogin] = useState(initialShowLogin);
  const [showPasswordReset, setShowPasswordReset] = useState(false);
  const [open, setOpen] = useState(true);
  const navigate = useNavigate();

  const handleToggleForm = () => {
    setShowLogin(!showLogin);
    setShowPasswordReset(false);
  };

  const handleShowPasswordReset = () => {
    setShowPasswordReset(true);
  };

  const handleBackToLogin = () => {
    setShowPasswordReset(false);
    setShowLogin(true);
  };

  const handleClose = () => {
    setOpen(false);
    navigate('/');
  };

  const handleLoginSuccess = (token: string, username: string) => {
    setOpen(false);
    navigate('/');
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <Box p={2}>
        {showPasswordReset ? (
          <ResetPasswordForm
            onClose={handleClose}
            onBackToLogin={handleBackToLogin}
          />
        ) : showLogin ? (
          <LoginForm
            onToggleForm={handleToggleForm}
            onLoginSuccess={handleLoginSuccess}
            onClose={handleClose}
            onForgotPassword={handleShowPasswordReset}
          />
        ) : (
          <RegisterForm
            onToggleForm={handleToggleForm}
            onClose={handleClose}
          />
        )}
      </Box>
    </Dialog>
  );
};

// Create wrapper components for the routes
const LoginRoute: React.FC = () => <AuthContainer initialShowLogin={true} />;
const RegisterRoute: React.FC = () => <AuthContainer initialShowLogin={false} />;

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <LanguageProvider>
        <AuthProvider>
          <Router>
            <Routes>
              <Route path="/login" element={<PublicRoute><LoginRoute /></PublicRoute>} />
              <Route path="/register" element={<PublicRoute><RegisterRoute /></PublicRoute>} />
              <Route
                path="/"
                element={
                  <MainLayout>
                    <ConversionChat />
                  </MainLayout>
                }
              />
              <Route
                path="/history"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <HistoryView />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
              <Route
                path="/settings"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <SettingsView />
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
            </Routes>
          </Router>
        </AuthProvider>
      </LanguageProvider>
    </ThemeProvider>
  );
}

export default App;
