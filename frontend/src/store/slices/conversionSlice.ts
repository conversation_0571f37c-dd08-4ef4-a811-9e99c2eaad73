import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ConversionHistory {
  id: number;
  inputText: string;
  outputText: string;
  conversionType: 'M' | 'I';
  createdAt: string;
}

interface ConversionState {
  history: ConversionHistory[];
  loading: boolean;
  error: string | null;
}

const initialState: ConversionState = {
  history: [],
  loading: false,
  error: null,
};

const conversionSlice = createSlice({
  name: 'conversion',
  initialState,
  reducers: {
    setHistory: (state, action: PayloadAction<ConversionHistory[]>) => {
      state.history = action.payload;
    },
    addToHistory: (state, action: PayloadAction<ConversionHistory>) => {
      state.history.unshift(action.payload);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setHistory, addToHistory, setLoading, setError } = conversionSlice.actions;
export const conversionReducer = conversionSlice.reducer;
