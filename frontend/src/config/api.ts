// API configuration for development
// Use dynamic hostname and protocol detection to support both HTTP and HTTPS
const currentHost = window.location.hostname;
const currentProtocol = window.location.protocol;

// For production servers, always use HTTP for backend API calls
// since the backend doesn't support HTTPS yet
const apiProtocol = (currentHost === '************' || currentHost === 'steelnet.ai')
  ? 'http:'
  : currentProtocol;

const API_BASE_URL = `${currentProtocol}//${currentHost}:5173`;
const BACKEND_URL = `${apiProtocol}//${currentHost}:8000`;

console.log('Using dynamic API configuration:');
console.log('- Frontend protocol:', currentProtocol);
console.log('- API protocol:', apiProtocol);
console.log('- Host:', currentHost);
console.log('- Frontend URL:', API_BASE_URL);
console.log('- Backend URL:', BACKEND_URL);

export const API_ENDPOINTS = {
  // Use the proxy for all API requests
  LLM: `${API_BASE_URL}/api/llm`,
  LLM_FUNCTIONS: `${API_BASE_URL}/api/llm/functions`,
  CONVERSION: `${API_BASE_URL}/conversion`,
  AUTH: `${API_BASE_URL}/auth`,
  HEALTH: `${API_BASE_URL}/health`,
};

export default API_ENDPOINTS;
