import React, { createContext, useContext, useState, useEffect } from 'react';
import { authApi, User } from '../services/api';
import { syncIndexedDBToServer } from '../services/chatSessionStorage';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (username: string, email: string, password: string) => Promise<void>;
  verifyEmail: (email: string, code: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadUser = async () => {
      try {
        const token = localStorage.getItem('token');
        if (token) {
          const profile = await authApi.getProfile();
          setUser(profile);
        }
      } catch (error) {
        console.error('Failed to load user:', error);
        localStorage.removeItem('token');
      } finally {
        setLoading(false);
      }
    };

    loadUser();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      // First call the login API to get the token
      const loginResponse = await authApi.login(email, password);
      console.log('Login API response:', loginResponse);

      // Then get the user profile
      const profile = await authApi.getProfile();
      console.log('Profile API response:', profile);

      // Update the user state
      setUser(profile);

      // Sync IndexedDB chats to server with improved handling
      console.log('Syncing local chats to server after login');
      try {
        // This will handle both new and existing users appropriately:
        // - For new users: Link all locally stored chat history to their account
        // - For existing users: Merge local and server chats with conflict resolution
        await syncIndexedDBToServer();
        console.log('Successfully synced chat history with server');
      } catch (syncError) {
        console.error('Failed to sync chats after login:', syncError);
        // Don't fail the login if sync fails, but log the error
      }

      return profile;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const logout = () => {
    console.log('Logging out user');
    authApi.logout();
    setUser(null);
    console.log('User state after logout:', null);
  };

  const register = async (username: string, email: string, password: string) => {
    try {
      await authApi.register(username, email, password);
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  };

  const verifyEmail = async (email: string, code: string) => {
    try {
      await authApi.verifyEmail(email, code);
    } catch (error) {
      console.error('Email verification failed:', error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        register,
        verifyEmail,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
