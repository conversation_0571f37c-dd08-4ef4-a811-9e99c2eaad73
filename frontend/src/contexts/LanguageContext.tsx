import React, { createContext, useState, useContext, useEffect } from 'react';

// Define available languages
export type Language = 'zh' | 'en';

// Define translations
export const translations = {
  zh: {
    // App
    appName: 'STEELNET',
    tagline: '',

    // Conversion modes
    imperialToMetric: '英制 → 公制',
    metricToImperial: '公制 → 英制',

    // Actions
    convert: '单位转换',
    table: '制表',
    send: '发送',
    sending: '发送中...',
    working: '工作中...',
    cancel: '取消',

    // Welcome messages
    welcome: '欢迎使用钢铁智联! 我可以帮您整理表格,转换尺寸,计算重量及回答相关问题~',

    // Input placeholders
    inputPlaceholder: '',

    // Result actions
    copy: '复制',
    copyForExcel: '复制为Excel格式',
    copied: '已复制！',
    like: '点赞',
    reportError: '报告错误',
    feedback: '反馈建议',
    sendToEmail: '发送到邮箱',
    downloadExcel: '下载Excel',

    // Email
    emailTitle: '发送转换结果到邮箱',
    emailDescription: '我们将把转换结果发送到您的邮箱，方便您后续查看和使用。',
    emailAddress: '邮箱地址',
    emailPreview: '预览内容',
    emailSuccess: '发送成功！邮件已发出',
    emailError: '发送失败，请稍后再试',

    // Table headers
    steelType: '钢铁类型',
    length: '长度',
    width: '宽度',
    thickness: '厚度',
    weight: '重量',

    // Notifications
    conversionComplete: '转换完成！以下是您的转换结果：',
    conversionFailed: '转换失败，请检查您的输入内容或网络连接。',
    conversionError: '抱歉，转换过程中发生错误。请稍后再试或联系支持团队。',

    // Feedback
    feedbackThanks: '感谢您的反馈！我们将努力改进我们的服务。',
    errorReportThanks: '感谢您报告错误！我们将尽快修复。',

    // Authentication
    login: '登录',
    register: '注册',
    email: '邮箱',
    password: '密码',
    confirmPassword: '确认密码',
    username: '用户名',
    usernameHelp: '请输入您的姓名或昵称',
    companyName: '公司名称',
    companyNameHelp: '请输入您的公司或组织名称',
    country: '国家/地区',
    required: '必填',
    optional: '选填',
    passwordHelp: '密码至少需要6个字符，包含大小写字母和数字',
    passwordsMustMatch: '两次输入的密码不匹配',
    invalidEmail: '邮箱格式不正确',
    verificationCode: '验证码',
    verificationSent: '验证码已发送至您的邮箱',
    verificationFailed: '验证失败',
    verificationSuccess: '邮箱验证成功，请完善您的个人信息',
    loggingIn: '正在登录...',
    syncingChats: '正在同步聊天记录...',
    loginSuccess: '登录成功',
    registerSuccess: '注册成功',
    loginFailed: '登录失败，请检查用户名和密码',
    registerFailed: '注册失败',
    hasAccount: '已有账号？点击登录',
    noAccount: '没有账号？点击注册',
    completeRegistration: '完成注册',
    nextStep: '下一步',
    profile: '个人资料',
    verification: '验证',
    codeSent: '验证码已发送',
    phoneNumber: '电话号码',
    address: '地址',
    city: '城市',
    postalCode: '邮政编码',
    accountType: '账户类型',
    individual: '个人',
    business: '企业',
    logout: '登出',
    logoutConfirm: '确定要登出吗？',
    profileUpdate: '更新个人资料',
    changePassword: '修改密码',
    currentPassword: '当前密码',
    newPassword: '新密码',
    close: '关闭',
    success: '成功',

    // Language Switcher
    changeLanguage: '切换语言',
    chinese: '中文',
    english: 'English',

    // Password Reset
    forgotPassword: '忘记密码？',
    resetPassword: '重置密码',
    resetPasswordInstructions: '请输入您的注册邮箱，我们将发送密码重置验证码给您。',
    sendResetCode: '发送重置验证码',
    resetCodeSent: '重置验证码已发送至您的邮箱。',
    enterVerificationCodeAndNewPassword: '请输入您收到的验证码和新密码。',
    passwordResetSuccess: '密码重置成功！您现在可以使用新密码登录。',
    backToLogin: '返回登录',
    back: '返回',
    emailRequired: '请输入您的邮箱地址',
    verificationCodeRequired: '请输入验证码',
    newPasswordRequired: '请输入新密码',
    passwordsDoNotMatch: '两次输入的密码不匹配',
    passwordTooShort: '密码长度至少为8个字符',
    resetRequestFailed: '密码重置请求失败',
    resetConfirmFailed: '密码重置确认失败',
    requestResetCode: '请求重置验证码',
    verifyAndReset: '验证并重置',

    // Chat Session
    welcomeMessageContent: '欢迎使用钢铁智联! 我可以帮您整理表格,转换尺寸,计算重量及回答相关问题~',
    newConversionTitle: '新对话框',
    newSessionPreview: '新的会话',

    // Countries
    countryCN: '中国',
    countryUS: '美国',
    countryJP: '日本',
    countryKR: '韩国',
    countryDE: '德国',
    countryRU: '俄罗斯',
    countryGB: '英国',
    countryFR: '法国',
    countryIN: '印度',
    countryBR: '巴西',
    countryCA: '加拿大',
    countryAU: '澳大利亚',
    countryIT: '意大利',
    countryES: '西班牙',
    countryMX: '墨西哥',
    countryID: '印度尼西亚',
    countryTR: '土耳其',
    countrySA: '沙特阿拉伯',
    countryZA: '南非',
    countryTH: '泰国',
    countrySG: '新加坡',
    countryMY: '马来西亚',
    countryVN: '越南',
    countryPH: '菲律宾',
    countryAE: '阿联酋',
    countryNL: '荷兰',
    countryBE: '比利时',
    countryPL: '波兰',
    countryCZ: '捷克',
    countryAT: '奥地利',
    countrySE: '瑞典',
    countryNO: '挪威',
    countryDK: '丹麦',
    countryFI: '芬兰',
    countryIE: '爱尔兰',
    countryPT: '葡萄牙',
    countryGR: '希腊',
    countryHU: '匈牙利',
    countryBG: '保加利亚',
    countryRO: '罗马尼亚',
    countryUA: '乌克兰',
    countryEG: '埃及',
    countryNG: '尼日利亚',
    countryAR: '阿根廷',
    countryCL: '智利',
    countryCO: '哥伦比亚',
    countryPE: '秘鲁',
    countryVE: '委内瑞拉',
    countryIL: '以色列',
    countryJO: '约旦',
    countryKW: '科威特',
    countryQA: '卡塔尔',
    countryBH: '巴林',
    countryOM: '阿曼',
    countryLB: '黎巴嫩',
    countryIQ: '伊拉克',
    countryIR: '伊朗',
    countryAF: '阿富汗',
    countryPK: '巴基斯坦',
    countryBD: '孟加拉国',
    countryLK: '斯里兰卡',
    countryMM: '缅甸',
    countryKH: '柬埔寨',
    countryLA: '老挝',
    countryNP: '尼泊尔',
    countryBT: '不丹',
    countryMN: '蒙古',
    countryKZ: '哈萨克斯坦',
    countryUZ: '乌兹别克斯坦',
    countryKG: '吉尔吉斯斯坦',
    countryTJ: '塔吉克斯坦',
    countryTM: '土库曼斯坦',
    countryGE: '格鲁吉亚',
    countryAM: '亚美尼亚',
    countryAZ: '阿塞拜疆',

    // Sidebar
    creatingSession: '创建中...',
    history: '历史记录',
    noHistory: '没有历史记录',
    deleteSessionTooltip: '删除会话',
    converter: '转换器',
    settings: '设置',
    sidebarFooter: '',

    // Functions/Features
    steelIndustryConsultation: '钢铁行业咨询',
    steelIndustryConsultationDesc: '一般钢铁行业知识查询和咨询',
    unitConversion: '单位转换',
    unitConversionDesc: '英制/公制单位转换',
    tabulationFunction: '制表功能',
    tabulationFunctionDesc: '单位转换并生成表格',

    // User interface
    user: '用户',
    signOut: '退出登录',
    signIn: '登录',
    initialWelcomeState: '初始欢迎状态 - 发送消息开始新对话',
    loadingFunctions: '加载功能中...',

    // Email template translations
    emailVerificationTitle: '邮箱验证 - Steel Unit Converter',
    emailVerificationHeader: '钢铁智联 SteelNet',
    emailVerificationSubtitle: '一站式钢铁行业智能助手',
    emailVerificationHeading: '验证您的电子邮箱',
    emailVerificationGreeting: '您好！',
    emailVerificationMessage: '感谢您注册钢铁智联服务平台。为确保账户安全，请使用以下验证码完成注册流程：',
    emailVerificationExpiry: '此验证码将在<strong>30分钟</strong>后失效。',
    emailVerificationIgnore: '如果您没有请求此验证码，请忽略此邮件。可能有人错误地输入了您的电子邮箱地址。',
    emailVerificationFeatures: '注册成功后，您将能够使用我们的单位转换、制表和咨询等功能，助力您的钢铁行业业务。',
    emailVerificationRegards: '诚挚的问候，<br>钢铁智联团队',
    emailVerificationFooter: '此电子邮件由系统自动发送，请勿直接回复。',
    emailVerificationCopyright: '钢铁智联 | SteelNet。保留所有权利。',
  },
  en: {
    // App
    appName: 'STEELNET',
    tagline: '',

    // Conversion modes
    imperialToMetric: 'Imperial → Metric',
    metricToImperial: 'Metric → Imperial',

    // Actions
    convert: 'Convert',
    table: 'Table',
    send: 'Send',
    sending: 'Sending...',
    working: 'Working...',
    cancel: 'Cancel',

    // Welcome messages
    welcome: 'Welcome to Steelnet.ai, what can I help you ?',

    // Input placeholders
    inputPlaceholder: '',

    // Result actions
    copy: 'Copy',
    copyForExcel: 'Copy for Excel',
    copied: 'Copied!',
    like: 'Like',
    reportError: 'Report Error',
    feedback: 'Feedback',
    sendToEmail: 'Send to Email',
    downloadExcel: 'Download Excel',

    // Email
    emailTitle: 'Send Conversion Result to Email',
    emailDescription: 'We will send the conversion result to your email for future reference.',
    emailAddress: 'Email Address',
    emailPreview: 'Preview',
    emailSuccess: 'Success! Email has been sent',
    emailError: 'Sending failed, please try again later',

    // Table headers
    steelType: 'Steel Type',
    length: 'Length',
    width: 'Width',
    thickness: 'Thickness',
    weight: 'Weight',

    // Notifications
    conversionComplete: 'Conversion complete! Here are your results:',
    conversionFailed: 'Conversion failed. Please check your input or network connection.',
    conversionError: 'Sorry, an error occurred during the conversion. Please try again later or contact support.',

    // Feedback
    feedbackThanks: 'Thank you for your feedback! We will work to improve our service.',
    errorReportThanks: 'Thank you for reporting this error! We will fix it as soon as possible.',

    // Authentication
    login: 'Login',
    register: 'Register',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    username: 'Username',
    usernameHelp: 'Please enter your name or nickname',
    companyName: 'Company Name',
    companyNameHelp: 'Please enter your company or organization name',
    country: 'Country/Region',
    required: 'Required',
    optional: 'Optional',
    passwordHelp: 'Password must be at least 6 characters, including uppercase, lowercase letters and numbers',
    passwordsMustMatch: 'Passwords must match',
    invalidEmail: 'Invalid email format',
    verificationCode: 'Verification Code',
    verificationSent: 'Verification code has been sent to your email',
    verificationFailed: 'Verification failed',
    verificationSuccess: 'Email verified successfully, please complete your profile',
    loggingIn: 'Logging in...',
    syncingChats: 'Syncing chat history...',
    loginSuccess: 'Login successful',
    registerSuccess: 'Registration successful',
    loginFailed: 'Login failed, please check your credentials',
    registerFailed: 'Registration failed',
    hasAccount: 'Already have an account? Login',
    noAccount: 'Don\'t have an account? Register',
    completeRegistration: 'Complete Registration',
    nextStep: 'Next',
    profile: 'Profile',
    verification: 'Verification',
    codeSent: 'Code sent',
    phoneNumber: 'Phone Number',
    address: 'Address',
    city: 'City',
    postalCode: 'Postal Code',
    accountType: 'Account Type',
    individual: 'Individual',
    business: 'Business',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to logout?',
    profileUpdate: 'Update Profile',
    changePassword: 'Change Password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    close: 'Close',
    success: 'Success',

    // Language Switcher
    changeLanguage: 'Change Language',
    chinese: '中文',
    english: 'English',

    // Password Reset
    forgotPassword: 'Forgot Password?',
    resetPassword: 'Reset Password',
    resetPasswordInstructions: 'Please enter your registered email, we will send you a password reset verification code.',
    sendResetCode: 'Send Reset Code',
    resetCodeSent: 'Reset verification code has been sent to your email.',
    enterVerificationCodeAndNewPassword: 'Please enter the verification code and new password you received.',
    passwordResetSuccess: 'Password reset successful! You can now use the new password to login.',
    backToLogin: 'Back to Login',
    back: 'Back',
    emailRequired: 'Please enter your email address',
    verificationCodeRequired: 'Please enter the verification code',
    newPasswordRequired: 'Please enter the new password',
    passwordsDoNotMatch: 'Passwords do not match',
    passwordTooShort: 'Password must be at least 8 characters',
    resetRequestFailed: 'Password reset request failed',
    resetConfirmFailed: 'Password reset confirmation failed',
    requestResetCode: 'Request Reset Code',
    verifyAndReset: 'Verify and Reset',

    // Chat Session
    welcomeMessageContent: 'Welcome to Steelnet.ai, what can I help you ?',
    newConversionTitle: 'NEW CHAT',
    newSessionPreview: 'New Session',

    // Countries
    countryCN: 'China',
    countryUS: 'United States',
    countryJP: 'Japan',
    countryKR: 'South Korea',
    countryDE: 'Germany',
    countryRU: 'Russia',
    countryGB: 'United Kingdom',
    countryFR: 'France',
    countryIN: 'India',
    countryBR: 'Brazil',
    countryCA: 'Canada',
    countryAU: 'Australia',
    countryIT: 'Italy',
    countryES: 'Spain',
    countryMX: 'Mexico',
    countryID: 'Indonesia',
    countryTR: 'Turkey',
    countrySA: 'Saudi Arabia',
    countryZA: 'South Africa',
    countryTH: 'Thailand',
    countrySG: 'Singapore',
    countryMY: 'Malaysia',
    countryVN: 'Vietnam',
    countryPH: 'Philippines',
    countryAE: 'United Arab Emirates',
    countryNL: 'Netherlands',
    countryBE: 'Belgium',
    countryPL: 'Poland',
    countryCZ: 'Czech Republic',
    countryAT: 'Austria',
    countrySE: 'Sweden',
    countryNO: 'Norway',
    countryDK: 'Denmark',
    countryFI: 'Finland',
    countryIE: 'Ireland',
    countryPT: 'Portugal',
    countryGR: 'Greece',
    countryHU: 'Hungary',
    countryBG: 'Bulgaria',
    countryRO: 'Romania',
    countryUA: 'Ukraine',
    countryEG: 'Egypt',
    countryNG: 'Nigeria',
    countryAR: 'Argentina',
    countryCL: 'Chile',
    countryCO: 'Colombia',
    countryPE: 'Peru',
    countryVE: 'Venezuela',
    countryIL: 'Israel',
    countryJO: 'Jordan',
    countryKW: 'Kuwait',
    countryQA: 'Qatar',
    countryBH: 'Bahrain',
    countryOM: 'Oman',
    countryLB: 'Lebanon',
    countryIQ: 'Iraq',
    countryIR: 'Iran',
    countryAF: 'Afghanistan',
    countryPK: 'Pakistan',
    countryBD: 'Bangladesh',
    countryLK: 'Sri Lanka',
    countryMM: 'Myanmar',
    countryKH: 'Cambodia',
    countryLA: 'Laos',
    countryNP: 'Nepal',
    countryBT: 'Bhutan',
    countryMN: 'Mongolia',
    countryKZ: 'Kazakhstan',
    countryUZ: 'Uzbekistan',
    countryKG: 'Kyrgyzstan',
    countryTJ: 'Tajikistan',
    countryTM: 'Turkmenistan',
    countryGE: 'Georgia',
    countryAM: 'Armenia',
    countryAZ: 'Azerbaijan',

    // Sidebar
    creatingSession: 'Creating...',
    history: 'History',
    noHistory: 'No history',
    deleteSessionTooltip: 'Delete session',
    converter: 'Converter',
    settings: 'Settings',
    sidebarFooter: '',

    // Functions/Features
    steelIndustryConsultation: 'Steel Industry Consultation',
    steelIndustryConsultationDesc: 'General steel industry knowledge queries and consultation',
    unitConversion: 'Unit Conversion',
    unitConversionDesc: 'Imperial/Metric unit conversion',
    tabulationFunction: 'Tabulation Function',
    tabulationFunctionDesc: 'Unit conversion with table generation',

    // User interface
    user: 'User',
    signOut: 'Sign Out',
    signIn: 'Sign In',
    initialWelcomeState: 'Initial welcome state - Send a message to start a new conversation',
    loadingFunctions: 'Loading functions...',

    // Email template translations
    emailVerificationTitle: 'Email Verification - Steel Unit Converter',
    emailVerificationHeader: 'SteelNet',
    emailVerificationSubtitle: 'Professional Steel Industry Intelligence Assistant',
    emailVerificationHeading: 'Verify Your Email Address',
    emailVerificationGreeting: 'Hello!',
    emailVerificationMessage: 'Thank you for registering with SteelNet. To ensure account security, please use the following verification code to complete the registration process:',
    emailVerificationExpiry: 'This verification code will expire in <strong>30 minutes</strong>.',
    emailVerificationIgnore: 'If you did not request this verification code, please ignore this email. Someone may have mistakenly entered your email address.',
    emailVerificationFeatures: 'After successful registration, you will be able to use our unit conversion, table generation, and consulting features to support your steel industry business.',
    emailVerificationRegards: 'Best regards,<br>The SteelNet Team',
    emailVerificationFooter: 'This email was sent automatically. Please do not reply directly.',
    emailVerificationCopyright: 'SteelNet. All rights reserved.',
  }
};

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: keyof typeof translations['zh']) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get initially preferred language from localStorage or browser language
  const getInitialLanguage = (): Language => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'zh' || savedLanguage === 'en')) {
      return savedLanguage;
    }

    // Browser language detection
    const browserLang = navigator.language.split('-')[0];
    return browserLang === 'zh' ? 'zh' : 'en';
  };

  const [language, setLanguageState] = useState<Language>(getInitialLanguage);

  // Translator function
  const t = (key: keyof typeof translations['zh']): string => {
    return translations[language][key] || key;
  };

  // Set language and save to localStorage
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    localStorage.setItem('language', lang);
  };

  const value = {
    language,
    setLanguage,
    t,
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export default LanguageContext;