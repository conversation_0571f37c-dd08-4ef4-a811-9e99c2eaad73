export const authApi = {
  async register(userData: RegisterData) {
    const { data } = await axiosInstance.post<RegisterResponse>('/register', userData);
    return data;
  },

  async requestVerification(email: string) {
    const { data } = await axiosInstance.post<{message: string, _meta?: {note?: string}}>('/request-verification', { email });
    return data;
  },

  async verifyEmail(verificationData: VerifyEmailData) {
    const { data } = await axiosInstance.post<{message: string}>('/verify', verificationData);
    return data;
  },
}; 