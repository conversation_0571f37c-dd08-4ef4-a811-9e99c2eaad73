import axios from 'axios';

const API_URL = '/api';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Add response interceptor for better error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export interface ConversionResult {
  steel_type?: string;
  length?: { value: number; unit: string };
  width?: { value: number; unit: string };
  thickness?: { value: number; unit: string };
  weight?: { value: number; unit: string };
}

export interface User {
  username: string;
  email: string;
  is_verified: boolean;
  is_paid: boolean;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  username: string;
  email: string;
  company_name?: string;
  country?: string;
}

export interface ConversionHistory {
  id: number;
  steel_type: string;
  input_length: number;
  input_width: number;
  input_thickness: number;
  input_weight: number;
  input_unit_system: string;
  output_unit_system: string;
  created_at: string;
}

export interface RegistrationData {
  username: string;
  email: string;
  password: string;
  company?: string;
  country?: string;
  phone?: string;
}

export interface EmailVerificationData {
  email: string;
  code: string;
}

export const conversionApi = {
  convert: async (text: string, unitSystem: 'metric' | 'imperial') => {
    const response = await api.post<ConversionResult>('/convert', {
      text,
      unit_system: unitSystem,
    });
    return response.data;
  },

  getHistory: async () => {
    const response = await api.get<ConversionHistory[]>('/conversion-history');
    return response.data;
  },
};

export const authApi = {
  register: async (data: RegistrationData) => {
    const response = await api.post('/auth/register', data);
    return response.data;
  },

  requestVerification: async (email: string) => {
    const response = await api.post('/auth/request-verification', { email });
    return response.data;
  },

  verifyEmail: async (data: EmailVerificationData) => {
    const response = await api.post('/auth/verify-email', data);
    return response.data;
  },

  login: async (email: string, password: string) => {
    const response = await api.post<LoginResponse>('/auth/login', {
      email,
      password,
    });
    // Store token in localStorage
    localStorage.setItem('token', response.data.access_token);
    return response.data;
  },

  logout: () => {
    localStorage.removeItem('token');
  },

  getProfile: async () => {
    const response = await api.get<User>('/auth/profile');
    return response.data;
  },
};

export default api; 