import { openDB, IDBPDatabase } from 'idb';
import { ChatSession } from '../types/chatSession';
import { Message } from '../types/chat';
import { v4 as uuidv4 } from 'uuid';

// Database name and version
const DB_NAME = 'steel_converter_chat_db';
const DB_VERSION = 1;

// Store names
const CHAT_STORE = 'chats';
const META_STORE = 'meta';

// Interface for database
interface ChatDB {
  chats: {
    key: string;
    value: ChatSession;
    indexes: { 'by-lastUpdated': Date };
  };
  meta: {
    key: string;
    value: any;
  };
}

// Initialize the database
const initDB = async (): Promise<IDBPDatabase<ChatDB>> => {
  return openDB<ChatDB>(DB_NAME, DB_VERSION, {
    upgrade(db) {
      // Create stores if they don't exist
      if (!db.objectStoreNames.contains(CHAT_STORE)) {
        const chatStore = db.createObjectStore(CHAT_STORE, { keyPath: 'id' });
        chatStore.createIndex('by-lastUpdated', 'lastUpdated', { unique: false });
      }

      if (!db.objectStoreNames.contains(META_STORE)) {
        db.createObjectStore(META_STORE, { keyPath: 'key' });
      }
    },
  });
};

// Get all chat sessions
export const getAllChats = async (): Promise<ChatSession[]> => {
  try {
    const db = await initDB();
    const tx = db.transaction(CHAT_STORE, 'readonly');
    const store = tx.objectStore(CHAT_STORE);
    const index = store.index('by-lastUpdated');

    // Get all chats sorted by lastUpdated (newest first)
    const chats = await index.getAll();
    return chats.reverse();
  } catch (error) {
    console.error('Error getting chats from IndexedDB:', error);
    return [];
  }
};

// Get a single chat session
export const getChat = async (id: string): Promise<ChatSession | null> => {
  try {
    const db = await initDB();
    const chat = await db.get(CHAT_STORE, id);
    return chat || null;
  } catch (error) {
    console.error(`Error getting chat ${id} from IndexedDB:`, error);
    return null;
  }
};

// Save a chat session
export const saveChat = async (chat: ChatSession): Promise<boolean> => {
  try {
    console.log('IndexedDB: Saving chat session:', chat);
    const db = await initDB();

    // Ensure the chat object has all required fields
    const chatToSave = {
      ...chat,
      id: chat.id || uuidv4(), // Ensure there's an ID
      lastUpdated: new Date(), // Ensure lastUpdated is a Date object
      messages: Array.isArray(chat.messages) ? chat.messages : [], // Ensure messages is an array
      title: chat.title || 'New Chat', // Ensure there's a title
      preview: chat.preview || '' // Ensure there's a preview
    };

    await db.put(CHAT_STORE, chatToSave);
    console.log('IndexedDB: Successfully saved chat session:', chatToSave);
    return true;
  } catch (error) {
    console.error('Error saving chat to IndexedDB:', error);
    return false;
  }
};

// Delete a chat session
export const deleteChat = async (id: string): Promise<boolean> => {
  try {
    const db = await initDB();
    await db.delete(CHAT_STORE, id);
    return true;
  } catch (error) {
    console.error(`Error deleting chat ${id} from IndexedDB:`, error);
    return false;
  }
};

// Add a message to a chat session
export const addMessageToChat = async (chatId: string, message: Message): Promise<ChatSession | null> => {
  try {
    console.log(`IndexedDB: Adding message to chat ${chatId}`, message);
    const db = await initDB();
    const tx = db.transaction(CHAT_STORE, 'readwrite');
    const store = tx.objectStore(CHAT_STORE);

    // Get the chat
    let chat = await store.get(chatId);

    // If chat doesn't exist, create a new one
    if (!chat) {
      console.warn(`IndexedDB: Chat ${chatId} not found in database, creating a new one`);
      chat = {
        id: chatId,
        title: message.type === 'user' ? message.content.substring(0, 20) + '...' : 'New Chat',
        lastUpdated: new Date(),
        messages: [],
        preview: message.type === 'user' ? message.content.substring(0, 50) : '',
        function: 'general'
      };
    } else {
      console.log(`IndexedDB: Found chat ${chatId}`, chat);
    }

    // Ensure messages is an array
    if (!Array.isArray(chat.messages)) {
      console.warn(`IndexedDB: Chat ${chatId} has invalid messages property, resetting to empty array`);
      chat.messages = [];
    }

    // Add the message
    const updatedChat = {
      ...chat,
      messages: [...chat.messages, message],
      lastUpdated: new Date(),
      // Update preview if it's a user message
      preview: message.type === 'user' ? message.content.substring(0, 50) : chat.preview,
    };

    // Save the updated chat
    await store.put(updatedChat);
    await tx.done;

    console.log(`IndexedDB: Successfully updated chat ${chatId}`, updatedChat);
    return updatedChat;
  } catch (error) {
    console.error(`Error adding message to chat ${chatId} in IndexedDB:`, error);
    return null;
  }
};

// Debug function to check database status
export const debugIndexedDB = async (): Promise<void> => {
  try {
    const db = await initDB();
    const tx = db.transaction(CHAT_STORE, 'readonly');
    const store = tx.objectStore(CHAT_STORE);

    // Get all chats
    const chats = await store.getAll();
    console.log(`IndexedDB Debug - Found ${chats.length} chats in database`);

    // Log details about each chat
    chats.forEach(chat => {
      const hasUserMessages = chat.messages.some(msg => msg.type === 'user');
      console.log(`Chat ${chat.id}: ${chat.title} - ${chat.messages.length} messages, has user messages: ${hasUserMessages}`);
      console.log('Chat messages:', chat.messages);
    });

    // Get active chat
    const metaTx = db.transaction(META_STORE, 'readonly');
    const metaStore = metaTx.objectStore(META_STORE);
    const activeChat = await metaStore.get('activeChat');
    console.log('IndexedDB Debug - Active chat ID:', activeChat ? activeChat.value : 'none');

    // If there's an active chat, get its details
    if (activeChat && activeChat.value) {
      const activeChatData = await store.get(activeChat.value);
      if (activeChatData) {
        console.log('IndexedDB Debug - Active chat details:', {
          id: activeChatData.id,
          title: activeChatData.title,
          messageCount: activeChatData.messages.length,
          hasUserMessages: activeChatData.messages.some(msg => msg.type === 'user')
        });
        console.log('Active chat messages:', activeChatData.messages);
      } else {
        console.log(`IndexedDB Debug - Active chat ID ${activeChat.value} not found in database`);
      }
    }

    // Add a global function to check IndexedDB from browser console
    (window as any).checkIndexedDB = async () => {
      try {
        const db = await initDB();
        const tx = db.transaction(CHAT_STORE, 'readonly');
        const store = tx.objectStore(CHAT_STORE);
        const chats = await store.getAll();
        console.log('All chats in IndexedDB:', chats);

        const metaTx = db.transaction(META_STORE, 'readonly');
        const metaStore = metaTx.objectStore(META_STORE);
        const activeChat = await metaStore.get('activeChat');
        console.log('Active chat ID:', activeChat ? activeChat.value : 'none');

        return { chats, activeChat };
      } catch (error) {
        console.error('Error checking IndexedDB:', error);
        return null;
      }
    };
    console.log('Added global checkIndexedDB() function - run this in browser console to check database');

    return;
  } catch (error) {
    console.error('Error debugging IndexedDB:', error);
  }
};

// Set active chat ID
export const setActiveChat = async (id: string): Promise<boolean> => {
  try {
    const db = await initDB();
    await db.put(META_STORE, { key: 'activeChat', value: id });
    return true;
  } catch (error) {
    console.error('Error setting active chat in IndexedDB:', error);
    return false;
  }
};

// Get active chat ID
export const getActiveChat = async (): Promise<string | null> => {
  try {
    const db = await initDB();
    const meta = await db.get(META_STORE, 'activeChat');
    return meta ? meta.value : null;
  } catch (error) {
    console.error('Error getting active chat from IndexedDB:', error);
    return null;
  }
};

// Clear all data
export const clearAllData = async (): Promise<boolean> => {
  try {
    const db = await initDB();
    const tx = db.transaction([CHAT_STORE, META_STORE], 'readwrite');
    await Promise.all([
      tx.objectStore(CHAT_STORE).clear(),
      tx.objectStore(META_STORE).clear(),
    ]);
    await tx.done;
    return true;
  } catch (error) {
    console.error('Error clearing IndexedDB data:', error);
    return false;
  }
};

// Export all chats as JSON
export const exportChats = async (): Promise<string> => {
  try {
    const chats = await getAllChats();
    return JSON.stringify(chats);
  } catch (error) {
    console.error('Error exporting chats from IndexedDB:', error);
    return '[]';
  }
};

// Import chats from JSON
export const importChats = async (json: string): Promise<boolean> => {
  try {
    const chats = JSON.parse(json) as ChatSession[];
    const db = await initDB();
    const tx = db.transaction(CHAT_STORE, 'readwrite');
    const store = tx.objectStore(CHAT_STORE);

    // Clear existing chats
    await store.clear();

    // Add imported chats
    for (const chat of chats) {
      await store.add({
        ...chat,
        lastUpdated: new Date(chat.lastUpdated), // Ensure lastUpdated is a Date object
      });
    }

    await tx.done;
    return true;
  } catch (error) {
    console.error('Error importing chats to IndexedDB:', error);
    return false;
  }
};
