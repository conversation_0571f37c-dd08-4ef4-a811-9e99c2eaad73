import { Message } from '../types/chat';
import Cookies from 'js-cookie';
import { API_BASE_URL } from '../config';

// Constants
const GUEST_CHAT_COOKIE = 'steel_converter_guest_chat';
const MAX_COOKIE_SIZE_BYTES = 4000; // Safe size for cookies

// Function to check if user is logged in
const isLoggedIn = (): boolean => {
  return !!localStorage.getItem('token');
};

// Utilities for compression (basic, can be improved with proper compression libs)
const compressMessages = (messages: Message[]): string => {
  // Keep only the essential data before storing
  const compressedMessages = messages.map(msg => ({
    type: msg.type,
    content: msg.content,
    timestamp: msg.timestamp,
    function: msg.function,
    result: msg.result ? {
      converted_text: msg.result.converted_text,
      original_text: msg.result.original_text,
      hasTable: msg.result.hasTable,
      function: msg.result.function
    } : undefined,
  }));

  return JSON.stringify(compressedMessages);
};

const decompressMessages = (data: string): Message[] => {
  try {
    const parsed = JSON.parse(data);
    if (!Array.isArray(parsed)) return [];

    // Convert string timestamps back to Date objects
    return parsed.map(msg => ({
      ...msg,
      timestamp: new Date(msg.timestamp),
    }));
  } catch (e) {
    console.error('Failed to decompress messages:', e);
    return [];
  }
};

// Local storage for guest users
const saveMessagesToLocal = (messages: Message[]): void => {
  try {
    const compressed = compressMessages(messages);

    // Check if the data is too large for a cookie
    if (compressed.length > MAX_COOKIE_SIZE_BYTES) {
      // If too large, keep only the most recent messages
      saveMessagesToLocal(messages.slice(-10));
      return;
    }

    Cookies.set(GUEST_CHAT_COOKIE, compressed, { expires: 7 }); // Store for 7 days
  } catch (e) {
    console.error('Failed to save chat messages to cookie:', e);
  }
};

const loadMessagesFromLocal = (): Message[] => {
  try {
    const data = Cookies.get(GUEST_CHAT_COOKIE);
    if (!data) return [];

    return decompressMessages(data);
  } catch (e) {
    console.error('Failed to load chat messages from cookie:', e);
    return [];
  }
};

// API calls for logged-in users
const saveMessagesToAPI = async (messages: Message[]): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/chat/history`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({ messages: compressMessages(messages) }),
    });

    return response.ok;
  } catch (e) {
    console.error('Failed to save chat messages to API:', e);
    return false;
  }
};

const loadMessagesFromAPI = async (): Promise<Message[]> => {
  try {
    const response = await fetch(`${API_BASE_URL}/chat/history`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
    });

    if (!response.ok) return [];

    const data = await response.json();
    return decompressMessages(data.messages || '[]');
  } catch (e) {
    console.error('Failed to load chat messages from API:', e);
    return [];
  }
};

// Public API
export const saveMessages = async (messages: Message[]): Promise<void> => {
  if (isLoggedIn()) {
    await saveMessagesToAPI(messages);
  } else {
    saveMessagesToLocal(messages);
  }
};

export const loadMessages = async (): Promise<Message[]> => {
  if (isLoggedIn()) {
    return await loadMessagesFromAPI();
  } else {
    return loadMessagesFromLocal();
  }
};

// Function to clear messages (logout scenario)
export const clearMessages = (): void => {
  Cookies.remove(GUEST_CHAT_COOKIE);
};