@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --animation-timing: 0.2s;
  --animation-easing: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  /* ChatGPT-style font stack */
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;

  /* ChatGPT colors */
  --gpt-bg-primary: #0f0f0f;
  --gpt-bg-secondary: #1a1a1a;
  --gpt-bg-sidebar: #0c0c0c;
  --gpt-text-primary: #ececec;
  --gpt-text-secondary: #8e8ea0;
  --gpt-accent: #e5e5e5;
  --gpt-accent-hover: #d4d4d4;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Inter", system-ui, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  height: 100vh;
  margin: 0;
  background-color: var(--gpt-bg-primary);
  color: var(--gpt-text-primary);
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  height: 100vh;
  width: 100%;
}

a {
  font-weight: 500;
  color: var(--gpt-accent);
  text-decoration: none;
  transition: color var(--animation-timing) var(--animation-easing);
}

a:hover {
  color: var(--gpt-accent-hover);
}

h1 {
  font-size: 2rem;
  line-height: 1.2;
  font-weight: 600;
}

h2 {
  font-size: 1.75rem;
  line-height: 1.2;
  font-weight: 600;
}

h3 {
  font-size: 1.5rem;
  line-height: 1.3;
  font-weight: 600;
}

button {
  border-radius: 6px;
  border: 1px solid transparent;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all var(--animation-timing) var(--animation-easing);
  background-color: var(--gpt-accent);
  color: white;
}

button:hover {
  background-color: var(--gpt-accent-hover);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Focus visible styles for accessibility */
:focus-visible {
  outline: 2px solid var(--gpt-accent);
  outline-offset: 2px;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gpt-bg-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background-color: #4a4a4a;
  border-radius: 3px;
  border: 1px solid var(--gpt-bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
  background-color: #5a5a5a;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  body {
    font-size: 14px;
  }
  
  h1 {
    font-size: 1.75rem;
  }
  
  h2 {
    font-size: 1.5rem;
  }
  
  h3 {
    font-size: 1.25rem;
  }
}

/* Fix for iOS height issues */
@supports (-webkit-touch-callout: none) {
  body,
  #root {
    min-height: -webkit-fill-available;
  }
}

/* Loading animation */
.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Smooth transitions for theme changes */
* {
  transition: background-color var(--animation-timing) var(--animation-easing),
              color var(--animation-timing) var(--animation-easing),
              border-color var(--animation-timing) var(--animation-easing);
}

/* Selection styling */
::selection {
  background-color: rgba(16, 163, 127, 0.3);
  color: var(--gpt-text-primary);
}
