import { createTheme } from '@mui/material/styles';

declare module '@mui/material/styles' {
  interface Palette {
    customGradient: {
      main: string;
      gradient: string;
    };
    sidebarBackground?: string;
    sidebarText?: string;
    sidebarTextSecondary?: string;
    sidebarHoverBg?: string;
    sidebarSelectedBg?: string;
    inputBackground?: string;
    inputBorder?: string;
    inputFocusBorder?: string;
  }
  
  interface PaletteOptions {
    customGradient?: {
      main?: string;
      gradient?: string;
    };
    sidebarBackground?: string;
    sidebarText?: string;
    sidebarTextSecondary?: string;
    sidebarHoverBg?: string;
    sidebarSelectedBg?: string;
    inputBackground?: string;
    inputBorder?: string;
    inputFocusBorder?: string;
  }
}

// ChatGPT-style font stack - prioritizing Segoe UI for Windows, SF Pro for macOS, Inter as fallback
const FONT_FAMILY_GPT = '"Segoe UI", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Inter", system-ui, Helvetica, Arial, sans-serif';

// Define a common base for light and dark modes with GPT-style typography
const commonThemeSettings = {
  typography: {
    fontFamily: FONT_FAMILY_GPT,
    fontSize: 14,
    h1: { fontWeight: 600, fontSize: '2rem', lineHeight: 1.2 },
    h2: { fontWeight: 600, fontSize: '1.75rem', lineHeight: 1.2 },
    h3: { fontWeight: 600, fontSize: '1.5rem', lineHeight: 1.3 },
    h4: { fontWeight: 600, fontSize: '1.25rem', lineHeight: 1.3 },
    h5: { fontWeight: 500, fontSize: '1.1rem', lineHeight: 1.4 },
    h6: { fontWeight: 500, fontSize: '1rem', lineHeight: 1.4 },
    subtitle1: { fontWeight: 500, fontSize: '0.875rem', lineHeight: 1.4 },
    subtitle2: { fontWeight: 400, fontSize: '0.8rem', lineHeight: 1.4 },
    body1: { fontSize: '0.875rem', lineHeight: 1.5, fontWeight: 400 },
    body2: { fontSize: '0.8rem', lineHeight: 1.5, fontWeight: 400 },
    button: {
      fontWeight: 500,
      fontSize: '0.875rem',
      textTransform: 'none' as const,
      letterSpacing: '0.02em',
    },
  },
  shape: {
    borderRadius: 6, // GPT uses smaller border radius
  },
};

// Color palette converted from Zinc oklch to RGB/hex
const grayColors = {
  50: '#fafafa',    // Very light gray
  100: '#f5f5f5',   // Light gray
  200: '#e5e5e5',   // Medium-light gray
  300: '#d4d4d4',   // Light-medium gray
  400: '#a3a3a3',   // Medium gray
  500: '#737373',   // Medium-dark gray
  600: '#525252',   // Dark gray
  700: '#404040',   // Darker gray
  800: '#262626',   // Very dark gray
  900: '#171717',   // Nearly black
  950: '#0a0a0a',   // Almost black
};

// Specific colors from the reference image converted to hex
const zincGrayColors = {
  lightest: '#ebebeb',  // oklch(0.92 0.004 286.32)
  light: '#b4b4b4',     // oklch(0.705 0.015 286.067)
  medium: '#8c8c8c',    // oklch(0.552 0.016 285.938)
  mediumDark: '#707070', // oklch(0.442 0.017 285.786)
  dark: '#363636',      // oklch(0.21 0.006 285.885)
  darkest: '#242424',   // oklch(0.141 0.005 285.823)
};

// Steel theme based on the image - dark with grayscale accents
const theme = createTheme({
  ...commonThemeSettings,
  palette: {
    mode: 'dark',
    primary: {
      main: zincGrayColors.mediumDark, // #707070
      light: zincGrayColors.medium,    // #8c8c8c
      dark: zincGrayColors.dark,       // #363636
      contrastText: zincGrayColors.lightest, // #ebebeb
    },
    secondary: {
      main: zincGrayColors.medium,     // #8c8c8c
      light: zincGrayColors.light,     // #b4b4b4
      dark: zincGrayColors.dark,       // #363636
      contrastText: zincGrayColors.lightest, // #ebebeb
    },
    error: {
      main: '#ef4444', // Modern red
    },
    warning: {
      main: '#f59e0b', // Modern amber
    },
    info: {
      main: '#3b82f6', // Modern blue
    },
    success: {
      main: '#22c55e', // Modern green (only for success states)
    },
    background: {
      default: zincGrayColors.darkest, // #242424 - Almost black background
      paper: '#1a1a1a',               // Very dark gray for cards/panels
    },
    text: {
      primary: zincGrayColors.lightest,   // #ebebeb - White text
      secondary: zincGrayColors.light,    // #b4b4b4 - Light gray text
    },
    divider: zincGrayColors.dark,         // #363636
    action: {
      hover: 'rgba(255, 255, 255, 0.08)',
      selected: 'rgba(255, 255, 255, 0.12)',
      disabled: 'rgba(255, 255, 255, 0.26)',
    },
    // Sophisticated neutral UI colors based on image
    sidebarBackground: '#0a0a0a', // Very dark sidebar
    sidebarText: '#ffffff',
    sidebarTextSecondary: '#888888',
    sidebarHoverBg: 'rgba(255, 255, 255, 0.08)', // Gray hover
    sidebarSelectedBg: 'rgba(255, 255, 255, 0.12)', // Gray selected background
    inputBackground: '#333333', // Input background
    inputBorder: 'rgba(255, 255, 255, 0.15)',
    inputFocusBorder: zincGrayColors.light, // Gray focus border
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
          scrollbarWidth: thin;
          scrollbarColor: #4a4a4a #2f2f2f;
          font-family: ${FONT_FAMILY_GPT};
          background-color: ${zincGrayColors.darkest};
          color: ${zincGrayColors.lightest};
        }
        body::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        body::-webkit-scrollbar-track {
          background: #2f2f2f;
          border-radius: 3px;
        }
        body::-webkit-scrollbar-thumb {
          background-color: #4a4a4a;
          border-radius: 3px;
          border: 1px solid #2f2f2f;
        }
        body::-webkit-scrollbar-thumb:hover {
          background-color: #5a5a5a;
        }
      `,
    },
    MuiButton: {
      styleOverrides: {
        root: {
          boxShadow: 'none',
          textTransform: 'none',
          padding: '8px 16px',
          borderRadius: '6px',
          fontWeight: 500,
          '&:hover': {
            boxShadow: 'none',
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
          },
        },
        containedPrimary: {
          backgroundColor: zincGrayColors.dark,
          color: zincGrayColors.lightest,
          '&:hover': {
            backgroundColor: zincGrayColors.mediumDark,
          },
          '&:disabled': {
            backgroundColor: 'rgba(255, 255, 255, 0.3)',
            color: 'rgba(255, 255, 255, 0.5)',
          },
        },
        outlinedPrimary: {
          borderColor: zincGrayColors.mediumDark,
          color: zincGrayColors.light,
          '&:hover': {
            borderColor: zincGrayColors.medium,
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
          },
        },
        textPrimary: {
          color: zincGrayColors.light,
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: 'none',
          backgroundColor: '#1a1a1a',
          color: zincGrayColors.lightest,
          borderBottom: `1px solid ${zincGrayColors.dark}`,
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.5)',
          backgroundColor: '#1a1a1a',
          backgroundImage: 'none',
          borderColor: zincGrayColors.dark,
        },
        rounded: {
          borderRadius: 6,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.3)',
          backgroundColor: '#1a1a1a',
          borderRadius: 6,
          backgroundImage: 'none',
          border: `1px solid ${zincGrayColors.dark}`,
        },
      },
    },
    MuiTextField: {
      defaultProps: {
        variant: 'outlined',
      },
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: '6px',
            backgroundColor: '#1a1a1a',
            color: zincGrayColors.lightest,
            fontSize: '0.875rem',
            '& fieldset': {
              borderColor: zincGrayColors.mediumDark,
            },
            '&:hover fieldset': {
              borderColor: zincGrayColors.medium,
            },
            '&.Mui-focused fieldset': {
              borderColor: zincGrayColors.light,
            },
            '&.Mui-disabled': {
              backgroundColor: 'rgba(255,255,255,0.03)',
              color: 'rgba(255,255,255,0.3)',
              '& fieldset': {
                borderColor: 'rgba(255,255,255,0.06)',
              }
            }
          },
          '& .MuiInputLabel-root': {
            color: zincGrayColors.medium,
            fontSize: '0.875rem',
            '&.Mui-focused': {
              color: zincGrayColors.light,
            },
            '&.Mui-disabled': {
              color: 'rgba(255,255,255,0.3)',
            }
          },
          '& .MuiInputBase-input': {
            color: zincGrayColors.lightest,
          },
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 6,
          color: zincGrayColors.lightest,
          padding: '8px 12px',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
          },
          '&.Mui-selected': {
            backgroundColor: 'rgba(255, 255, 255, 0.12)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.16)',
            },
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          fontWeight: 500,
          borderRadius: '12px',
          color: zincGrayColors.lightest,
          backgroundColor: zincGrayColors.dark,
          '&.MuiChip-outlined': {
            borderColor: zincGrayColors.mediumDark,
            color: zincGrayColors.light,
            backgroundColor: 'transparent',
          }
        },
      },
    },
    MuiAvatar: {
      styleOverrides: {
        root: {
          fontSize: '0.875rem',
          fontWeight: 500,
          backgroundColor: zincGrayColors.dark,
          color: zincGrayColors.lightest,
        },
      },
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: zincGrayColors.dark,
          borderRadius: '6px',
          fontSize: '0.75rem',
          color: zincGrayColors.lightest,
          border: '1px solid rgba(255, 255, 255, 0.06)',
        },
        arrow: {
          color: zincGrayColors.dark,
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          backgroundColor: zincGrayColors.dark,
          backgroundImage: 'none',
        }
      }
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          color: zincGrayColors.medium,
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
            color: zincGrayColors.light,
          },
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: zincGrayColors.darkest,
          borderRight: `1px solid ${zincGrayColors.dark}`,
        },
      },
    },
  },
});

export default theme;
