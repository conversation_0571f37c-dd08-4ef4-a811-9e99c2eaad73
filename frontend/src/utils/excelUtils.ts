import * as XLSX from 'xlsx';

/**
 * Convert a markdown table to Excel file and trigger download
 * @param tableData The markdown table content
 * @param fileName The name of the file to download
 */
export const convertMarkdownTableToExcel = (tableData: string, fileName: string = 'conversion_table.xlsx'): void => {
  try {
    // Parse the markdown table
    const { headers, rows } = parseMarkdownTable(tableData);
    
    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(rows);
    
    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Conversion');
    
    // Generate Excel file and trigger download
    XLSX.writeFile(workbook, fileName);
  } catch (error) {
    console.error('Error converting table to Excel:', error);
  }
};

/**
 * Convert a JSON table to Excel file and trigger download
 * @param tableData The table data in JSON format
 * @param fileName The name of the file to download
 */
export const convertJsonTableToExcel = (tableData: any, fileName: string = 'conversion_table.xlsx'): void => {
  try {
    // Create worksheet from the table data
    const worksheet = XLSX.utils.json_to_sheet([tableData]);
    
    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Conversion');
    
    // Generate Excel file and trigger download
    XLSX.writeFile(workbook, fileName);
  } catch (error) {
    console.error('Error converting JSON to Excel:', error);
  }
};

/**
 * Parse a markdown table into headers and rows
 * @param markdownTable The markdown table content
 * @returns An object with headers and rows
 */
export const parseMarkdownTable = (markdownTable: string): { headers: string[], rows: any[] } => {
  // Split the table into lines
  const lines = markdownTable.trim().split('\n');
  
  // Find table rows (lines that start with |)
  const tableRows = lines.filter(line => line.trim().startsWith('|'));
  
  if (tableRows.length < 2) {
    throw new Error('Invalid markdown table format');
  }
  
  // Extract headers from the first row
  const headerRow = tableRows[0];
  const headers = headerRow
    .split('|')
    .filter(cell => cell.trim() !== '')
    .map(cell => cell.trim());
  
  // Skip the separator row (second row)
  
  // Extract data rows
  const dataRows = tableRows.slice(2);
  const rows = dataRows.map(row => {
    const cells = row
      .split('|')
      .filter(cell => cell.trim() !== '')
      .map(cell => cell.trim());
    
    // Create an object with header keys and cell values
    const rowObject: Record<string, string> = {};
    headers.forEach((header, index) => {
      rowObject[header] = cells[index] || '';
    });
    
    return rowObject;
  });
  
  return { headers, rows };
};
