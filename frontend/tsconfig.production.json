{
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.production.tsbuildinfo",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    
    /* Skip all type checking */
    "skipLibCheck": true,
    "noEmit": true,
    
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "jsx": "react-jsx",
    
    /* Disable strict type checking */
    "strict": false,
    "noImplicitAny": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    "noUncheckedSideEffectImports": false,
    "allowJs": true,
    "checkJs": false
  },
  "include": ["src"],
  "exclude": ["node_modules"]
}
