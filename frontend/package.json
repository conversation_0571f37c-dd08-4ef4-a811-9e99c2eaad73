{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build", "build:low-memory": "NODE_OPTIONS=--max-old-space-size=256 vite build --mode production", "build:minimal": "NODE_OPTIONS=--max-old-space-size=256 vite build --mode production --minify=false", "lint": "eslint .", "preview": "vite preview --host 0.0.0.0"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.4.4", "@mui/material": "^6.4.4", "@reduxjs/toolkit": "^2.5.1", "@types/js-cookie": "^3.0.6", "@types/uuid": "^10.0.0", "axios": "^1.7.9", "date-fns": "^2.30.0", "idb": "^8.0.2", "js-cookie": "^3.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^6.20.0", "serve": "^14.2.4", "uuid": "^9.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}