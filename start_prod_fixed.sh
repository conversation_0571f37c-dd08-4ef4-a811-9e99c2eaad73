#!/usr/bin/env bash

# Fixed Production Startup Script for Steel Unit Converter
# This script fixes the database connection issues and starts the application in production mode

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the current directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
LOG_DIR="$SCRIPT_DIR/logs"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to kill existing processes
kill_existing() {
    print_info "Killing existing processes..."

    # Kill backend processes
    BACKEND_PIDS=$(ps aux | grep "uvicorn main:app\|gunicorn main:app" | grep -v grep | awk '{print $2}')
    if [ -n "$BACKEND_PIDS" ]; then
        print_info "Killing existing backend processes..."
        for PID in $BACKEND_PIDS; do
            kill -9 $PID 2>/dev/null || true
        done
    fi

    # Kill frontend processes - check for both npm preview and serve
    FRONTEND_PIDS=$(ps aux | grep "npm run preview\|serve -s dist" | grep -v grep | awk '{print $2}')
    if [ -n "$FRONTEND_PIDS" ]; then
        print_info "Killing existing frontend processes..."
        for PID in $FRONTEND_PIDS; do
            kill -9 $PID 2>/dev/null || true
        done
    fi

    # Also check for any processes on port 3000
    PORT_3000_PID=$(lsof -t -i:3000 2>/dev/null)
    if [ -n "$PORT_3000_PID" ]; then
        print_info "Killing process on port 3000..."
        kill -9 $PORT_3000_PID 2>/dev/null || true
    fi

    # Also check for any processes on port 8000
    PORT_8000_PID=$(lsof -t -i:8000 2>/dev/null)
    if [ -n "$PORT_8000_PID" ]; then
        print_info "Killing process on port 8000..."
        kill -9 $PORT_8000_PID 2>/dev/null || true
    fi

    print_success "All existing processes killed."
}

# Function to check and install required packages
install_packages() {
    print_info "Installing required packages..."

    # Check for system package manager
    if command -v apt-get &> /dev/null; then
        print_info "Using apt-get to install system dependencies..."
        sudo apt-get update
        sudo apt-get install -y python3-dev libpq-dev default-libmysqlclient-dev build-essential python3-pip python3-venv mysql-client
    elif command -v yum &> /dev/null; then
        print_info "Using yum to install system dependencies..."
        sudo yum install -y python3-devel postgresql-devel mysql-devel gcc python3-pip python3-virtualenv mysql
    else
        print_warning "Could not determine package manager. Please install required packages manually."
    fi

    # Create virtual environment if it doesn't exist
    if [ ! -d "$BACKEND_DIR/venv" ]; then
        print_info "Creating virtual environment..."
        cd "$BACKEND_DIR"
        python3 -m venv venv
    fi

    # Activate virtual environment
    source "$BACKEND_DIR/venv/bin/activate"

    # Install Python dependencies
    print_info "Installing Python dependencies..."
    pip install --upgrade pip
    pip install -r "$BACKEND_DIR/requirements.txt"
    pip install pymysql aiomysql sqlalchemy gunicorn uvicorn pydantic-settings==1.2.0

    print_success "Required packages installed."
}

# Function to ensure .env files are properly set up
setup_env_files() {
    print_info "Setting up environment files..."

    # Check if .env file exists in root directory
    if [ ! -f "$SCRIPT_DIR/.env" ]; then
        print_info ".env file not found in root directory. Creating from .env.production..."
        if [ -f "$SCRIPT_DIR/.env.production" ]; then
            cp "$SCRIPT_DIR/.env.production" "$SCRIPT_DIR/.env"
            print_success "Created .env file from .env.production."
        else
            print_error ".env.production file not found. Cannot create .env file."
            return 1
        fi
    fi

    # Check if .env file exists in backend directory
    if [ ! -f "$BACKEND_DIR/.env" ]; then
        print_info ".env file not found in backend directory. Creating from root .env..."
        cp "$SCRIPT_DIR/.env" "$BACKEND_DIR/.env"
        print_success "Created backend .env file."
    fi

    # Create a Python script to fix database connection
    print_info "Creating script to fix database connection..."
    cat > "$SCRIPT_DIR/fix_db_connection.py" << 'EOF'
#!/usr/bin/env python3
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Print environment variables
print("Environment variables:")
print(f"RDS_HOSTNAME: {os.getenv('RDS_HOSTNAME')}")
print(f"RDS_PORT: {os.getenv('RDS_PORT')}")
print(f"RDS_DB_NAME: {os.getenv('RDS_DB_NAME')}")
print(f"RDS_USERNAME: {os.getenv('RDS_USERNAME')}")
print(f"DATABASE_URL: {os.getenv('DATABASE_URL')}")

# Create backend .env file
backend_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend')
backend_env_path = os.path.join(backend_dir, '.env')

with open(backend_env_path, 'w') as f:
    f.write("# Database configuration - MySQL 8.0 RDS\n")
    f.write(f"RDS_HOSTNAME={os.getenv('RDS_HOSTNAME', '')}\n")
    f.write(f"RDS_PORT={os.getenv('RDS_PORT', '3306')}\n")
    f.write(f"RDS_DB_NAME={os.getenv('RDS_DB_NAME', '')}\n")
    f.write(f"RDS_USERNAME={os.getenv('RDS_USERNAME', '')}\n")
    f.write(f"RDS_PASSWORD={os.getenv('RDS_PASSWORD', '')}\n")
    f.write("\n")
    f.write("# Database connection pool settings (optimized for MySQL 8.0 RDS)\n")
    f.write(f"DB_POOL_SIZE={os.getenv('DB_POOL_SIZE', '20')}\n")
    f.write(f"DB_MAX_OVERFLOW={os.getenv('DB_MAX_OVERFLOW', '30')}\n")
    f.write(f"DB_POOL_TIMEOUT={os.getenv('DB_POOL_TIMEOUT', '30')}\n")
    f.write(f"DB_POOL_RECYCLE={os.getenv('DB_POOL_RECYCLE', '1800')}\n")
    f.write(f"SQL_ECHO={os.getenv('SQL_ECHO', 'False')}\n")
    f.write("\n")
    f.write("# Environment\n")
    f.write("ENV=production\n")
    f.write("DEBUG=False\n")

print(f"Created backend .env file at {backend_env_path}")
EOF

    # Make the script executable
    chmod +x "$SCRIPT_DIR/fix_db_connection.py"

    # Run the script
    print_info "Running database connection fix script..."
    python3 "$SCRIPT_DIR/fix_db_connection.py"

    print_success "Environment files set up successfully."
}

# Function to initialize the database
initialize_database() {
    print_info "Initializing the database..."

    # Run the init-database.py script
    if [ -f "$SCRIPT_DIR/init-database.py" ]; then
        print_info "Running init-database.py..."
        python3 "$SCRIPT_DIR/init-database.py" --env-file "$SCRIPT_DIR/.env"
    else
        print_error "init-database.py not found."
        return 1
    fi

    # Run the migrate-database.py script
    if [ -f "$SCRIPT_DIR/migrate-database.py" ]; then
        print_info "Running migrate-database.py..."
        python3 "$SCRIPT_DIR/migrate-database.py" --env-file "$SCRIPT_DIR/.env"
    else
        print_error "migrate-database.py not found."
        return 1
    fi

    print_success "Database initialization completed."
}

# Function to start the backend
start_backend() {
    print_info "Starting the backend..."

    # Change directory to backend
    cd "$BACKEND_DIR"

    # Activate virtual environment
    source venv/bin/activate

    # Set environment variables
    export ENV="production"
    export DEBUG="False"

    # Start the backend with Gunicorn
    print_info "Starting backend with Gunicorn..."
    gunicorn main:app \
        --worker-class uvicorn.workers.UvicornWorker \
        --workers 2 \
        --threads 4 \
        --bind 0.0.0.0:8000 \
        --max-requests 500 \
        --max-requests-jitter 50 \
        --timeout 120 \
        --keep-alive 5 \
        --log-level warning \
        --worker-tmp-dir /dev/shm \
        --preload \
        > "$LOG_DIR/backend.log" 2>&1 &

    BACKEND_PID=$!
    echo $BACKEND_PID > "$LOG_DIR/backend.pid"
    print_success "Backend started with PID: $BACKEND_PID"
    print_info "Backend logs available at: $LOG_DIR/backend.log"
}

# Function to start the frontend
start_frontend() {
    print_info "Starting the frontend..."

    # Change directory to frontend
    cd "$FRONTEND_DIR"

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_info "Installing frontend dependencies..."
        npm install
    fi

    # Build the frontend
    print_info "Building the frontend..."
    npm run build

    # Start the frontend server
    print_info "Starting frontend server..."
    serve -s dist -l tcp://0.0.0.0:3000 --no-clipboard --single --cors > "$LOG_DIR/frontend.log" 2>&1 &

    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$LOG_DIR/frontend.pid"
    print_success "Frontend started with PID: $FRONTEND_PID"
    print_info "Frontend logs available at: $LOG_DIR/frontend.log"
}

# Main function
main() {
    print_info "Starting fixed production startup script..."

    # Kill existing processes
    kill_existing

    # Install required packages
    install_packages

    # Set up environment files
    setup_env_files

    # Initialize the database
    initialize_database

    # Start the backend
    start_backend

    # Start the frontend
    start_frontend

    print_success "Application started successfully."
    print_info "Backend: http://localhost:8000"
    print_info "Frontend: http://localhost:3000"
}

# Run the main function
main
