#!/usr/bin/env python3
"""
Test script to verify chat history storage functionality
Tests both user and AI message storage in the chat system
"""

import asyncio
import json
import aiohttp
import time
from typing import Dict, Any, List

# Configuration
BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:5173"

async def test_chat_history_storage():
    """Test the complete chat history storage flow"""
    print("🧪 Testing Chat History Storage Functionality")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # Test 1: Send a user message and get AI response
        print("\n📝 Test 1: Sending user message and checking AI response storage")
        
        test_message = "Convert 1 inch to mm"
        
        try:
            # Send message to LLM endpoint
            llm_payload = {
                "text": test_message,
                "unit_system": "metric",
                "function": "conversion",
                "model": "deepseek-v3-250324"
            }
            
            print(f"Sending message: '{test_message}'")
            
            async with session.post(
                f"{BASE_URL}/llm",
                json=llm_payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ LLM response received successfully")
                    print(f"Response: {result.get('result', {}).get('converted_text', 'No converted text')[:100]}...")
                    
                    # Check if the response contains expected conversion
                    converted_text = result.get('result', {}).get('converted_text', '')
                    if 'mm' in converted_text and ('25.4' in converted_text or '25,4' in converted_text):
                        print("✅ Conversion result looks correct")
                    else:
                        print("⚠️ Conversion result may be incorrect")
                        print(f"Full response: {converted_text}")
                else:
                    print(f"❌ LLM request failed with status: {response.status}")
                    error_text = await response.text()
                    print(f"Error: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Error testing LLM endpoint: {e}")
            return False
        
        # Test 2: Test streaming endpoint
        print("\n📡 Test 2: Testing streaming endpoint")
        
        try:
            streaming_payload = {
                "text": "Convert 2 feet to meters",
                "unit_system": "metric",
                "function": "conversion"
            }
            
            print(f"Sending streaming message: '{streaming_payload['text']}'")
            
            async with session.post(
                f"{BASE_URL}/llm/stream",
                json=streaming_payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    print("✅ Streaming response started")
                    
                    # Read streaming response
                    accumulated_content = ""
                    chunk_count = 0
                    
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            data = line_str[6:]
                            if data.strip() == '[DONE]':
                                break
                            try:
                                chunk_data = json.loads(data)
                                chunk_count += 1
                                
                                if chunk_data.get('type') == 'content':
                                    accumulated_content += chunk_data.get('content', '')
                                elif chunk_data.get('type') == 'completion':
                                    print("✅ Streaming completed successfully")
                                    result = chunk_data.get('result', {})
                                    final_text = result.get('converted_text', accumulated_content)
                                    print(f"Final result: {final_text[:100]}...")
                                    
                                    # Check if conversion looks correct
                                    if 'meter' in final_text.lower() and ('0.6' in final_text or '0,6' in final_text):
                                        print("✅ Streaming conversion result looks correct")
                                    else:
                                        print("⚠️ Streaming conversion result may be incorrect")
                                        print(f"Full result: {final_text}")
                                    break
                                    
                            except json.JSONDecodeError:
                                continue
                    
                    print(f"📊 Processed {chunk_count} streaming chunks")
                    
                else:
                    print(f"❌ Streaming request failed with status: {response.status}")
                    error_text = await response.text()
                    print(f"Error: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Error testing streaming endpoint: {e}")
            return False
        
        # Test 3: Test table functionality with testCase.txt data
        print("\n📊 Test 3: Testing table functionality with test data")
        
        test_table_data = """S/S 430 BA NO PI
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#"""
        
        try:
            table_payload = {
                "text": test_table_data,
                "unit_system": "metric",
                "function": "conversion",
                "table_mode": True
            }
            
            print("Sending table conversion request...")
            
            async with session.post(
                f"{BASE_URL}/llm",
                json=table_payload,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ Table conversion response received")
                    
                    converted_text = result.get('result', {}).get('converted_text', '')
                    has_table = result.get('result', {}).get('hasTable', False)
                    
                    print(f"Has table: {has_table}")
                    
                    # Check for table indicators
                    if '<table_stream>' in converted_text or '|' in converted_text:
                        print("✅ Table format detected in response")
                    else:
                        print("⚠️ No table format detected")
                        
                    print(f"Response preview: {converted_text[:200]}...")
                    
                else:
                    print(f"❌ Table conversion failed with status: {response.status}")
                    error_text = await response.text()
                    print(f"Error: {error_text}")
                    return False
                    
        except Exception as e:
            print(f"❌ Error testing table functionality: {e}")
            return False
        
        print("\n🎉 All tests completed successfully!")
        return True

async def test_frontend_integration():
    """Test frontend integration and user interaction features"""
    print("\n🌐 Testing Frontend Integration")
    print("=" * 30)
    
    # This would require browser automation to fully test
    # For now, we'll just verify the frontend is accessible
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(FRONTEND_URL) as response:
                if response.status == 200:
                    print("✅ Frontend is accessible")
                    content = await response.text()
                    
                    # Check for key elements
                    if 'Steel Unit Converter' in content:
                        print("✅ Frontend title found")
                    if 'chat' in content.lower():
                        print("✅ Chat interface elements found")
                    
                    return True
                else:
                    print(f"❌ Frontend not accessible: {response.status}")
                    return False
                    
        except Exception as e:
            print(f"❌ Error accessing frontend: {e}")
            return False

async def main():
    """Main test function"""
    print("🚀 Starting Chat History Storage Tests")
    print("=" * 60)
    
    # Test backend functionality
    backend_success = await test_chat_history_storage()
    
    # Test frontend accessibility
    frontend_success = await test_frontend_integration()
    
    print("\n📋 Test Summary")
    print("=" * 20)
    print(f"Backend Tests: {'✅ PASSED' if backend_success else '❌ FAILED'}")
    print(f"Frontend Tests: {'✅ PASSED' if frontend_success else '❌ FAILED'}")
    
    if backend_success and frontend_success:
        print("\n🎉 All tests PASSED! The chat history storage functionality appears to be working correctly.")
        print("\n📝 Next steps:")
        print("1. Test user interaction features (copy, like, report) in the browser")
        print("2. Verify that AI responses are properly stored in chat history")
        print("3. Test with the specific test cases from testCase.txt")
    else:
        print("\n❌ Some tests FAILED. Please check the logs and fix the issues.")
    
    return backend_success and frontend_success

if __name__ == "__main__":
    asyncio.run(main())
