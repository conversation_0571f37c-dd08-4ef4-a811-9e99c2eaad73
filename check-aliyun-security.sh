#!/bin/bash

# <PERSON><PERSON><PERSON> to check and fix Alibaba Cloud ECS security group settings
# This script checks if ports 80 and 443 are open in the security group

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section header
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Function to print success message
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print warning message
print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Function to print error message
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to check if a port is open from the internet
check_port_from_internet() {
    local port=$1
    local description=$2
    local public_ip="************"
    
    print_header "Checking if port $port ($description) is open from the internet"
    
    # Try to connect to the port from an external service
    echo "Testing connection to $public_ip:$port..."
    
    # Use curl to check if the port is open
    if curl -s --connect-timeout 5 -o /dev/null $public_ip:$port; then
        print_success "Port $port is accessible from the internet"
        return 0
    else
        print_error "Port $port is NOT accessible from the internet"
        return 1
    fi
}

# Function to check Alibaba Cloud security group settings
check_aliyun_security() {
    print_header "Checking Alibaba Cloud Security Group Settings"
    
    # Check if aliyun CLI is installed
    if command -v aliyun &> /dev/null; then
        print_success "Aliyun CLI is installed"
        
        # Try to get security group information
        echo "Fetching security group information..."
        aliyun ecs DescribeSecurityGroups
        
        # Ask for security group ID
        echo -e "\nPlease enter your security group ID from the above list:"
        read -r SECURITY_GROUP_ID
        
        # Get security group rules
        echo "Fetching security group rules for $SECURITY_GROUP_ID..."
        aliyun ecs DescribeSecurityGroupAttribute --SecurityGroupId $SECURITY_GROUP_ID
        
        # Check if ports 80 and 443 are allowed
        if aliyun ecs DescribeSecurityGroupAttribute --SecurityGroupId $SECURITY_GROUP_ID | grep -E "Port.*80|Port.*443" | grep -q "Accept"; then
            print_success "Ports 80 and 443 are allowed in the security group"
        else
            print_error "Ports 80 and 443 may not be allowed in the security group"
            
            # Ask if user wants to add the rules
            echo -e "\nWould you like to add rules for ports 80 and 443? (y/n)"
            read -r ADD_RULES
            
            if [[ "$ADD_RULES" =~ ^[Yy]$ ]]; then
                echo "Adding rule for port 80..."
                aliyun ecs AuthorizeSecurityGroup --SecurityGroupId $SECURITY_GROUP_ID --IpProtocol tcp --PortRange 80/80 --SourceCidrIp 0.0.0.0/0
                
                echo "Adding rule for port 443..."
                aliyun ecs AuthorizeSecurityGroup --SecurityGroupId $SECURITY_GROUP_ID --IpProtocol tcp --PortRange 443/443 --SourceCidrIp 0.0.0.0/0
                
                print_success "Rules added successfully"
            fi
        fi
    else
        print_error "Aliyun CLI is not installed"
        print_warning "You need to check security group settings manually in the Alibaba Cloud console"
        echo "Please follow these steps:"
        echo "1. Log in to the Alibaba Cloud console"
        echo "2. Go to Elastic Compute Service (ECS)"
        echo "3. Click on 'Security Groups' in the left menu"
        echo "4. Find the security group associated with your instance"
        echo "5. Click on 'Add Rules' and add the following rules:"
        echo "   - Protocol: TCP, Port Range: 80/80, Authorization Object: 0.0.0.0/0"
        echo "   - Protocol: TCP, Port Range: 443/443, Authorization Object: 0.0.0.0/0"
    fi
}

# Function to check instance metadata
check_instance_metadata() {
    print_header "Checking Instance Metadata"
    
    # Try to get instance metadata
    echo "Fetching instance metadata..."
    
    # Check if we can access the metadata service
    if curl -s http://***************/latest/meta-data/ > /dev/null; then
        print_success "Metadata service is accessible"
        
        # Get instance ID
        INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
        echo "Instance ID: $INSTANCE_ID"
        
        # Get region ID
        REGION_ID=$(curl -s http://***************/latest/meta-data/region-id)
        echo "Region ID: $REGION_ID"
        
        # Get private IP
        PRIVATE_IP=$(curl -s http://***************/latest/meta-data/private-ipv4)
        echo "Private IP: $PRIVATE_IP"
        
        # Get public IP
        PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
        echo "Public IP: $PUBLIC_IP"
        
        # Check if the public IP matches our expected IP
        if [ "$PUBLIC_IP" = "************" ]; then
            print_success "Public IP matches expected IP"
        else
            print_warning "Public IP ($PUBLIC_IP) does not match expected IP (************)"
            echo "You may need to update the PUBLIC_IP variable in your scripts"
        fi
    else
        print_error "Metadata service is not accessible"
        print_warning "This may not be an Alibaba Cloud instance, or the metadata service is disabled"
    fi
}

# Function to check network connectivity
check_network() {
    print_header "Checking Network Connectivity"
    
    # Check if we can reach the internet
    if ping -c 1 google.com > /dev/null 2>&1; then
        print_success "Internet connectivity is working"
    else
        print_error "Internet connectivity is not working"
    fi
    
    # Check if we can reach our public IP
    if ping -c 1 ************ > /dev/null 2>&1; then
        print_success "Public IP is reachable"
    else
        print_warning "Public IP is not reachable via ping (this may be normal if ICMP is blocked)"
    fi
    
    # Check if ports 80 and 443 are open from the internet
    check_port_from_internet 80 "HTTP"
    check_port_from_internet 443 "HTTPS"
}

# Main function
main() {
    print_header "Alibaba Cloud ECS Security Check"
    echo "This script will check if ports 80 and 443 are open in the security group"
    
    # Check if running as root
    if [ "$EUID" -eq 0 ]; then
        print_warning "Running as root. Some checks may not work correctly."
    fi
    
    # Check instance metadata
    check_instance_metadata
    
    # Check network connectivity
    check_network
    
    # Check Alibaba Cloud security group settings
    check_aliyun_security
    
    print_header "Summary"
    echo "If ports 80 and 443 are not accessible from the internet, please check your security group settings"
    echo "You can also try the following:"
    echo "1. Restart nginx: sudo systemctl restart nginx"
    echo "2. Check nginx configuration: sudo nginx -t"
    echo "3. Check nginx logs: sudo tail -f /var/log/nginx/error.log"
    echo "4. Restart the application: ./start-prod.sh --restart --url-access"
}

# Run the main function
main
