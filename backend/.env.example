# Database configuration
DATABASE_URL=postgresql+asyncpg://user:password@localhost/dbname

# Aliyun RDS MySQL configuration
RDS_HOSTNAME=your-rds-instance.mysql.rds.aliyuncs.com
RDS_PORT=3306
RDS_DB_NAME=your_database_name
RDS_USERNAME=your_rds_username
RDS_PASSWORD=your_rds_password

# Database connection pool settings
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800

# Security
SECRET_KEY=your-secret-key-change-this
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# Email settings
SMTP_SERVER=smtp.example.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password

# Sentry error tracking (optional)
SENTRY_DSN=

# LLM service
VOLCANO_API_KEY=your-api-key
VOLCANO_ENDPOINT_ID=deepseek-v3-250324

# Aliyun OSS configuration
ALIYUN_OSS_ACCESS_KEY_ID=your-access-key-id
ALIYUN_OSS_ACCESS_KEY_SECRET=your-access-key-secret
ALIYUN_OSS_ENDPOINT=https://oss-cn-beijing.aliyuncs.com
ALIYUN_OSS_BUCKET_NAME=your-bucket-name
ALIYUN_OSS_PREFIX=chat_histories/ 