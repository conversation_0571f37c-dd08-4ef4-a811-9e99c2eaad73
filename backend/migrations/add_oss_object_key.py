"""
Migration script to add oss_object_key column to chat_histories table
"""
from sqlalchemy import Column, String, MetaData, Table
from alembic import op
import sqlalchemy as sa

def upgrade():
    """Add oss_object_key column to chat_histories table"""
    op.add_column('chat_histories', Column('oss_object_key', String(255), nullable=True))

def downgrade():
    """Remove oss_object_key column from chat_histories table"""
    op.drop_column('chat_histories', 'oss_object_key')
