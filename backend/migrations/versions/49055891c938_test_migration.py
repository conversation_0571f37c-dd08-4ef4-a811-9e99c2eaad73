"""test_migration

Revision ID: 49055891c938
Revises: 
Create Date: 2025-03-27 23:30:38.056985

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '49055891c938'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('email', sa.String(length=100), nullable=False),
    sa.Column('hashed_password', sa.String(length=100), nullable=False),
    sa.Column('company_name', sa.String(length=100), nullable=True),
    sa.Column('country', sa.String(length=100), nullable=True),
    sa.Column('user_type', sa.Enum('FREE', 'PAID', name='usertype'), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_admin', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
    sa.Column('conversion_count', sa.Integer(), nullable=False),
    sa.Column('preferences', sa.JSON(), nullable=True),
    sa.Column('profile_image_url', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_users_created_at', 'users', ['created_at'], unique=False)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index('ix_users_username_email', 'users', ['username', 'email'], unique=False)
    op.create_table('chat_histories',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('session_id', sa.String(length=50), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=True),
    sa.Column('message_count', sa.Integer(), nullable=False),
    sa.Column('last_message_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('is_archived', sa.Boolean(), nullable=False),
    sa.Column('content', sa.JSON(), nullable=True),
    sa.Column('meta_data', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_chat_histories_session_id'), 'chat_histories', ['session_id'], unique=False)
    op.create_index('ix_chat_histories_user_id_created_at', 'chat_histories', ['user_id', 'created_at'], unique=False)
    op.create_table('conversions',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('input_text', sa.Text(), nullable=False),
    sa.Column('from_unit', sa.String(length=50), nullable=True),
    sa.Column('to_unit', sa.String(length=50), nullable=True),
    sa.Column('input_value', sa.Float(), nullable=True),
    sa.Column('output_value', sa.Float(), nullable=True),
    sa.Column('unit_system', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('query_function', sa.String(length=50), nullable=True),
    sa.Column('result_text', sa.Text(), nullable=True),
    sa.Column('meta_data', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_conversions_unit_system', 'conversions', ['unit_system'], unique=False)
    op.create_index('ix_conversions_user_id_created_at', 'conversions', ['user_id', 'created_at'], unique=False)
    op.create_table('verification_codes',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('email', sa.String(length=100), nullable=False),
    sa.Column('code', sa.String(length=10), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('is_used', sa.Boolean(), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('password_hash', sa.String(length=100), nullable=False),
    sa.Column('company_name', sa.String(length=100), nullable=True),
    sa.Column('country', sa.String(length=100), nullable=True),
    sa.Column('user_id', sa.String(length=36), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_verification_codes_email'), 'verification_codes', ['email'], unique=False)
    op.create_index('ix_verification_codes_email_code', 'verification_codes', ['email', 'code'], unique=False)
    op.create_index('ix_verification_codes_expires_at', 'verification_codes', ['expires_at'], unique=False)
    op.create_table('chat_likes',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.String(length=36), nullable=False),
    sa.Column('chat_id', sa.String(length=36), nullable=False),
    sa.Column('message_index', sa.Integer(), nullable=False),
    sa.Column('is_liked', sa.Boolean(), nullable=False),
    sa.Column('feedback', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['chat_id'], ['chat_histories.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_chat_likes_chat_id_message_index', 'chat_likes', ['chat_id', 'message_index'], unique=False)
    op.create_index('ix_chat_likes_user_id', 'chat_likes', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_chat_likes_user_id', table_name='chat_likes')
    op.drop_index('ix_chat_likes_chat_id_message_index', table_name='chat_likes')
    op.drop_table('chat_likes')
    op.drop_index('ix_verification_codes_expires_at', table_name='verification_codes')
    op.drop_index('ix_verification_codes_email_code', table_name='verification_codes')
    op.drop_index(op.f('ix_verification_codes_email'), table_name='verification_codes')
    op.drop_table('verification_codes')
    op.drop_index('ix_conversions_user_id_created_at', table_name='conversions')
    op.drop_index('ix_conversions_unit_system', table_name='conversions')
    op.drop_table('conversions')
    op.drop_index('ix_chat_histories_user_id_created_at', table_name='chat_histories')
    op.drop_index(op.f('ix_chat_histories_session_id'), table_name='chat_histories')
    op.drop_table('chat_histories')
    op.drop_index('ix_users_username_email', table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index('ix_users_created_at', table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###
