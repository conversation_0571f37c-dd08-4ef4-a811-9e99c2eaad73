"""add_oss_object_key_to_chat_histories

Revision ID: d5be61a668c2
Revises: 49055891c938
Create Date: 2025-04-18 14:23:58.890951

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd5be61a668c2'
down_revision: Union[str, None] = '49055891c938'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('chat_histories', sa.Column('oss_object_key', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('chat_histories', 'oss_object_key')
    # ### end Alembic commands ###
