"""
MySQL optimization adapter for SQLAlchemy models.
This script applies MySQL-specific optimizations to the SQLAlchemy models at runtime.
It should be imported after the models are defined but before the database is initialized.
"""

from sqlalchemy import Index
import logging

# Import the models
from models.user_model import User
from models.chat_history import Cha<PERSON><PERSON><PERSON><PERSON>, ChatLike
from models.conversion_model import Conversion
from models.auth_model import VerificationCode

# Set up logging
logger = logging.getLogger(__name__)

def apply_mysql_optimizations():
    """Apply MySQL-specific optimizations to the models."""
    # Apply MySQL table options to all tables
    tables = [User, ChatHistory, ChatLike, Conversion, VerificationCode]
    for table in tables:
        # Add MySQL-specific table options
        table.__table__.kwargs.update({
            'mysql_engine': 'InnoDB',
            'mysql_charset': 'utf8mb4',
            'mysql_collate': 'utf8mb4_unicode_ci',
            'mysql_row_format': 'DYNAMIC'
        })
    
    # Add full-text search indexes for MySQL
    try:
        ChatHistory.__table__.append_constraint(
            Index('ix_chat_histories_title_fulltext', ChatHistory.title, mysql_prefix='FULLTEXT')
        )
        
        User.__table__.append_constraint(
            Index('ix_users_username_fulltext', User.username, mysql_prefix='FULLTEXT')
        )
        
        logger.info("MySQL optimizations applied successfully.")
    except Exception as e:
        logger.error(f"Error applying MySQL optimizations: {e}")
    
    return True

def mysql_json_to_dict(json_data):
    """Convert MySQL JSON data to Python dictionary."""
    if json_data is None:
        return {}
    
    # If it's already a dict, return it
    if isinstance(json_data, dict):
        return json_data
    
    # Try to parse the JSON string
    import json
    try:
        return json.loads(json_data)
    except (TypeError, json.JSONDecodeError):
        return {}

# Call this function when the application starts if using MySQL
if __name__ == "__main__":
    apply_mysql_optimizations()