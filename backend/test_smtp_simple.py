import smtplib
from email.mime.text import MIMEText
from email.header import <PERSON><PERSON>
import sys

# Aliyun Enterprise Mail settings
smtp_server = 'smtp.qiye.aliyun.com'
smtp_port = 465
username = '<EMAIL>'
password = 'STEELnet456456'

# Set up email
recipient = input("Enter recipient email: ")
if not recipient:
    print("No recipient provided, using <EMAIL>")
    recipient = "<EMAIL>"

msg = MIMEText('This is a test email from the Steel Unit Converter application.', 'plain', 'utf-8')
msg['Subject'] = Header('SMTP Test', 'utf-8')
msg['From'] = f'钢铁智联 <{username}>'
msg['To'] = recipient

# Connect and authenticate with detailed error handling
try:
    print(f"\nConnecting to {smtp_server}:{smtp_port} using SSL...")
    server = smtplib.SMTP_SSL(smtp_server, smtp_port)
    server.set_debuglevel(1)  # Enable verbose debug output
    
    print("\nServer info:")
    print(f"EHLO response: {server.ehlo()}")
    
    print(f"\nAttempting login with username: {username}")
    server.login(username, password)
    print("Login successful!")
    
    print(f"\nSending test email to {recipient}...")
    server.sendmail(username, recipient, msg.as_string())
    print("Email sent successfully!")
    
except smtplib.SMTPAuthenticationError as e:
    print(f"\nAuthentication Error: {e}")
    print("Possible causes:")
    print("1. Incorrect username or password")
    print("2. Account security settings (check for 2FA or app-specific passwords)")
    print("3. Service restrictions (check Aliyun console for restrictions)")
    
except smtplib.SMTPConnectError as e:
    print(f"\nConnection Error: {e}")
    print("Possible causes:")
    print("1. Incorrect server or port")
    print("2. Firewall blocking connection")
    print("3. Server down or maintenance")
    
except Exception as e:
    print(f"\nError: {e}")
    import traceback
    traceback.print_exc()
    
finally:
    try:
        server.quit()
        print("\nSMTP connection closed.")
    except:
        pass 
 