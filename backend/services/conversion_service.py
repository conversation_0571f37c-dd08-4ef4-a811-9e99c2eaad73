import re
from typing import Dict, List, Tuple
import aiohttp
import os
from dotenv import load_dotenv

load_dotenv()

class ConversionService:
    def __init__(self):
        self.volcengine_api_key = os.getenv("VOLCENGINE_API_KEY")
        self.conversion_factors = {
            "length": {
                "in_to_mm": 25.4,
                "mm_to_in": 1/25.4
            },
            "weight": {
                "lb_to_kg": 0.453592,
                "kg_to_lb": 1/0.453592
            }
        }

    async def convert_text(self, text: str, direction: str) -> str:
        if direction not in ["M", "I"]:
            raise ValueError("Direction must be 'M' for Imperial to Metric or 'I' for Metric to Imperial")

        # Split text into lines for processing
        lines = text.split("\n")
        converted_lines = []

        for line in lines:
            if not line.strip():
                converted_lines.append(line)
                continue

            # Convert measurements in the line
            converted_line = await self._process_line(line, direction)
            converted_lines.append(converted_line)

        return "\n".join(converted_lines)

    async def _process_line(self, line: str, direction: str) -> str:
        # Process fractions (e.g., 3/16")
        line = self._convert_fractions(line, direction)
        
        # Process decimal measurements
        line = self._convert_decimals(line, direction)
        
        # Process weights
        line = self._convert_weights(line, direction)
        
        return line

    def _convert_fractions(self, text: str, direction: str) -> str:
        def fraction_to_mm(match):
            num = float(match.group(1))
            denom = float(match.group(2))
            inches = num / denom
            mm = inches * self.conversion_factors["length"]["in_to_mm"]
            return f"{mm:.2f}"

        if direction == "M":
            # Convert fractions like 3/16" to mm
            pattern = r'(\d+)/(\d+)"'
            text = re.sub(pattern, fraction_to_mm, text)
        
        return text

    def _convert_decimals(self, text: str, direction: str) -> str:
        def decimal_to_metric(match):
            value = float(match.group(1))
            if direction == "M":
                return f"{value * self.conversion_factors['length']['in_to_mm']:.2f}"
            else:
                return f"{value * self.conversion_factors['length']['mm_to_in']:.2f}"

        # Convert decimal measurements
        pattern = r'(\d+\.?\d*)"' if direction == "M" else r'(\d+\.?\d*)(?:mm|MM)'
        text = re.sub(pattern, decimal_to_metric, text)
        
        return text

    def _convert_weights(self, text: str, direction: str) -> str:
        def weight_to_metric(match):
            value = float(match.group(1).replace(",", ""))
            if direction == "M":
                return f"{value * self.conversion_factors['weight']['lb_to_kg']:.2f} kg"
            else:
                return f"{value * self.conversion_factors['weight']['kg_to_lb']:.2f}#"

        # Convert weights
        pattern = r'(\d+,?\d*(?:\.\d+)?)#' if direction == "M" else r'(\d+,?\d*(?:\.\d+)?)(?:\s?kg|KG)'
        text = re.sub(pattern, weight_to_metric, text)
        
        return text

    async def process_with_ai(self, text: str) -> str:
        """
        Process text using Volcengine AI API for more complex conversions
        """
        if not self.volcengine_api_key:
            raise ValueError("Volcengine API key not configured")

        # TODO: Implement Volcengine AI API integration
        return text
