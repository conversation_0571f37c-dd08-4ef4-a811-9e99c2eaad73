import json
import logging
import os
from typing import Optional, Dict, Any, List

import httpx
from fastapi import HTTPException

logger = logging.getLogger(__name__)

class DeepSeekService:
    """Service for interacting with Volcano Engine DeepSeek API for unit conversions."""
    
    def __init__(self):
        self.api_key = os.getenv("DEEPSEEK_API_KEY", "")
        self.api_base_url = os.getenv("DEEPSEEK_API_URL", "https://api.volcengine.com/v1/llm/generation")
        self.model = os.getenv("DEEPSEEK_MODEL", "deepseek-r1")
        self.timeout = int(os.getenv("DEEPSEEK_TIMEOUT", "30"))
        
        if not self.api_key:
            logger.warning("DEEPSEEK_API_KEY is not set. LLM functionality will not work correctly.")
    
    async def convert_units(self, text: str, unit_system: str = "metric") -> Dict[str, Any]:
        """
        Use the DeepSeek API to convert units based on user input text.
        
        Args:
            text: The text containing the conversion request
            unit_system: The preferred unit system (metric, imperial)
            
        Returns:
            Dictionary containing the conversion result
        """
        if not self.api_key:
            raise HTTPException(status_code=500, detail="DeepSeek API key is not configured")
        
        # Construct a prompt for the DeepSeek API
        prompt = f"""
        你是一个专业的单位转换助手，请帮我完成以下单位转换：

        用户需求：{text}
        首选单位系统：{unit_system}
        
        请以JSON格式返回结果，包括以下字段：
        {{
          "input_value": 数值,
          "input_unit": "输入单位",
          "output_value": 转换后的数值,
          "output_unit": "输出单位",
          "formula": "转换公式",
          "explanation": "简短说明"
        }}
        
        只返回JSON格式的结果，不要包含任何其他文本。
        """
        
        try:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            payload = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": "你是一个专业的单位转换助手，擅长精确的单位换算。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1,  # Low temperature for more deterministic responses
                "top_p": 0.95,
                "max_tokens": 500
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    self.api_base_url,
                    headers=headers,
                    json=payload
                )
                
            if response.status_code != 200:
                logger.error(f"DeepSeek API error: {response.status_code} - {response.text}")
                raise HTTPException(status_code=500, detail="Error from DeepSeek API")
            
            response_data = response.json()
            
            # Extract the model's response and parse the JSON
            if "choices" in response_data and len(response_data["choices"]) > 0:
                ai_message = response_data["choices"][0]["message"]["content"]
                # Extract JSON from the message (it might be wrapped in ```json ... ``` blocks)
                json_str = self._extract_json(ai_message)
                result = json.loads(json_str)
                
                # Format the response to our standard format
                return {
                    "result": {
                        "from": result.get("input_unit", ""),
                        "to": result.get("output_unit", ""),
                        "value": result.get("input_value", 0),
                        "converted_value": result.get("output_value", 0),
                        "formula": result.get("formula", ""),
                    },
                    "message": result.get("explanation", "Here is your conversion result")
                }
            else:
                raise HTTPException(status_code=500, detail="Invalid response from DeepSeek API")
                
        except httpx.TimeoutException:
            logger.error("DeepSeek API timeout")
            raise HTTPException(status_code=504, detail="DeepSeek API request timed out")
        except httpx.RequestError as e:
            logger.error(f"DeepSeek API request error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"DeepSeek API request error: {str(e)}")
        except json.JSONDecodeError:
            logger.error(f"Failed to parse DeepSeek API response as JSON")
            raise HTTPException(status_code=500, detail="Failed to parse conversion result")
        except Exception as e:
            logger.error(f"Unexpected error during unit conversion: {str(e)}")
            raise HTTPException(status_code=500, detail="An unexpected error occurred")
    
    def _extract_json(self, text: str) -> str:
        """Extract JSON from text that might contain code blocks."""
        # Check if the response is wrapped in ```json ... ``` blocks
        if "```json" in text and "```" in text.split("```json", 1)[1]:
            return text.split("```json", 1)[1].split("```", 1)[0].strip()
        # Check if the response is wrapped in ``` ... ``` blocks
        elif text.startswith("```") and "```" in text[3:]:
            return text.split("```", 2)[1].strip()
        # Otherwise, assume the entire text is JSON
        return text.strip()


# Create a singleton instance
deepseek_service = DeepSeekService()
