import requests
import json

def test_conversion_api():
    url = "http://localhost:8000/api/conversion"
    data = {"text": "5 feet to meters"}
    
    try:
        response = requests.post(url, json=data)
        print(f"Status code: {response.status_code}")
        print(f"Response content: {response.text}")
        
        if response.status_code == 200:
            print("Conversion API is working properly!")
        else:
            print(f"API call failed with status code: {response.status_code}")
    except Exception as e:
        print(f"Error calling API: {str(e)}")

def test_conversion_functions():
    url = "http://localhost:8000/api/conversion/functions"
    
    try:
        response = requests.get(url)
        print(f"Status code: {response.status_code}")
        print(f"Response content: {response.text}")
        
        if response.status_code == 200:
            print("Conversion Functions API is working properly!")
        else:
            print(f"API call failed with status code: {response.status_code}")
    except Exception as e:
        print(f"Error calling API: {str(e)}")

if __name__ == "__main__":
    print("Testing Conversion API...")
    test_conversion_api()
    print("\nTesting Conversion Functions API...")
    test_conversion_functions() 