from passlib.context import Crypt<PERSON>ontext
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from datetime import datetime, timedelta
from typing import Optional, Union
import os
import random
import logging
from dotenv import load_dotenv
from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from database import get_async_session
from models.user_model import User

load_dotenv()

# Setup logger
logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token", auto_error=False)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate password hash."""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """Verify JWT token and return payload."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except JWTError:
        return None

def generate_verification_code() -> str:
    """Generate a 6-digit verification code."""
    return ''.join([str(random.randint(0, 9)) for _ in range(6)])

def send_verification_email(email: str, code: str, language: str = "en") -> bool:
    """Send verification email with code using Aliyun Enterprise Email."""
    try:
        # Get SMTP settings from environment variables
        smtp_server = os.getenv("SMTP_SERVER", "smtp.qiye.aliyun.com")
        smtp_port = int(os.getenv("SMTP_PORT", "465"))
        smtp_username = os.getenv("SMTP_USERNAME")
        smtp_password = os.getenv("SMTP_PASSWORD")
        smtp_sender_name = os.getenv("SMTP_SENDER_NAME", "SteelNet")
        smtp_domain = os.getenv("SMTP_DOMAIN", "steelnet.ai")

        # Debug information
        logger.info(f"SMTP Configuration - Server: {smtp_server}, Port: {smtp_port}, Username: {smtp_username}")

        if not all([smtp_server, smtp_username, smtp_password]):
            logger.warning("SMTP settings not configured. Email verification will not be sent.")
            logger.info(f"Verification code for {email}: {code}")
            return True  # Return success in development to allow testing

        # Check if this is a QQ email address (needs special handling)
        is_qq_email = '@qq.com' in email.lower()
        logger.info(f"Sending to {'QQ email' if is_qq_email else 'standard email'}: {email}")

        # Define email translations
        email_translations = {
            "zh": {
                "emailVerificationTitle": "邮箱验证 - Steel Unit Converter",
                "emailVerificationHeader": "钢铁智联 SteelNet",
                "emailVerificationSubtitle": "一站式钢铁行业智能助手",
                "emailVerificationHeading": "验证您的电子邮箱",
                "emailVerificationGreeting": "您好！",
                "emailVerificationMessage": "感谢您注册钢铁智联服务平台。为确保账户安全，请使用以下验证码完成注册流程：",
                "emailVerificationExpiry": "此验证码将在<strong>30分钟</strong>后失效。",
                "emailVerificationIgnore": "如果您没有请求此验证码，请忽略此邮件。可能有人错误地输入了您的电子邮箱地址。",
                "emailVerificationFeatures": "注册成功后，您将能够使用我们的单位转换、制表和咨询等功能，助力您的钢铁行业业务。",
                "emailVerificationRegards": "诚挚的问候，<br>钢铁智联团队",
                "emailVerificationFooter": "此电子邮件由系统自动发送，请勿直接回复。",
                "emailVerificationCopyright": "钢铁智联 | SteelNet。保留所有权利。",
            },
            "en": {
                "emailVerificationTitle": "Email Verification - Steel Unit Converter",
                "emailVerificationHeader": "SteelNet",
                "emailVerificationSubtitle": "Professional Steel Industry Intelligence Assistant",
                "emailVerificationHeading": "Verify Your Email Address",
                "emailVerificationGreeting": "Hello!",
                "emailVerificationMessage": "Thank you for registering with SteelNet. To ensure account security, please use the following verification code to complete the registration process:",
                "emailVerificationExpiry": "This verification code will expire in <strong>30 minutes</strong>.",
                "emailVerificationIgnore": "If you did not request this verification code, please ignore this email. Someone may have mistakenly entered your email address.",
                "emailVerificationFeatures": "After successful registration, you will be able to use our unit conversion, table generation, and consulting features to support your steel industry business.",
                "emailVerificationRegards": "Best regards,<br>The SteelNet Team",
                "emailVerificationFooter": "This email was sent automatically. Please do not reply directly.",
                "emailVerificationCopyright": "SteelNet. All rights reserved.",
            }
        }

        # Get translations for the specified language, default to English if not found
        lang_translations = email_translations.get(language, email_translations["en"])

        # Load email template based on language
        template_filename = "email_verification.html" if language == "zh" else "email_verification_en.html"
        template_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "backend", "templates", template_filename
        )

        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                html_template = f.read()
        except FileNotFoundError:
            logger.warning(f"Email template not found at {template_path}, using fallback template")
            # Fallback template with placeholders
            html_template = """
            <html>
            <head>
                <style>
                    body { font-family: Arial, sans-serif; line-height: 1.6; }
                    .container { padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #eee; }
                    .header { text-align: center; padding-bottom: 10px; border-bottom: 1px solid #eee; }
                    .code { font-size: 24px; font-weight: bold; padding: 10px; text-align: center;
                            background-color: #f5f5f5; margin: 20px 0; letter-spacing: 5px; }
                    .footer { font-size: 12px; color: #777; margin-top: 30px; text-align: center; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>{{emailVerificationHeader}}</h1>
                        <p>{{emailVerificationSubtitle}}</p>
                    </div>
                    <div class="content">
                        <h2>{{emailVerificationHeading}}</h2>
                        <p>{{emailVerificationGreeting}}</p>
                        <p>{{emailVerificationMessage}}</p>

                        <div class="code">{{code}}</div>

                        <p>{{emailVerificationExpiry}}</p>

                        <p>{{emailVerificationIgnore}}</p>

                        <p>{{emailVerificationFeatures}}</p>

                        <p>{{emailVerificationRegards}}</p>
                    </div>
                    <div class="footer">
                        <p>{{emailVerificationFooter}}</p>
                        <p>&copy; {{year}} {{emailVerificationCopyright}}</p>
                    </div>
                </div>
            </body>
            </html>
            """

        # Replace template variables
        current_year = datetime.utcnow().year
        html_content = html_template.replace("{{code}}", code).replace("{{year}}", str(current_year))
        
        # Replace all translation placeholders
        for key, value in lang_translations.items():
            html_content = html_content.replace(f"{{{{{key}}}}}", value)

        # Create message using Aliyun Enterprise Email format
        from email.mime.multipart import MIMEMultipart
        from email.mime.text import MIMEText
        from email.utils import formataddr, make_msgid, formatdate

        msg = MIMEMultipart('alternative')

        # Set proper headers for Aliyun Enterprise Email
        msg['Message-ID'] = make_msgid(domain=smtp_domain)
        msg['Date'] = formatdate(localtime=True)

        # For QQ email, use very simple subject and no special characters
        if is_qq_email:
            msg['Subject'] = f"Your SteelNet Code: {code}"
        else:
            subject_text = "SteelNet 验证码" if language == "zh" else "SteelNet Verification Code"
            msg['Subject'] = subject_text

        # Format the From header properly for Aliyun Enterprise Email
        msg['From'] = formataddr((smtp_sender_name, smtp_username))
        msg['To'] = email
        msg['Reply-To'] = smtp_username

        # Add additional headers for better deliverability
        msg.add_header('X-Mailer', 'SteelNet Mailer')
        msg.add_header('X-Priority', '1')  # High priority

        # Add plain text alternative for better deliverability
        if language == "zh":
            text_content = f"""
            钢铁智联 - 邮箱验证

            感谢您注册钢铁智联。
            为了完成注册，请使用此验证码: {code}

            此验证码将在30分钟后过期。

            如果您没有请求此验证码，请忽略此邮件。

            诚挚的问候，
            钢铁智联团队
            """
        else:
            text_content = f"""
            SteelNet - Email Verification

            Thank you for registering with SteelNet.
            To complete your registration, please use this verification code: {code}

            This code will expire in 30 minutes.

            If you did not request this verification code, please ignore this email.

            Best regards,
            The SteelNet Team
            """

        # Attach plain text and HTML content
        msg.attach(MIMEText(text_content, 'plain', 'utf-8'))
        msg.attach(MIMEText(html_content, 'html', 'utf-8'))

        # Send using Aliyun Enterprise Email with SSL (port 465)
        logger.info(f"Attempting to connect to Aliyun Enterprise Email: {smtp_server}:{smtp_port}")

        # Add timeout to prevent hanging connections
        timeout = 30

        # Use SSL connection for Aliyun Enterprise Email (port 465)
        logger.info("Using SSL connection for Aliyun Enterprise Email")
        server = smtplib.SMTP_SSL(smtp_server, smtp_port, timeout=timeout)

        logger.info(f"SMTP SSL connection established, attempting login with username: {smtp_username}")
        server.login(smtp_username, smtp_password)

        logger.info(f"SMTP login successful, sending email to: {email}")
        server.sendmail(msg['From'], [email], msg.as_string())
        server.quit()

        logger.info(f"Verification email sent successfully to {email} in {language}")
        return True
        
    except smtplib.SMTPConnectError as e:
        logger.error(f"SMTP Connection Error: {str(e)}")
        logger.error("This usually indicates firewall/port blocking or DNS issues")
        logger.warning(f"EMAIL SENDING FAILED - Verification code for {email}: {code}")
        return False
    except smtplib.SMTPAuthenticationError as e:
        logger.error(f"SMTP Authentication Error: {str(e)}")
        logger.error("Check your Aliyun Enterprise Email username and password")
        logger.error("Make sure SMTP service is enabled in Aliyun Email admin panel")
        logger.warning(f"EMAIL SENDING FAILED - Verification code for {email}: {code}")
        return False
    except smtplib.SMTPRecipientsRefused as e:
        logger.error(f"SMTP Recipients Refused: {str(e)}")
        logger.error("The recipient email address was rejected")
        logger.warning(f"EMAIL SENDING FAILED - Verification code for {email}: {code}")
        return False
    except smtplib.SMTPException as e:
        logger.error(f"SMTP Error: {str(e)}")
        logger.warning(f"EMAIL SENDING FAILED - Verification code for {email}: {code}")
        return False
    except socket.timeout as e:
        logger.error(f"Connection Timeout: {str(e)}")
        logger.error("Aliyun Enterprise Email connection timed out - check network connectivity")
        logger.warning(f"EMAIL SENDING FAILED - Verification code for {email}: {code}")
        return False
    except socket.gaierror as e:
        logger.error(f"DNS Resolution Error: {str(e)}")
        logger.error("Cannot resolve Aliyun Enterprise Email server hostname - check DNS settings")
        logger.warning(f"EMAIL SENDING FAILED - Verification code for {email}: {code}")
        return False
    except ConnectionRefusedError as e:
        logger.error(f"Connection Refused: {str(e)}")
        logger.error("Aliyun Enterprise Email server refused connection - check port and firewall")
        logger.warning(f"EMAIL SENDING FAILED - Verification code for {email}: {code}")
        return False
    except Exception as e:
        logger.error(f"Error sending verification email: {str(e)}")
        # Print stack trace for debugging
        import traceback
        logger.error(f"Stack trace: {traceback.format_exc()}")

        # If email sending fails, log the verification code so it can be manually provided if needed
        logger.warning(f"EMAIL SENDING FAILED - Verification code for {email}: {code}")
        return False

async def get_current_user(token: str = Depends(oauth2_scheme), db_session: AsyncSession = Depends(get_async_session)) -> User:
    """
    Get the current user from JWT token.
    Raises 401 if token is invalid or user not found.
    """
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    payload = verify_token(token)
    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    username: str = payload.get("sub")
    if username is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create a new session for this request
    from database import AsyncSessionLocal

    async with AsyncSessionLocal() as session:
        async with session.begin():
            # Get user from database
            result = await session.execute(select(User).where(User.username == username))
            user = result.scalars().first()

            if user is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Inactive user",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            # Update last login time
            user.last_login = datetime.utcnow()

            # Create a detached copy of the user object with all the attributes we need
            user_dict = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "company_name": user.company_name,
                "country": user.country,
                "is_active": user.is_active,
                "is_admin": user.is_admin,
                "last_login": user.last_login,
                "created_at": user.created_at
            }

    # Create a new User instance that's not bound to any session
    detached_user = User(**user_dict)
    return detached_user

async def get_current_user_optional(token: str = Depends(oauth2_scheme)) -> Optional[User]:
    """
    Get the current user from the JWT token if available.
    This dependency will return None if the token is invalid or not provided.
    """
    try:
        return await get_current_user(token)
    except HTTPException:
        return None

async def get_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Check if the current user is an admin and return the user if so.
    This dependency will raise an HTTPException if the user is not an admin.
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to access this resource",
        )
    return current_user
