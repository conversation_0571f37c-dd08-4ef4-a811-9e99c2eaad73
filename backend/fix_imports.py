#!/usr/bin/env python3
"""
Script to fix relative imports in router files

This script adds the necessary import statements to all router files
to ensure they can be run directly without relative import errors.
"""
import os
import re
import sys

def fix_imports_in_file(file_path):
    """Fix imports in a single file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if the file already has the sys.path.append line
    if 'sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))' in content:
        print(f"File {file_path} already has the import fix")
        return False
    
    # Add the import statements after the existing imports
    import_block = (
        "import sys\n"
        "import os\n"
        "# Add the parent directory to sys.path\n"
        "sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))\n"
    )
    
    # Find a good place to insert the import block
    # Look for the last import statement
    import_pattern = r'^import\s+.*$|^from\s+.*import.*$'
    imports = re.findall(import_pattern, content, re.MULTILINE)
    
    if imports:
        last_import = imports[-1]
        # Insert after the last import
        content = content.replace(last_import, last_import + '\n\n' + import_block)
    else:
        # If no imports found, add at the beginning of the file
        content = import_block + '\n' + content
    
    # Write the modified content back to the file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed imports in {file_path}")
    return True

def fix_all_router_files():
    """Fix imports in all router files"""
    router_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'routers')
    
    if not os.path.exists(router_dir):
        print(f"Router directory not found: {router_dir}")
        return
    
    fixed_count = 0
    for filename in os.listdir(router_dir):
        if filename.endswith('.py') and filename != '__init__.py':
            file_path = os.path.join(router_dir, filename)
            if fix_imports_in_file(file_path):
                fixed_count += 1
    
    print(f"Fixed imports in {fixed_count} files")

def fix_main_file():
    """Fix imports in main.py"""
    main_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'main.py')
    
    if not os.path.exists(main_file):
        print(f"Main file not found: {main_file}")
        return
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if the file already has the sys.path.append line
    if 'sys.path.append(os.path.dirname(os.path.abspath(__file__)))' in content:
        print(f"Main file already has the import fix")
        return
    
    # Add the import statements after the existing imports
    import_block = (
        "# Setup Python path to allow local imports\n"
        "import sys\n"
        "import os\n"
        "sys.path.append(os.path.dirname(os.path.abspath(__file__)))\n"
    )
    
    # Find a good place to insert the import block
    # Look for the first import statement
    import_pattern = r'^import\s+.*$|^from\s+.*import.*$'
    imports = re.findall(import_pattern, content, re.MULTILINE)
    
    if imports:
        first_import = imports[0]
        # Insert before the first import
        content = content.replace(first_import, import_block + '\n' + first_import)
    else:
        # If no imports found, add at the beginning of the file
        content = import_block + '\n' + content
    
    # Write the modified content back to the file
    with open(main_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed imports in {main_file}")

if __name__ == "__main__":
    fix_all_router_files()
    fix_main_file()
    print("Import fixes completed")
