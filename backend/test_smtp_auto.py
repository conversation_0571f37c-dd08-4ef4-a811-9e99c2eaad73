import smtplib
from email.mime.text import MIMEText
from email.header import <PERSON><PERSON>
import sys
import os
from dotenv import load_dotenv

# Load env variables
load_dotenv()

# Aliyun Enterprise Mail settings
smtp_server = os.getenv("SMTP_SERVER", "smtp.qiye.aliyun.com")
smtp_port = int(os.getenv("SMTP_PORT", "465"))
username = os.getenv("SMTP_USERNAME", "<EMAIL>")
password = os.getenv("SMTP_PASSWORD", "STEELnet456456")

# Test recipient - change this to a valid email for testing
recipient = "<EMAIL>"  # Replace with your test email

print(f"Testing SMTP connection to {smtp_server}:{smtp_port}")
print(f"From: {username}")
print(f"To: {recipient}")

# Set up email
msg = MIMEText('This is a test email from the Steel Unit Converter application.', 'plain', 'utf-8')
msg['Subject'] = Header('SMTP Test', 'utf-8')
msg['From'] = f'钢铁智联 <{username}>'
msg['To'] = recipient

# Connect and authenticate with detailed error handling
try:
    print(f"\nConnecting to {smtp_server}:{smtp_port} using SSL...")
    server = smtplib.SMTP_SSL(smtp_server, smtp_port)
    server.set_debuglevel(1)  # Enable verbose debug output
    
    print("\nServer info:")
    print(f"EHLO response: {server.ehlo()}")
    
    print(f"\nAttempting login with username: {username}")
    server.login(username, password)
    print("Login successful!")
    
    print(f"\nSending test email to {recipient}...")
    server.sendmail(username, recipient, msg.as_string())
    print("Email sent successfully!")
    
except smtplib.SMTPAuthenticationError as e:
    print(f"\nAuthentication Error: {e}")
    print("Possible causes:")
    print("1. Incorrect username or password")
    print("2. Account security settings (check for 2FA or app-specific passwords)")
    print("3. Service restrictions (check Aliyun console for restrictions)")
    print("\nTroubleshooting tips for Aliyun Enterprise Mail:")
    print("- Log in to the Aliyun Enterprise Mail admin console")
    print("- Check if the account has sending permissions")
    print("- Verify that the SMTP service is enabled for this account")
    print("- Check if you need to use the app-specific password instead of account password")
    
except smtplib.SMTPConnectError as e:
    print(f"\nConnection Error: {e}")
    print("Possible causes:")
    print("1. Incorrect server or port")
    print("2. Firewall blocking connection")
    print("3. Server down or maintenance")
    
except Exception as e:
    print(f"\nError: {e}")
    import traceback
    traceback.print_exc()
    
finally:
    try:
        server.quit()
        print("\nSMTP connection closed.")
    except:
        pass 
 