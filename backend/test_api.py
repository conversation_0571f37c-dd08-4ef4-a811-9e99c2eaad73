import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpassword123"
TEST_USERNAME = "testuser"

def test_login():
    """Test the login endpoint"""
    print("\n===== Testing Login =====")
    login_url = f"{BASE_URL}/api/auth/login"
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(login_url, data=login_data)
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Login endpoint working!")
            return response.json().get("access_token")
        else:
            print("❌ Login endpoint failed!")
            return None
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_token():
    """Test the token endpoint"""
    print("\n===== Testing Token =====")
    token_url = f"{BASE_URL}/api/auth/token"
    token_data = {
        "username": TEST_USERNAME,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(
            token_url, 
            data=token_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Token endpoint working!")
            return response.json().get("access_token")
        else:
            print("❌ Token endpoint failed!")
            return None
    except Exception as e:
        print(f"Error: {e}")
        return None

def test_llm_functions(token):
    """Test the LLM functions endpoint"""
    print("\n===== Testing LLM Functions =====")
    functions_url = f"{BASE_URL}/api/llm/functions"
    
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    try:
        response = requests.get(functions_url, headers=headers)
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ LLM Functions endpoint working!")
            return True
        else:
            print("❌ LLM Functions endpoint failed!")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_llm_query(token):
    """Test the LLM query endpoint"""
    print("\n===== Testing LLM Query =====")
    query_url = f"{BASE_URL}/api/llm"
    
    headers = {
        "Content-Type": "application/json"
    }
    if token:
        headers["Authorization"] = f"Bearer {token}"
    
    query_data = {
        "text": "Convert 100 kg to lb",
        "unit_system": "metric",
        "function": "conversion"
    }
    
    try:
        response = requests.post(query_url, json=query_data, headers=headers)
        print(f"Status code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)[:300]}...")
        
        if response.status_code == 200:
            print("✅ LLM Query endpoint working!")
            return True
        else:
            print("❌ LLM Query endpoint failed!")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_reset_password_request():
    """Test the password reset request endpoint"""
    print("\n===== Testing Password Reset Request =====")
    url = f"{BASE_URL}/api/auth/reset-password/request"
    data = {
        "email": TEST_EMAIL
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Password reset request endpoint working!")
            return True
        else:
            print("❌ Password reset request endpoint failed!")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_reset_password_confirm():
    """Test the password reset confirmation endpoint"""
    print("\n===== Testing Password Reset Confirmation =====")
    url = f"{BASE_URL}/api/auth/reset-password/confirm"
    data = {
        "email": TEST_EMAIL,
        "code": "123456",  # Test code
        "new_password": "newpassword123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Status code: {response.status_code}")
        print(f"Response: {response.text}")
        
        # We expect a 400 because the code is invalid in the test
        if response.status_code == 400:
            print("✅ Password reset confirmation endpoint working (expected error for test code)!")
            return True
        else:
            print("❌ Password reset confirmation endpoint returned unexpected status!")
            return False
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    print(f"Testing API endpoints at {BASE_URL}")
    
    # Test login endpoint
    token = test_login()
    
    # If login failed, try token endpoint
    if not token:
        token = test_token()
    
    # Test password reset endpoints
    test_reset_password_request()
    test_reset_password_confirm()
    
    # Test LLM functions endpoint (with or without token)
    test_llm_functions(token)
    
    # Test LLM query endpoint (with or without token)
    if token:
        test_llm_query(token)
    else:
        print("\nSkipping LLM query test as no token is available.")
    
    print("\nAPI testing complete!") 