import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8000"

def test_endpoint(method, path, data=None, headers=None, expected_status=None):
    """Test a specific endpoint and print the result"""
    url = f"{BASE_URL}{path}"
    
    if headers is None:
        headers = {}
    
    print(f"\n===== Testing {method} {path} =====")
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data if data else None, headers=headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data if data else None, headers=headers)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            print(f"Unsupported method: {method}")
            return False
        
        status = response.status_code
        content_type = response.headers.get("content-type", "")
        
        # Print response details
        print(f"Status: {status}")
        print(f"Content-Type: {content_type}")
        
        # Try to print response as JSON if possible
        try:
            if "application/json" in content_type:
                print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)[:500]}")
            else:
                print(f"Response: {response.text[:500]}")
        except:
            print(f"Response: {response.text[:500]}")
        
        # Check if status matches expected
        if expected_status and status != expected_status:
            print(f"❌ Unexpected status code: got {status}, expected {expected_status}")
            return False
        
        print(f"✅ Endpoint accessible (returned {status})")
        return True
    except Exception as e:
        print(f"Error: {e}")
        return False

def run_tests():
    """Run tests for all API endpoints"""
    # Root endpoint
    test_endpoint("GET", "/", expected_status=200)
    
    # Health endpoint
    test_endpoint("GET", "/health", expected_status=200)
    
    # Auth endpoints
    test_endpoint("POST", "/api/auth/login", data={"email": "<EMAIL>", "password": "test123"}, expected_status=401)
    test_endpoint("POST", "/api/auth/token", data={"username": "test", "password": "test123"}, expected_status=422)
    test_endpoint("POST", "/api/auth/register", data={"username": "test", "email": "<EMAIL>", "password": "test123"}, expected_status=201)
    test_endpoint("POST", "/api/auth/verify-email", data={"email": "<EMAIL>", "code": "123456"}, expected_status=400)
    test_endpoint("POST", "/api/auth/request-verification", data={"email": "<EMAIL>"}, expected_status=200)
    # Password reset endpoints
    test_endpoint("POST", "/api/auth/reset-password/request", data={"email": "<EMAIL>"}, expected_status=200)
    test_endpoint("POST", "/api/auth/reset-password/confirm", 
                  data={"email": "<EMAIL>", "code": "123456", "new_password": "newpassword123"}, 
                  expected_status=400) # Expect 400 for invalid code
    
    # LLM endpoints
    test_endpoint("GET", "/api/llm/functions", expected_status=200)
    test_endpoint("POST", "/api/llm", data={"text": "Convert 5 kg to pounds", "function": "conversion"}, expected_status=200)
    
    # Chat endpoints
    test_endpoint("GET", "/api/chat/sessions", headers={"Authorization": "Bearer invalid_token"}, expected_status=401)
    
    # Email endpoints
    test_endpoint("POST", "/api/email/send", 
        data={
            "email": "<EMAIL>", 
            "type": "conversion", 
            "data": {
                "from": "kg",
                "to": "lb",
                "value": 5,
                "converted_value": 11.02
            }
        }, 
        expected_status=404)
    
    # Conversion endpoints
    test_endpoint("POST", "/api/conversion", data={"text": "Convert 5 kg to pounds", "function": "conversion"}, expected_status=200)
    test_endpoint("GET", "/api/conversion/functions", expected_status=200)

if __name__ == "__main__":
    print(f"Testing all API endpoints at {BASE_URL}")
    print(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    run_tests()
    print("\nEndpoint testing complete!") 