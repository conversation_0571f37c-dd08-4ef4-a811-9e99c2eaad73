#!/usr/bin/env python
"""
Script to fix indentation issues in auth.py
"""

def fix_auth_file():
    file_path = 'routers/auth.py'
    
    try:
        # Read the file
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        # Find the problematic section and fix it
        for i, line in enumerate(lines):
            if i > 0 and 'if verified_code or existing_email.is_active:' in lines[i-1] and 'raise HTTPException' in line and not line.strip().startswith('raise'):
                # Fix the indentation - add 4 spaces at the beginning
                lines[i] = ' ' * 16 + line.lstrip()
                print(f"Fixed indentation on line {i+1}")
            
            # Fix other potential indentation issues
            if '# Create verification code for new user' in line and not line.strip().startswith('#'):
                # Check if this line and the next few lines need proper indentation
                for j in range(i, min(i+5, len(lines))):
                    if not lines[j].startswith(' ' * 8) and lines[j].strip():
                        lines[j] = ' ' * 8 + lines[j].lstrip()
                        print(f"Fixed indentation on line {j+1}")
        
        # Write back to the file
        with open(file_path, 'w') as f:
            f.writelines(lines)
        
        print(f"Fixed indentation issues in {file_path}")
        return True
    
    except Exception as e:
        print(f"Error fixing file: {e}")
        return False

if __name__ == "__main__":
    fix_auth_file() 