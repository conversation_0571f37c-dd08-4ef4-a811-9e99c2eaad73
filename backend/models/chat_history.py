from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Text, Boolean, Index, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import datetime
import uuid
import os

from database import Base

# Check if we're using SQLite
IS_SQLITE = os.getenv("DATABASE_URL", "").startswith("sqlite")

class ChatHistory(Base):
    """Model for storing chat history"""
    __tablename__ = "chat_histories"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    session_id = Column(String(50), nullable=False, index=True)
    title = Column(String(200), nullable=True)
    message_count = Column(Integer, default=0, nullable=False)
    last_message_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    is_archived = Column(Boolean, default=False, nullable=False)

    # Object key for storage in OSS
    oss_object_key = Column(String(255), nullable=True)

    # Chat content stored as JSON
    content = Column(JSON, nullable=True)

    # Metadata for chat session
    meta_data = Column(JSON, nullable=True)

    # Relationships
    user = relationship("User", back_populates="chat_histories")
    likes = relationship("ChatLike", back_populates="chat", cascade="all, delete-orphan")

    # Common indexes
    __table_args__ = (
        Index('ix_chat_histories_user_id_created_at', 'user_id', 'created_at'),
        Index('ix_chat_histories_session_id', 'session_id'),
    )

class ChatLike(Base):
    """Model for storing user likes/feedback on chat messages"""
    __tablename__ = "chat_likes"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    chat_id = Column(String(36), ForeignKey("chat_histories.id", ondelete="CASCADE"), nullable=False)
    message_index = Column(Integer, nullable=False)  # Index of the message within the chat
    is_liked = Column(Boolean, default=True, nullable=False)
    feedback = Column(Text, nullable=True)  # Using standard Text for compatibility
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)

    # Relationships
    user = relationship("User")
    chat = relationship("ChatHistory", back_populates="likes")

    # Common indexes
    __table_args__ = (
        Index('ix_chat_likes_chat_id_message_index', 'chat_id', 'message_index'),
        Index('ix_chat_likes_user_id', 'user_id'),
    )