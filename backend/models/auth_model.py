from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Index, Boolean
from sqlalchemy.sql import func
import datetime
import uuid
from sqlalchemy.orm import relationship

from database import Base

class VerificationCode(Base):
    """Model for storing email verification codes"""
    __tablename__ = "verification_codes"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String(100), nullable=False, index=True)
    code = Column(String(10), nullable=False)
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    is_used = Column(Boolean, default=False, nullable=False)
    
    # Store user data to create after verification
    username = Column(String(50), nullable=False)
    password_hash = Column(String(100), nullable=False)
    company_name = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    
    # Relationship with users (for used verification codes)
    user_id = Column(String(36), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    user = relationship("User", back_populates="verification_codes")
    
    # Indexes and table options
    __table_args__ = (
        Index('ix_verification_codes_email_code', 'email', 'code'),
        Index('ix_verification_codes_expires_at', 'expires_at'),
    ) 