from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, ForeignKey, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)
    credit_balance = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.utcnow)
    subscription_end = Column(DateTime, nullable=True)
    
    conversion_history = relationship("ConversionHistory", back_populates="user")
    payments = relationship("Payment", back_populates="user")

class ConversionHistory(Base):
    __tablename__ = "conversion_history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    input_text = Column(String)
    output_text = Column(String)
    conversion_type = Column(String)  # M or I
    created_at = Column(DateTime, default=datetime.utcnow)
    
    user = relationship("User", back_populates="conversion_history")

class Payment(Base):
    __tablename__ = "payments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    amount = Column(Float)
    payment_type = Column(String)  # subscription, credit_purchase
    status = Column(String)  # pending, completed, failed
    created_at = Column(DateTime, default=datetime.utcnow)
    
    user = relationship("User", back_populates="payments")

class Subscription(Base):
    __tablename__ = "subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    price = Column(Float)
    duration_days = Column(Integer)
    max_conversions = Column(Integer)
    description = Column(String)
