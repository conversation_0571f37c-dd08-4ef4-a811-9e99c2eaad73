from sqlalchemy import Column, Inte<PERSON>, String, Float, ForeignKey, DateTime, Text, Index, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import datetime
import uuid

from database import Base

class Conversion(Base):
    """Model for storing conversion history"""
    __tablename__ = "conversions"
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    input_text = Column(Text, nullable=False)
    from_unit = Column(String(50), nullable=True)
    to_unit = Column(String(50), nullable=True)
    input_value = Column(Float, nullable=True)
    output_value = Column(Float, nullable=True)
    unit_system = Column(String(20), nullable=True, default="metric")
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    
    # Store the query function and result
    query_function = Column(String(50), nullable=True)
    result_text = Column(Text, nullable=True)
    
    # Metadata for additional information
    meta_data = Column(JSON, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="conversions")
    
    # Indexes and table options
    __table_args__ = (
        Index('ix_conversions_user_id_created_at', 'user_id', 'created_at'),
        Index('ix_conversions_unit_system', 'unit_system'),
    )
