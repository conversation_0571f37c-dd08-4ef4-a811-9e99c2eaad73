from sqlalchemy import <PERSON>ole<PERSON>, Column, Integer, String, DateTime, Enum, Index, Text, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import datetime
import enum
import uuid

from database import Base

class UserType(enum.Enum):
    FREE = "free"
    PAID = "paid"

class User(Base):
    """Model for user accounts"""
    __tablename__ = "users"
    
    id = Column(String(36), primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(100), nullable=False)
    company_name = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    user_type = Column(Enum(UserType), default=UserType.FREE, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_admin = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now(), nullable=False)
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    # Additional fields
    conversion_count = Column(Integer, default=0, nullable=False)
    preferences = Column(JSON, nullable=True)
    profile_image_url = Column(String(255), nullable=True)
    
    # Relationship with the conversions table
    conversions = relationship("Conversion", back_populates="user", cascade="all, delete-orphan")
    # Relationship with the chat histories
    chat_histories = relationship("ChatHistory", back_populates="user", cascade="all, delete-orphan")
    # Relationship with verification codes
    verification_codes = relationship("VerificationCode", back_populates="user")
    
    # Indexes
    __table_args__ = (
        Index('ix_users_username_email', 'username', 'email'),
        Index('ix_users_created_at', 'created_at'),
    )
