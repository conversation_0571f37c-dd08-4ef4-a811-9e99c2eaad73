import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Head<PERSON>
from email.utils import formataddr, make_msgid, formatdate
import os
import random
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Generate verification code
verification_code = ''.join([str(random.randint(0, 9)) for _ in range(6)])

# SMTP Settings
smtp_server = os.getenv("SMTP_SERVER", "smtp.qiye.aliyun.com")
smtp_port = int(os.getenv("SMTP_PORT", "25"))  # Use port 25 with STARTTLS
smtp_username = os.getenv("SMTP_USERNAME", "<EMAIL>")
smtp_password = os.getenv("SMTP_PASSWORD", "STEELnet456456")
smtp_sender_name = os.getenv("SMTP_SENDER_NAME", "SteelNet")
smtp_domain = os.getenv("SMTP_DOMAIN", "steelnet.ai")
smtp_hostname = os.getenv("SMTP_HOSTNAME", "unit-converter.steelnet.ai")

# Recipient - QQ email
recipient = "<EMAIL>"

print(f"Testing SMTP connection to: {smtp_server}:{smtp_port}")
print(f"From: {formataddr((smtp_sender_name, smtp_username))}")
print(f"To: {recipient}")
print(f"Verification code: {verification_code}")

# Create message - Use a format optimized for QQ email
msg = MIMEMultipart('alternative')

# Set proper headers for better deliverability to QQ
msg['Message-ID'] = make_msgid(domain=smtp_domain)
msg['Date'] = formatdate(localtime=True)
# Put verification code directly in subject line for QQ (helps with deliverability)
msg['Subject'] = f"Your SteelNet Code: {verification_code}"
msg['From'] = formataddr((smtp_sender_name, smtp_username))
msg['To'] = recipient
msg['Reply-To'] = smtp_username

# Add additional headers for better deliverability
msg.add_header('X-Mailer', 'SteelNet Mailer')
msg.add_header('X-Priority', '1')  # High priority

# Plain text version - Keep it simple for QQ
text_content = f"""
SteelNet Verification

Your verification code is: {verification_code}

This code will expire in 30 minutes.

Thank you,
SteelNet Team
"""
msg.attach(MIMEText(text_content, 'plain', 'utf-8'))

# HTML version - Keep it simple for QQ
html_content = f"""
<html>
<head>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }}
        .code {{ font-size: 24px; font-weight: bold; padding: 10px; background-color: #f0f0f0; }}
    </style>
</head>
<body>
    <h2>SteelNet Verification</h2>
    <p>Your verification code is:</p>
    <div class="code">{verification_code}</div>
    <p>This code will expire in 30 minutes.</p>
    <p>Thank you,<br>SteelNet Team</p>
</body>
</html>
"""
msg.attach(MIMEText(html_content, 'html', 'utf-8'))

try:
    print(f"Connecting to {smtp_server}:{smtp_port}...")
    server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)
    server.set_debuglevel(1)  # Enable verbose debug output
    
    # Set the hostname for the EHLO command
    server.local_hostname = smtp_hostname
    
    print("Server connected, sending EHLO...")
    ehlo_response = server.ehlo()
    print(f"EHLO response: {ehlo_response}")
    
    print("Starting TLS...")
    starttls_response = server.starttls()
    print(f"STARTTLS response: {starttls_response}")
    
    # Send EHLO again after STARTTLS
    print("Sending EHLO again after STARTTLS...")
    ehlo_again_response = server.ehlo()
    print(f"EHLO again response: {ehlo_again_response}")
    
    print(f"Logging in with username: {smtp_username}")
    login_response = server.login(smtp_username, smtp_password)
    print(f"Login response: {login_response}")
    
    print(f"Sending email to QQ recipient: {recipient}")
    print(f"With verification code: {verification_code}")
    send_response = server.sendmail(smtp_username, recipient, msg.as_string())
    print(f"Send response: {send_response}")
    
    print("Email sent successfully!")
    print(f"Check QQ email for verification code: {verification_code}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
finally:
    try:
        print("Closing server connection...")
        server.quit()
    except:
        pass
    
print("Test complete!") 