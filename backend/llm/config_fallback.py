"""
Configuration module for the LLM package (Fallback version)

This is a fallback version of the config module that doesn't rely on pydantic_settings.
It will be used if pydantic_settings is not available.
"""
import os
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)

class LLMConfig(BaseModel):
    """
    Configuration settings for the LLM module.

    These settings are loaded from environment variables.
    """
    # API key for 火山引擎 (Volcano Engine)
    api_key: str = Field(
        "9ed8bdbe-1fa4-4a97-b4ae-52843714fdca",  # Default API key
        description="API key for 火山引擎 (Volcano Engine)"
    )

    # Endpoint ID for 火山引擎 (Volcano Engine) - DeepSeek R1 model
    endpoint_id: str = Field(
        "deepseek-v3-250324",
        description="Endpoint ID for 火山引擎 DeepSeek R1 model"
    )

    # API URL for 火山引擎 (Volcano Engine)
    api_url: str = Field(
        "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
        description="API URL for 火山引擎 (Volcano Engine)"
    )

    # Timeout for 火山引擎 (Volcano Engine) API requests
    timeout: int = Field(
        300,
        description="Timeout for 火山引擎 (Volcano Engine) API requests in seconds"
    )

    # Path to the LLM prompt template
    prompt_template_path: str = Field(
        "llm_prompt.txt",
        description="Path to the LLM prompt template"
    )

    class Config:
        extra = "ignore"  # Allow extra fields

def load_env_vars(config):
    """Load environment variables into config"""
    env_mapping = {
        "api_key": ["VOLCANO_ENGINE_API_KEY", "VOLCANO_API_KEY", "ARK_API_KEY", "VOLCENGINE_API_KEY"],
        "endpoint_id": ["VOLCANO_ENGINE_ENDPOINT_ID"],
        "api_url": ["VOLCANO_ENGINE_API_URL"],
        "timeout": ["VOLCANO_ENGINE_TIMEOUT"],
        "prompt_template_path": ["VOLCANO_ENGINE_PROMPT_TEMPLATE"]
    }
    
    for field, env_vars in env_mapping.items():
        for env_var in env_vars:
            value = os.getenv(env_var)
            if value is not None:
                if field == "timeout":
                    try:
                        value = int(value)
                    except ValueError:
                        logger.warning(f"Invalid value for {env_var}: {value}. Using default.")
                        continue
                setattr(config, field, value)
                break

# Create a singleton instance
llm_config = LLMConfig()
load_env_vars(llm_config)
