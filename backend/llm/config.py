"""
Configuration module for the LLM package

This module handles loading environment variables and configuration settings
specific to the LLM functionality.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
# Try to load .env.production first, then fall back to .env
if os.path.exists(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env.production')):
    load_dotenv(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env.production'))
    print("LLM Config: Loaded environment variables from .env.production")
else:
    load_dotenv()
    print("LLM Config: Loaded environment variables from .env")

class LLMConfig:
    """
    Configuration settings for the LLM module.

    These settings are loaded from environment variables.
    """
    def __init__(self):
        # API key for 火山引擎 (Volcano Engine)
        self.api_key = self._get_env_var(
            ["VOLCANO_ENGINE_API_KEY", "VOLCANO_API_KEY", "ARK_API_KEY", "VOLCENGINE_API_KEY"],
            "9ed8bdbe-1fa4-4a97-b4ae-52843714fdca"  # Default API key
        )

        # Endpoint ID for 火山引擎 (Volcano Engine) - DeepSeek R1 model
        self.endpoint_id = self._get_env_var(
            ["VOLCANO_ENGINE_ENDPOINT_ID"],
            "deepseek-v3-250324"
        )

        # API URL for 火山引擎 (Volcano Engine)
        self.api_url = self._get_env_var(
            ["VOLCANO_ENGINE_API_URL"],
            "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
        )

        # Timeout for 火山引擎 (Volcano Engine) API requests
        self.timeout = int(self._get_env_var(
            ["VOLCANO_ENGINE_TIMEOUT"],
            "300"
        ))

        # Path to the LLM prompt template
        self.prompt_template_path = self._get_env_var(
            ["VOLCANO_ENGINE_PROMPT_TEMPLATE"],
            "llm_prompt.txt"
        )

    def _get_env_var(self, env_vars, default):
        """
        Get an environment variable from a list of possible names.

        Args:
            env_vars: List of environment variable names to try
            default: Default value if none of the environment variables are set

        Returns:
            The value of the first environment variable that is set, or the default
        """
        for env_var in env_vars:
            value = os.getenv(env_var)
            if value is not None:
                return value
        return default

# Create a singleton instance
llm_config = LLMConfig()