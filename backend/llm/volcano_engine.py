"""
火山引擎 (Volcano Engine) LLM Connector

This module handles all communication with the 火山引擎 DeepSeek R1 API using direct HTTP requests.
"""
import json
import logging
import re
import os
from typing import Dict, Any, Optional, AsyncGenerator

import httpx
from fastapi import HTTPException
from fastapi.responses import StreamingResponse

# Initialize logger
logger = logging.getLogger(__name__)

try:
    from .config import llm_config
except ImportError as e:
    if "pydantic_settings" in str(e):
        # Fallback to the version that doesn't use pydantic_settings
        from .config_fallback import llm_config
        logger.warning("Using fallback config module due to missing pydantic_settings package")
    else:
        # Re-raise other import errors
        raise

class VolcanoEngineAPI:
    """
    API client for 火山引擎 (Volcano Engine) DeepSeek models.

    This class handles the API communication with the 火山引擎 DeepSeek service.
    Supports both DeepSeek R1 and DeepSeek V3 models with streaming support.
    """

    def __init__(self):
        self.api_key = llm_config.api_key
        self.endpoint_id = llm_config.endpoint_id
        self.api_url = llm_config.api_url
        self.timeout = llm_config.timeout

        if not self.api_key:
            logger.warning("Volcano Engine API key is not set. LLM functionality will not work correctly.")

        if not self.api_url:
            logger.warning("Volcano Engine API URL is not set. LLM functionality will not work correctly.")

    async def generate_response_stream(
            self,
            system_prompt: str,
            user_prompt: str,
            temperature: float = 0,
            max_tokens: int = 2000,
            model: str = None
        ) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response using the 火山引擎 DeepSeek models.

        Args:
            system_prompt: The system prompt to set the context
            user_prompt: The user prompt or question
            temperature: Controls randomness (0-1)
            max_tokens: Maximum number of tokens to generate
            model: The model to use (e.g., deepseek-r1-250120 or deepseek-v3-250324),
                  defaults to the configured endpoint_id if None

        Yields:
            Streaming response chunks as JSON strings
        """
        if not self.api_key:
            logger.warning("Volcano Engine API key is not configured")
            raise HTTPException(status_code=500, detail="Volcano Engine API key is not configured")

        if not self.api_url:
            logger.warning("Volcano Engine API URL is not configured")
            raise HTTPException(status_code=500, detail="Volcano Engine API URL is not configured")

        try:
            messages = []

            # Add system message if provided
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})

            # Add user message
            messages.append({"role": "user", "content": user_prompt})

            # Use the specified model or fall back to the configured endpoint_id
            model_to_use = model if model else self.endpoint_id

            # Prepare the request payload for streaming
            payload = {
                "model": model_to_use,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": True
            }

            # Prepare headers with API Key as Bearer token
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            logger.info(f"Starting streaming request to 火山引擎 API using model: {model_to_use}")

            # Make the streaming request with connection pooling and memory optimizations
            async with httpx.AsyncClient(
                timeout=self.timeout,
                limits=httpx.Limits(max_connections=5, max_keepalive_connections=2)
            ) as client:
                async with client.stream(
                    "POST",
                    self.api_url,
                    json=payload,
                    headers=headers
                ) as response:
                    # Check for HTTP errors
                    if response.status_code != 200:
                        error_text = await response.aread()
                        logger.error(f"HTTP error: {response.status_code} - {error_text}")
                        raise HTTPException(status_code=response.status_code,
                                            detail=f"Volcano Engine API error: {response.status_code} - {error_text}")

                    # Process streaming response with memory-efficient buffering
                    buffer = b""
                    async for chunk in response.aiter_bytes():
                        buffer += chunk
                        
                        # Process complete lines
                        while b"\n" in buffer:
                            line, buffer = buffer.split(b"\n", 1)
                            line_str = line.decode('utf-8', errors='ignore').strip()
                            
                            if line_str:
                                # Handle Server-Sent Events format
                                if line_str.startswith("data: "):
                                    data = line_str[6:]  # Remove "data: " prefix
                                    if data.strip() == "[DONE]":
                                        return
                                    try:
                                        chunk_data = json.loads(data)
                                        yield json.dumps(chunk_data, ensure_ascii=False)
                                    except json.JSONDecodeError:
                                        logger.warning(f"Failed to parse streaming chunk: {data}")
                                        continue
                                        
                        # Limit buffer size to prevent memory issues
                        if len(buffer) > 8192:  # 8KB limit
                            buffer = buffer[-4096:]  # Keep last 4KB

        except httpx.RequestError as e:
            logger.error(f"Request error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Request error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

    async def generate_response(
            self,
            system_prompt: str,
            user_prompt: str,
            temperature: float = 0,
            max_tokens: int = 2000,
            model: str = None,
            stream: bool = False
        ) -> Dict[str, Any]:
        """
        Generate a response using the 火山引擎 DeepSeek models.

        Args:
            system_prompt: The system prompt to set the context
            user_prompt: The user prompt or question
            temperature: Controls randomness (0-1)
            max_tokens: Maximum number of tokens to generate
            model: The model to use (e.g., deepseek-r1-250120 or deepseek-v3-250324),
                  defaults to the configured endpoint_id if None
            stream: Whether to stream the response

        Returns:
            Dictionary containing the API response
        """
        if not self.api_key:
            logger.warning("Volcano Engine API key is not configured")
            raise HTTPException(status_code=500, detail="Volcano Engine API key is not configured")

        if not self.api_url:
            logger.warning("Volcano Engine API URL is not configured")
            raise HTTPException(status_code=500, detail="Volcano Engine API URL is not configured")

        try:
            messages = []

            # Add system message if provided
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})

            # Add user message
            messages.append({"role": "user", "content": user_prompt})

            # Use the specified model or fall back to the configured endpoint_id
            # For 火山引擎, we should use the model specified in the config or passed as parameter
            model_to_use = model if model else self.endpoint_id

            # Prepare the request payload according to 火山引擎 DeepSeek R1 API format
            payload = {
                "model": model_to_use,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream
            }

            # Prepare headers with API Key as Bearer token
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            logger.info(f"Sending request to 火山引擎 API using model: {model_to_use}")
            logger.info(f"API URL: {self.api_url}")
            logger.info(f"Request payload: {json.dumps(payload)}")

            # Make the request with connection pooling
            async with httpx.AsyncClient(
                timeout=self.timeout,
                limits=httpx.Limits(max_connections=5, max_keepalive_connections=2)
            ) as client:
                response = await client.post(
                    self.api_url,
                    json=payload,
                    headers=headers
                )

                # Check for HTTP errors
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"HTTP error: {response.status_code} - {error_text}")
                    raise HTTPException(status_code=response.status_code,
                                        detail=f"Volcano Engine API error: {response.status_code} - {error_text}")

                # Parse the response
                try:
                    response_data = response.json()
                    logger.info(f"Successfully received response from 火山引擎 API")
                    logger.info(f"Complete API response: {json.dumps(response_data, ensure_ascii=False)}")
                    return response_data
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse response as JSON: {e}")
                    logger.error(f"Response text: {response.text}")
                    raise HTTPException(status_code=500,
                                        detail=f"Failed to parse response from Volcano Engine API: {str(e)}")

        except httpx.RequestError as e:
            logger.error(f"Request error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Request error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

    def extract_converted_content(self, text: str) -> str:
        """
        Extract content between <converted_content> tags from the LLM response.

        This is a critical function as we rely entirely on the LLM model to perform
        unit conversions, and we need to extract only the converted result from
        the response.

        Args:
            text: The text response from the LLM

        Returns:
            The content between <converted_content> tags, or the original text if no tags found
        """
        if not text:
            logger.warning("Empty response received from LLM")
            return ""

        pattern = r"<converted_content>(.*?)</converted_content>"
        match = re.search(pattern, text, re.DOTALL)

        if match:
            extracted_content = match.group(1).strip()
            logger.info(f"Successfully extracted content between <converted_content> tags: {len(extracted_content)} characters")
            return extracted_content

        logger.warning("No <converted_content> tags found in the response. Using full response.")
        return text.strip()

# Create a singleton instance
volcano_engine = VolcanoEngineAPI()