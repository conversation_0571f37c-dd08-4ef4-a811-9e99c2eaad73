# LLM Module for Steel Unit Converter

This module handles all interactions with Large Language Models (LLMs) for the Steel Unit Converter application, specifically using the DeepSeek R1 model on 火山引擎 (Volcano Engine).

## Overview

The LLM module is designed to be a separated component that isolates the LLM functionality from the rest of the application. It provides a clean interface for other components to use LLM capabilities without direct knowledge of the underlying LLM provider.

## Key Features

- **Isolated LLM interactions**: All communication with 火山引擎 DeepSeek R1 is encapsulated within this module
- **Configuration management**: Environment variables for the LLM are managed centrally
- **Service layer**: High-level services built on top of the connector for specific use cases
- **Error handling**: Comprehensive error handling for LLM requests

## Components

1. **Connector** (`volcano_engine.py`): Handles the low-level communication with the 火山引擎 DeepSeek R1 API
2. **Service** (`service.py`): Provides high-level services for specific use cases like unit conversion
3. **Configuration** (`config.py`): Manages environment variables and configuration for the LLM

## Usage

The LLM module is not meant to be directly accessed by the frontend. Instead, it should be used by backend API endpoints that then serve the results to the frontend.

Example:

```python
from llm.service import llm_service

# In an API endpoint
result = await llm_service.convert_units(
    text="Convert 1 inch to cm",
    unit_system="metric"
)
```

## Example Input and Output

### Input
```
Steel plate thickness: .015" (+/- .0015")
Hole diameter: 3/16"
Length: 1.5"
```

### Output (Metric Conversion)
```
Steel plate thickness: 0.38mm (+/- 0.04mm)
Hole diameter: 4.76mm
Length: 38.10mm
```

## Configuration

The LLM module requires several environment variables to be set:

- `VOLCANO_ENGINE_API_KEY` or `ARK_API_KEY`: API key for 火山引擎
- `VOLCANO_ENGINE_ENDPOINT_ID`: Endpoint ID for DeepSeek R1 model (defaults to "deepseek-r1")
- `VOLCANO_ENGINE_API_URL`: API URL for 火山引擎 (defaults to "https://ark.cn-beijing.volces.com/api/v3/chat/completions")
- `VOLCANO_ENGINE_TIMEOUT`: Timeout for API requests in seconds (defaults to 300)
- `VOLCANO_ENGINE_PROMPT_TEMPLATE`: Path to the prompt template file (defaults to "llm_prompt.txt")

These variables can be set in the `.env` file at the root of the backend directory. 