import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Head<PERSON>
from email.utils import formataddr, make_msgid
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# SMTP Settings
smtp_server = os.getenv("SMTP_SERVER", "smtp.qiye.aliyun.com")
smtp_port = int(os.getenv("SMTP_PORT", "25"))  # Use port 25 with STARTTLS
smtp_username = os.getenv("SMTP_USERNAME", "<EMAIL>")
smtp_password = os.getenv("SMTP_PASSWORD", "STEELnet456456")

# Recipient
recipient = "<EMAIL>"

print(f"Testing SMTP connection to: {smtp_server}:{smtp_port}")
print(f"From: {smtp_username}")
print(f"To: {recipient}")

# Create message - Use a more professional format with both HTML and plain text
msg = MIMEMultipart('alternative')

# Set proper headers for better deliverability
msg['Message-ID'] = make_msgid(domain='steelnet.ai')
msg['Subject'] = "SteelNet Verification Test"  # Avoid special characters in subject
msg['From'] = formataddr(("SteelNet", smtp_username))  # Use ASCII characters in From name
msg['To'] = recipient
msg['Reply-To'] = smtp_username  # Add explicit Reply-To header

# Plain text version
text_content = 'This is a test email from the Steel Unit Converter application. Please verify that you received this message.'
msg.attach(MIMEText(text_content, 'plain', 'utf-8'))

# HTML version
html_content = """
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; }
        .container { padding: 20px; border: 1px solid #eee; }
        .header { color: #333; border-bottom: 1px solid #eee; padding-bottom: 10px; }
        .content { margin: 20px 0; }
        .footer { font-size: 12px; color: #777; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>SteelNet Email Test</h2>
        </div>
        <div class="content">
            <p>This is a test email from the Steel Unit Converter application.</p>
            <p>If you've received this email, it means our email delivery system is working correctly.</p>
            <p>Please let the administrator know that you've received this test message.</p>
        </div>
        <div class="footer">
            <p>This is an automated message from SteelNet. Please do not reply directly to this email.</p>
            <p>© 2025 SteelNet</p>
        </div>
    </div>
</body>
</html>
"""
msg.attach(MIMEText(html_content, 'html', 'utf-8'))

try:
    print(f"Connecting to {smtp_server}:{smtp_port}...")
    server = smtplib.SMTP(smtp_server, smtp_port, timeout=30)
    server.set_debuglevel(1)  # Enable verbose debug output
    
    print("Server connected, sending EHLO...")
    ehlo_response = server.ehlo()
    print(f"EHLO response: {ehlo_response}")
    
    print("Starting TLS...")
    starttls_response = server.starttls()
    print(f"STARTTLS response: {starttls_response}")
    
    # Send EHLO again after STARTTLS
    print("Sending EHLO again after STARTTLS...")
    ehlo_again_response = server.ehlo()
    print(f"EHLO again response: {ehlo_again_response}")
    
    print(f"Logging in with username: {smtp_username}")
    login_response = server.login(smtp_username, smtp_password)
    print(f"Login response: {login_response}")
    
    print(f"Sending email to {recipient}...")
    send_response = server.sendmail(smtp_username, recipient, msg.as_string())
    print(f"Send response: {send_response}")
    
    print("Email sent successfully!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
finally:
    try:
        print("Closing server connection...")
        server.quit()
    except:
        pass
    
print("Test complete!") 