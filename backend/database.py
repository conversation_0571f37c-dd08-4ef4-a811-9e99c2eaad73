from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import inspect
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from dotenv import load_dotenv
from sqlalchemy import create_engine
from sqlalchemy.orm import Session
import logging

load_dotenv()

# Set up logging
logger = logging.getLogger(__name__)

# Log environment variables for debugging
logger.info("Environment variables for database connection:")
logger.info(f"RDS_HOSTNAME: {os.getenv('RDS_HOSTNAME')}")
logger.info(f"RDS_PORT: {os.getenv('RDS_PORT')}")
logger.info(f"RDS_DB_NAME: {os.getenv('RDS_DB_NAME')}")
logger.info(f"RDS_USERNAME: {os.getenv('RDS_USERNAME')}")
logger.info(f"DATABASE_URL: {os.getenv('DATABASE_URL')}")

# Create Base class for declarative models
Base = declarative_base()

def get_database_url():
    """
    Get database URL for database configuration.
    """
    # Import settings here to avoid circular imports
    from config import settings

    # Check if we have RDS settings
    rds_host = settings.RDS_HOSTNAME

    # If RDS host is set, use MySQL
    if rds_host:
        rds_port = settings.RDS_PORT
        rds_db_name = settings.RDS_DB_NAME
        rds_username = settings.RDS_USERNAME
        rds_password = settings.RDS_PASSWORD

        # Log the RDS settings for debugging
        logger.info(f"RDS settings from config: host={rds_host}, port={rds_port}, db={rds_db_name}, user={rds_username}")

        # Construct the MySQL connection string
        db_url = f"mysql+pymysql://{rds_username}:{rds_password}@{rds_host}:{rds_port}/{rds_db_name}?charset=utf8mb4"
        logger.info(f"Using MySQL RDS configuration for database connection to {rds_host}:{rds_port}/{rds_db_name}")
    else:
        # In production, we should always use MySQL
        if os.getenv("ENV") == "production":
            # Try to get RDS settings from environment variables directly
            rds_host = os.getenv("RDS_HOSTNAME", "rm-uf6ky293vc3i3l991no.mysql.rds.aliyuncs.com")
            rds_port = os.getenv("RDS_PORT", "3306")
            rds_db_name = os.getenv("RDS_DB_NAME", "unit_converter")
            rds_username = os.getenv("RDS_USERNAME", "unit")
            rds_password = os.getenv("RDS_PASSWORD", "dnBW6x$^53$3Bxn")

            # Log the RDS settings for debugging
            logger.info(f"Using RDS settings from environment: host={rds_host}, port={rds_port}, db={rds_db_name}, user={rds_username}")

            # Construct the MySQL connection string
            db_url = f"mysql+pymysql://{rds_username}:{rds_password}@{rds_host}:{rds_port}/{rds_db_name}?charset=utf8mb4"
            logger.info(f"Using MySQL RDS configuration for database connection to {rds_host}:{rds_port}/{rds_db_name}")
        else:
            # Use SQLite as fallback only in development
            db_url = "sqlite:///./unit_converter.db"
            logger.info("Using SQLite database as fallback (development only)")

    return db_url

# Get database URL
DATABASE_URL = get_database_url()

def get_engine_config():
    """Get engine configuration with connection pooling settings optimized for MySQL 8.0"""
    # Common args for both sync and async engines
    engine_args = {
        "echo": os.getenv("SQL_ECHO", "False").lower() == "true",
    }

    # MySQL 8.0 specific settings
    if DATABASE_URL.startswith('mysql'):
        engine_args.update({
            # MySQL 8.0 specific connect args
            "connect_args": {
                "charset": "utf8mb4",
                "use_unicode": True,
                "connect_timeout": 30,
            },
        })

    # Connection pool settings
    if not DATABASE_URL.startswith('sqlite'):
        pool_size = int(os.getenv("DB_POOL_SIZE", "5"))
        max_overflow = int(os.getenv("DB_MAX_OVERFLOW", "10"))
        pool_timeout = int(os.getenv("DB_POOL_TIMEOUT", "30"))
        pool_recycle = int(os.getenv("DB_POOL_RECYCLE", "1800"))

        engine_args.update({
            "pool_size": pool_size,
            "max_overflow": max_overflow,
            "pool_timeout": pool_timeout,
            "pool_recycle": pool_recycle,
            "pool_pre_ping": True,  # Important for MySQL 8.0 to verify connections
        })

        logger.info(f"Configured connection pool: size={pool_size}, max_overflow={max_overflow}")

    # SQLite specific args
    if DATABASE_URL.startswith('sqlite'):
        engine_args["connect_args"] = {"check_same_thread": False}

    return engine_args

# Use synchronous engine
engine_config = get_engine_config()
engine = create_engine(DATABASE_URL, **engine_config)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Create async engine for async operations
async_engine_config = get_engine_config()
async_database_url = DATABASE_URL
if DATABASE_URL.startswith('sqlite'):
    try:
        import aiosqlite
        async_database_url = DATABASE_URL.replace('sqlite:///', 'sqlite+aiosqlite:///')
    except ImportError:
        logger.warning("aiosqlite not installed, falling back to synchronous SQLite")
elif DATABASE_URL.startswith('mysql'):
    try:
        import aiomysql
        async_database_url = DATABASE_URL.replace('mysql+pymysql', 'mysql+aiomysql')
    except ImportError:
        logger.warning("aiomysql not installed, falling back to synchronous MySQL")
elif DATABASE_URL.startswith('postgresql'):
    try:
        import asyncpg
        async_database_url = DATABASE_URL.replace('postgresql', 'postgresql+asyncpg')
    except ImportError:
        logger.warning("asyncpg not installed, falling back to synchronous PostgreSQL")

async_engine = create_async_engine(async_database_url, **async_engine_config)

# Create async session factory
AsyncSessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=async_engine,
    class_=AsyncSession,
)

# Dependency to get async DB session
async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSessionLocal() as session:
        yield session

# Initialize database
async def init_db():
    """Initialize database tables"""
    from models.user_model import User
    from models.conversion_model import Conversion
    from models.chat_history import ChatHistory, ChatLike
    from models.auth_model import VerificationCode

    try:
        logger.info("Initializing database tables...")
        # For SQLite, we can use sync engine for simplicity
        if DATABASE_URL.startswith('sqlite'):
            # Create all tables if they don't exist
            Base.metadata.create_all(bind=engine)
            logger.info("Database tables created successfully with SQLite.")
        else:
            # For other databases, use async engine
            async with async_engine.begin() as conn:
                # We need to use sync methods since inspect doesn't work with async
                def get_table_names(connection):
                    insp = inspect(connection)
                    return insp.get_table_names()

                tables = await conn.run_sync(get_table_names)

                if "users" not in tables:
                    # Create all tables if they don't exist
                    await conn.run_sync(Base.metadata.create_all)
                    logger.info("Database tables created successfully.")
                else:
                    logger.info("Database tables already exist. Ensuring all tables are up to date...")
                    # This won't drop any tables but will create any missing ones
                    await conn.run_sync(Base.metadata.create_all)
                    logger.info("Database schema update completed.")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise

