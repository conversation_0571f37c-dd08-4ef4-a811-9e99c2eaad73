from functools import lru_cache
import os
from dotenv import load_dotenv

# Load environment variables from .env file
# Try to load .env.production first, then fall back to .env
if os.path.exists(os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env.production')):
    load_dotenv(os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env.production'))
    print("Loaded environment variables from .env.production")
else:
    load_dotenv()
    print("Loaded environment variables from .env")

class Settings:
    def __init__(self):
        # Base Configuration
        self.ENV = os.getenv("ENV", "development")
        self.DEBUG = os.getenv("DEBUG", "False") == "True"

        # Database Configuration
        self.database_url = os.getenv("DATABASE_URL", "sqlite:///./unit_converter.db")

        # RDS Configuration
        self.RDS_HOSTNAME = os.getenv("RDS_HOSTNAME", "")
        self.RDS_PORT = os.getenv("RDS_PORT", "3306")
        self.RDS_DB_NAME = os.getenv("RDS_DB_NAME", "")
        self.RDS_USERNAME = os.getenv("RDS_USERNAME", "")
        self.RDS_PASSWORD = os.getenv("RDS_PASSWORD", "")

        # JWT Configuration
        self.secret_key = os.getenv("SECRET_KEY", "your_secret_key_here")
        self.algorithm = os.getenv("ALGORITHM", "HS256")
        self.access_token_expire_minutes = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))

        # Email Configuration
        self.smtp_server = os.getenv("SMTP_SERVER", "smtp.gmail.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_username = os.getenv("SMTP_USERNAME", "<EMAIL>")
        self.smtp_password = os.getenv("SMTP_PASSWORD", "your-app-specific-password")

        # CORS Configuration
        self.CORS_ORIGINS = os.getenv("CORS_ORIGINS", "http://localhost:3000,http://localhost:5173,http://localhost:5174,http://localhost:5175,http://localhost:5176,http://localhost:5177,http://localhost:5178,http://localhost:5179")

        # Rate Limiting
        self.RATE_LIMIT_PER_MINUTE = int(os.getenv("RATE_LIMIT_PER_MINUTE", "60"))

        # DeepSeek API Configuration
        self.DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
        self.DEEPSEEK_API_URL = os.getenv("DEEPSEEK_API_URL", "https://api.volcengine.com/v1/llm/generation")
        self.DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "deepseek-v2")
        self.DEEPSEEK_TIMEOUT = int(os.getenv("DEEPSEEK_TIMEOUT", "30"))

        # Performance & Memory Management
        self.WORKERS_PER_CORE = float(os.getenv("WORKERS_PER_CORE", "1.0"))
        self.MAX_WORKERS = int(os.getenv("MAX_WORKERS", "4"))
        self.WEB_CONCURRENCY = int(os.getenv("WEB_CONCURRENCY", "1"))

        # Monitoring & Logging
        self.LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
        self.SENTRY_DSN = os.getenv("SENTRY_DSN", "")

@lru_cache()
def get_settings():
    return Settings()

# Single instance for importing elsewhere
settings = get_settings()
