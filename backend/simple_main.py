from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
import sys

# Setup Python path to allow local imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import conversion router only for testing
import routers.conversion as conversion_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create FastAPI application
app = FastAPI(
    title="Steel Unit Converter API (Simple Version)",
    description="Simplified API for testing unit conversion",
    version="1.0.0",
)

# CORS middleware
origins = ["*"]  # Allow all origins for testing
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Include only the conversion router
app.include_router(conversion_router.router)  # Conversion router already has '/api/conversion' prefix

# Root endpoint
@app.get("/")
async def root():
    return {"message": "Welcome to the Simplified Steel Unit Converter API"}

# Test endpoint
@app.get("/test")
async def test():
    return {"message": "API test endpoint is working"}

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("simple_main:app", host="127.0.0.1", port=8000, reload=True) 