#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check for and install missing dependencies

This script is run before starting the application to ensure all required
dependencies are installed.
"""
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("dependency-checker")

def check_and_install_package(package_name, import_name=None):
    """Check if a package is installed and install it if not"""
    if import_name is None:
        import_name = package_name.replace('-', '_')

    try:
        __import__(import_name)
        logger.info(f"Package {package_name} is already installed")
        return True
    except ImportError:
        logger.warning(f"Package {package_name} is not installed. Attempting to install...")

        # Try different installation methods
        methods = [
            # Regular pip install
            [sys.executable, "-m", "pip", "install", package_name],

            # With --break-system-packages for externally managed environments
            [sys.executable, "-m", "pip", "install", "--break-system-packages", package_name],

            # With --user flag
            [sys.executable, "-m", "pip", "install", "--user", package_name]
        ]

        for method in methods:
            try:
                logger.info(f"Trying to install with command: {' '.join(method)}")
                subprocess.check_call(method)
                logger.info(f"Successfully installed {package_name}")
                return True
            except subprocess.CalledProcessError as e:
                logger.warning(f"Installation method failed: {e}")
                continue

        # If all methods failed, try pipx if available
        try:
            # Check if pipx is installed
            subprocess.check_call(["which", "pipx"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)

            # Try installing with pipx
            logger.info(f"Trying to install {package_name} with pipx")
            subprocess.check_call(["pipx", "install", package_name, "--include-deps"])
            logger.info(f"Successfully installed {package_name} with pipx")
            return True
        except subprocess.CalledProcessError:
            # pipx not available or installation failed
            logger.error(f"All installation methods failed for {package_name}")
            return False

def install_system_packages():
    """Install system packages needed for Python dependencies"""
    try:
        # Check for apt-get (Debian/Ubuntu)
        subprocess.check_call(["which", "apt-get"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        logger.info("Installing system packages with apt-get...")

        # Update package lists
        subprocess.check_call(["sudo", "apt-get", "update"], stdout=subprocess.DEVNULL)

        # Install required system packages
        packages = [
            "python3-dev",
            "libpq-dev",
            "default-libmysqlclient-dev",
            "build-essential",
            "python3-pip",
            "python3-venv",
            "pipx"
        ]
        subprocess.check_call(["sudo", "apt-get", "install", "-y"] + packages)
        logger.info("Successfully installed system packages with apt-get")
        return True
    except subprocess.CalledProcessError:
        try:
            # Check for yum (CentOS/RHEL/Alibaba Cloud Linux)
            subprocess.check_call(["which", "yum"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            logger.info("Installing system packages with yum...")

            # Install required system packages
            packages = [
                "python3-devel",
                "postgresql-devel",
                "mysql-devel",
                "gcc",
                "python3-pip"
            ]
            subprocess.check_call(["sudo", "yum", "install", "-y"] + packages)

            # Install pipx if not available
            try:
                subprocess.check_call(["which", "pipx"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            except subprocess.CalledProcessError:
                logger.info("Installing pipx...")
                subprocess.check_call([sys.executable, "-m", "pip", "install", "--user", "pipx"])
                subprocess.check_call([sys.executable, "-m", "pipx", "ensurepath"])

            logger.info("Successfully installed system packages with yum")
            return True
        except subprocess.CalledProcessError:
            logger.warning("Could not install system packages. Neither apt-get nor yum is available.")
            return False

def main():
    """Check and install all required dependencies"""
    # Try to install system packages first
    install_system_packages()

    # List of packages to check and install if missing
    packages = [
        # We're using python-dotenv directly instead of pydantic-settings
        ("python-dotenv==1.0.0", "dotenv"),
        ("pymysql", "pymysql"),
        ("aiomysql", "aiomysql"),
        ("aiosqlite", "aiosqlite"),
        ("gunicorn", "gunicorn"),
        ("uvicorn", "uvicorn"),
    ]

    success = True
    for package_name, import_name in packages:
        if not check_and_install_package(package_name, import_name):
            success = False

    if not success:
        logger.error("Failed to install some dependencies. The application may not work correctly.")
        sys.exit(1)

    logger.info("All dependencies are installed")

if __name__ == "__main__":
    main()
