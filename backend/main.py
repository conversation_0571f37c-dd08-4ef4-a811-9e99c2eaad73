from fastapi import FastAP<PERSON>, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
import sys
from dotenv import load_dotenv
import time
from contextlib import asynccontextmanager
from prometheus_client import Counter, Histogram, generate_latest
from fastapi.responses import Response, JSONResponse
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration

# Setup Python path to allow local imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_db
import routers.auth as auth_router
import routers.email as email_router
import routers.llm as llm_router
import routers.chat as chat_router
import routers.conversion as conversion_router
import routers.health as health_router
from config import settings

# MySQL adapter for RDS optimization
from mysql_adapter import apply_mysql_optimizations

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize Sentry for error tracking if configured
if settings.SENTRY_DSN:
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        traces_sample_rate=0.1,
        integrations=[FastApiIntegration()]
    )

# Setup Prometheus metrics
try:
    # Try to create metrics, but handle case where they already exist
    REQUESTS = Counter(
        "http_requests_total",
        "Total count of HTTP requests by method and path",
        ["method", "path"],
        registry=None  # Don't register automatically
    )
    RESPONSE_TIME = Histogram(
        "http_request_duration_seconds",
        "HTTP response time in seconds",
        ["method", "path", "status"],
        registry=None  # Don't register automatically
    )
except ValueError:
    # If metrics already exist, just use the existing ones
    logger.warning("Prometheus metrics already registered, using existing metrics")
    from prometheus_client import REGISTRY
    REQUESTS = REGISTRY.get_sample_value("http_requests_total")
    RESPONSE_TIME = REGISTRY.get_sample_value("http_request_duration_seconds")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Load environment variables first
    load_dotenv()

    # Import database configuration
    from database import DATABASE_URL

    # Apply MySQL optimizations
    try:
        apply_mysql_optimizations()
        logger.info("MySQL RDS optimizations applied")
    except Exception as e:
        logger.error(f"Failed to apply MySQL optimizations: {e}")

    # Initialize the database
    await init_db()
    logger.info("Database initialized")

    # Startup operations
    logger.info("Application startup complete")

    yield

    # Shutdown operations
    logger.info("Application shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Steel Unit Converter API",
    description="API for converting steel unit measurements",
    version="1.0.0",
    lifespan=lifespan,
)

# CORS middleware - Allow all origins in development mode
if settings.ENV == "development" or settings.DEBUG:
    # In development mode, allow all origins for easier testing
    logger.info("Development mode: Configuring CORS to allow all origins")
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # This should allow all origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["*"],
        max_age=86400,  # 24 hours cache for preflight requests
    )
else:
    # In production mode, use the configured origins
    origins = settings.CORS_ORIGINS.split(",") if settings.CORS_ORIGINS else ["*"]
    # Always include localhost origins for development
    if "*" not in origins:
        # Add all possible localhost ports for development
        for port in range(3000, 3010):
            origins.append(f"http://localhost:{port}")
        for port in range(5173, 5190):
            origins.append(f"http://localhost:{port}")
        # Also add 127.0.0.1 for local testing
        for port in range(3000, 3010):
            origins.append(f"http://127.0.0.1:{port}")
        for port in range(5173, 5190):
            origins.append(f"http://127.0.0.1:{port}")
    logger.info(f"Production mode: Configuring CORS with origins: {origins}")
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["*"],
        max_age=86400,  # 24 hours cache for preflight requests
    )

# Middleware for monitoring
@app.middleware("http")
async def add_metrics(request: Request, call_next):
    # Record the start time
    start_time = time.time()

    # Process the request
    response = await call_next(request)

    # Record metrics
    path = request.url.path
    method = request.method
    status = response.status_code

    # Skip metrics route itself
    if path != "/metrics":
        REQUESTS.labels(method=method, path=path).inc()
        RESPONSE_TIME.labels(method=method, path=path, status=status).observe(
            time.time() - start_time
        )

    return response

# Include routers
app.include_router(auth_router.router)  # Auth router already has '/auth' prefix
app.include_router(email_router.router, prefix="/api")
app.include_router(llm_router.router)  # LLM router already has '/api/llm' prefix
app.include_router(chat_router.router)  # Chat router already has '/api/chat' prefix
app.include_router(conversion_router.router)  # Conversion router already has '/api/conversion' prefix
app.include_router(health_router.router)  # Health router already has '/api/health' prefix

# Root endpoint
@app.get("/")
async def root():
    return {"message": "Welcome to the Steel Unit Converter API"}

# Health check endpoint
@app.get("/health")
async def health_check():
    # Import database configuration
    from database import DATABASE_URL
    from config import settings

    # Determine database type and details
    if DATABASE_URL.startswith("mysql"):
        db_type = "MySQL RDS"
        db_host = settings.RDS_HOSTNAME
        db_name = settings.RDS_DB_NAME
    else:
        db_type = "SQLite"
        db_host = "local"
        db_name = "unit_converter.db"

    return {
        "status": "healthy",
        "database": {
            "type": db_type,
            "url": DATABASE_URL,
            "host": db_host,
            "name": db_name
        }
    }

# Metrics endpoint for Prometheus
@app.get("/metrics")
async def metrics():
    return Response(content=generate_latest(), media_type="text/plain")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)

