import smtplib
from email.mime.text import MIMEText
from email.header import <PERSON>er
import sys
import os
from dotenv import load_dotenv
import base64

# Load env variables
load_dotenv()

# Aliyun Enterprise Mail settings
smtp_server = os.getenv("SMTP_SERVER", "smtp.qiye.aliyun.com")
smtp_port = int(os.getenv("SMTP_PORT", "465"))
username = os.getenv("SMTP_USERNAME", "<EMAIL>")
password = os.getenv("SMTP_PASSWORD", "STEELnet456456")

# Test recipient
recipient = "<EMAIL>"  # Replace with your test email

print(f"Testing SMTP connection to {smtp_server}:{smtp_port}")
print(f"From: {username}")
print(f"To: {recipient}")

# Try with different authentication methods
methods = [
    "standard",
    "plain",
    "login_only",
    "try_without_domain"
]

for method in methods:
    print(f"\n\n===== Trying authentication method: {method} =====")
    
    # Connect to server
    if smtp_port == 465:
        server = smtplib.SMTP_SSL(smtp_server, smtp_port)
    else:
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
    
    server.set_debuglevel(1)
    print(f"EHLO response: {server.ehlo()}")
    
    try:
        # Try different authentication methods
        if method == "standard":
            print(f"Attempting login with standard method: {username}")
            server.login(username, password)
        
        elif method == "plain":
            print(f"Attempting login with PLAIN auth: {username}")
            auth_string = f"\0{username}\0{password}"
            auth_string_b64 = base64.b64encode(auth_string.encode()).decode()
            server.docmd("AUTH PLAIN", auth_string_b64)
        
        elif method == "login_only":
            print(f"Attempting login with LOGIN auth: {username}")
            server.docmd("AUTH LOGIN")
            server.docmd(base64.b64encode(username.encode()).decode())
            server.docmd(base64.b64encode(password.encode()).decode())
        
        elif method == "try_without_domain":
            # Try with just the username part (without @domain.com)
            simple_username = username.split('@')[0]
            print(f"Attempting login without domain: {simple_username}")
            server.login(simple_username, password)
        
        print(f"Success! Authentication method {method} worked.")
        
        # Try to send a test email
        msg = MIMEText('This is a test email from the Steel Unit Converter application.', 'plain', 'utf-8')
        msg['Subject'] = Header('SMTP Test', 'utf-8')
        msg['From'] = f'钢铁智联 <{username}>'
        msg['To'] = recipient
        
        print(f"Sending test email to {recipient}...")
        server.sendmail(username, recipient, msg.as_string())
        print("Email sent successfully!")
        
        # Success! We can break the loop
        break
        
    except Exception as e:
        print(f"Method {method} failed: {str(e)}")
    
    finally:
        try:
            server.quit()
        except:
            pass

print("\nTest complete!")
print("If all methods failed, please try these troubleshooting steps:")
print("1. Verify your username and password in the Aliyun console")
print("2. Check if SMTP is enabled for this account in the Aliyun Enterprise Mail settings")
print("3. Some Aliyun Enterprise Mail accounts require an app-specific password")
print("4. Try accessing your account through webmail to ensure it's active")
print("5. Contact Aliyun support if problems persist") 
 