"""
Password validation utility for the Steel Unit Converter backend.
Ensures consistent password requirements across the application.
"""

import re
from typing import Dict, List


def validate_password(password: str) -> Dict[str, any]:
    """
    Validate password according to application requirements.
    
    Requirements:
    - At least 6 characters long
    - Contains at least one uppercase letter (A-Z)
    - Contains at least one lowercase letter (a-z)
    - Contains at least one number (0-9)
    
    Args:
        password (str): The password to validate
        
    Returns:
        Dict containing:
        - is_valid (bool): Whether the password meets all requirements
        - errors (List[str]): List of validation error messages
        - requirements_met (Dict[str, bool]): Which requirements are met
    """
    errors = []
    requirements_met = {
        'min_length': False,
        'has_uppercase': False,
        'has_lowercase': False,
        'has_number': False
    }
    
    # Check minimum length (6 characters)
    if len(password) >= 6:
        requirements_met['min_length'] = True
    else:
        errors.append("Password must be at least 6 characters long")
    
    # Check for uppercase letters
    if re.search(r'[A-Z]', password):
        requirements_met['has_uppercase'] = True
    else:
        errors.append("Password must contain at least one uppercase letter")
    
    # Check for lowercase letters
    if re.search(r'[a-z]', password):
        requirements_met['has_lowercase'] = True
    else:
        errors.append("Password must contain at least one lowercase letter")
    
    # Check for numbers
    if re.search(r'\d', password):
        requirements_met['has_number'] = True
    else:
        errors.append("Password must contain at least one number")
    
    is_valid = len(errors) == 0
    
    return {
        'is_valid': is_valid,
        'errors': errors,
        'requirements_met': requirements_met
    }


def get_password_requirements() -> str:
    """
    Get a human-readable string describing password requirements.
    
    Returns:
        str: Password requirements description
    """
    return "Password must be at least 6 characters, including uppercase, lowercase letters and numbers"


def validate_password_simple(password: str) -> bool:
    """
    Simple boolean validation for password.
    
    Args:
        password (str): The password to validate
        
    Returns:
        bool: True if password meets all requirements, False otherwise
    """
    result = validate_password(password)
    return result['is_valid'] 