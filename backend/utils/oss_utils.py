"""
Aliyun OSS Utility Module

This module provides utilities for interacting with Aliyun Object Storage Service (OSS).
It handles operations like uploading, downloading, and managing chat history stored in OSS.
"""
import os
import json
import logging
import uuid
import pathlib
from typing import Dict, List, Any, Optional
import oss2
from datetime import datetime

logger = logging.getLogger(__name__)

class OSSManager:
    """
    Manager for Aliyun OSS operations.
    This class handles all interactions with Aliyun OSS for storing and retrieving chat history.
    If OSS is not configured, falls back to local file storage.
    """

    def __init__(self):
        """Initialize the OSS manager with credentials from environment variables."""
        self.access_key_id = os.getenv('ALIYUN_OSS_ACCESS_KEY_ID')
        self.access_key_secret = os.getenv('ALIYUN_OSS_ACCESS_KEY_SECRET')
        self.endpoint = os.getenv('ALIYUN_OSS_ENDPOINT')
        self.bucket_name = os.getenv('ALIYUN_OSS_BUCKET_NAME')
        self.prefix = os.getenv('ALIYUN_OSS_PREFIX', 'chat_histories/')

        # Set up local storage fallback
        self.local_storage_path = os.getenv('LOCAL_STORAGE_PATH', 'data/chat_histories')
        pathlib.Path(self.local_storage_path).mkdir(parents=True, exist_ok=True)

        self.is_configured = all([
            self.access_key_id,
            self.access_key_secret,
            self.endpoint,
            self.bucket_name
        ])

        if self.is_configured:
            # Initialize OSS auth and bucket
            self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)
            self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucket_name)
            logger.info(f"OSS Manager initialized for bucket: {self.bucket_name}")
        else:
            logger.warning("OSS Manager not fully configured. Using local file storage fallback.")

    def generate_object_key(self, user_id: int, session_id: str) -> str:
        """
        Generate a unique object key for storing chat history in OSS.

        Args:
            user_id: The ID of the user who owns the chat
            session_id: A unique identifier for the chat session

        Returns:
            A unique object key string
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{self.prefix}{user_id}/{session_id}_{timestamp}.json"

    def save_chat_history(self, user_id: int, session_id: str, messages: List[Dict[str, Any]]) -> Optional[str]:
        """
        Save chat history to OSS or local file storage.

        Args:
            user_id: The ID of the user who owns the chat
            session_id: A unique identifier for the chat session
            messages: The list of chat messages to save

        Returns:
            The object key if successful, None otherwise
        """
        # Create a structured object to store
        chat_data = {
            "user_id": user_id,
            "session_id": session_id,
            "timestamp": datetime.now().isoformat(),
            "messages": messages
        }

        # Convert to JSON
        json_data = json.dumps(chat_data, ensure_ascii=False)

        # Generate object key
        object_key = self.generate_object_key(user_id, session_id)

        if self.is_configured:
            try:
                # Upload to OSS
                self.bucket.put_object(object_key, json_data)
                logger.info(f"Chat history saved to OSS: {object_key}")
                return object_key
            except Exception as e:
                logger.error(f"Error saving chat history to OSS: {str(e)}")
                # Fall back to local storage
                return self._save_to_local_storage(object_key, json_data)
        else:
            # Use local file storage
            return self._save_to_local_storage(object_key, json_data)

    def get_chat_history(self, object_key: str) -> Optional[List[Dict[str, Any]]]:
        """
        Retrieve chat history from OSS or local file storage.

        Args:
            object_key: The object key for the chat history

        Returns:
            The list of chat messages if successful, None otherwise
        """
        if self.is_configured:
            try:
                # Get object from OSS
                obj = self.bucket.get_object(object_key)

                # Parse JSON data
                chat_data = json.loads(obj.read())

                # Return messages
                return chat_data.get("messages", [])
            except oss2.exceptions.NoSuchKey:
                logger.warning(f"Chat history not found in OSS: {object_key}")
                # Try local storage as fallback
                return self._get_from_local_storage(object_key)
            except Exception as e:
                logger.error(f"Error retrieving chat history from OSS: {str(e)}")
                # Try local storage as fallback
                return self._get_from_local_storage(object_key)
        else:
            # Use local file storage
            return self._get_from_local_storage(object_key)

    def update_chat_history(self, object_key: str, messages: List[Dict[str, Any]]) -> bool:
        """
        Update existing chat history in OSS or local file storage.

        Args:
            object_key: The object key for the chat history
            messages: The updated list of chat messages

        Returns:
            True if successful, False otherwise
        """
        # Get existing chat data
        existing_messages = self.get_chat_history(object_key)
        if existing_messages is None:
            logger.error(f"Cannot update chat history: {object_key} not found")
            return False

        # Create updated chat data
        chat_data = {
            "messages": messages,
            "updated_at": datetime.now().isoformat()
        }

        # Convert to JSON
        json_data = json.dumps(chat_data, ensure_ascii=False)

        if self.is_configured:
            try:
                # Upload to OSS
                self.bucket.put_object(object_key, json_data)
                logger.info(f"Chat history updated in OSS: {object_key}")
                return True
            except Exception as e:
                logger.error(f"Error updating chat history in OSS: {str(e)}")
                # Try local storage as fallback
                return self._save_to_local_storage(object_key, json_data) is not None
        else:
            # Use local file storage
            return self._save_to_local_storage(object_key, json_data) is not None

    def delete_chat_history(self, object_key: str) -> bool:
        """
        Delete chat history from OSS or local file storage.

        Args:
            object_key: The object key for the chat history

        Returns:
            True if successful, False otherwise
        """
        success = False

        # Try to delete from OSS if configured
        if self.is_configured:
            try:
                self.bucket.delete_object(object_key)
                logger.info(f"Chat history deleted from OSS: {object_key}")
                success = True
            except Exception as e:
                logger.error(f"Error deleting chat history from OSS: {str(e)}")

        # Also try to delete from local storage (as it might exist in both places)
        try:
            local_path = self._get_local_path(object_key)
            if os.path.exists(local_path):
                os.remove(local_path)
                logger.info(f"Chat history deleted from local storage: {local_path}")
                success = True
        except Exception as e:
            logger.error(f"Error deleting chat history from local storage: {str(e)}")

        return success

    def save_raw_data(self, object_key: str, data: str) -> bool:
        """
        Save raw string data to OSS or local file storage.

        Args:
            object_key: The object key for the data
            data: The string data to save

        Returns:
            True if successful, False otherwise
        """
        if self.is_configured:
            try:
                # Upload to OSS
                self.bucket.put_object(object_key, data)
                logger.info(f"Raw data saved to OSS: {object_key}")
                return True
            except Exception as e:
                logger.error(f"Error saving raw data to OSS: {str(e)}")
                # Fall back to local storage
                return self._save_raw_data_to_local(object_key, data)
        else:
            # Use local file storage
            return self._save_raw_data_to_local(object_key, data)

    def get_raw_data(self, object_key: str) -> Optional[str]:
        """
        Retrieve raw string data from OSS or local file storage.

        Args:
            object_key: The object key for the data

        Returns:
            The string data if successful, None otherwise
        """
        if self.is_configured:
            try:
                # Get object from OSS
                obj = self.bucket.get_object(object_key)

                # Read data as string
                return obj.read().decode('utf-8')
            except oss2.exceptions.NoSuchKey:
                logger.warning(f"Raw data not found in OSS: {object_key}")
                # Try local storage as fallback
                return self._get_raw_data_from_local(object_key)
            except Exception as e:
                logger.error(f"Error retrieving raw data from OSS: {str(e)}")
                # Try local storage as fallback
                return self._get_raw_data_from_local(object_key)
        else:
            # Use local file storage
            return self._get_raw_data_from_local(object_key)

    def _get_local_path(self, object_key: str) -> str:
        """
        Convert an object key to a local file path.

        Args:
            object_key: The object key

        Returns:
            The local file path
        """
        # Replace any path separators in the object key with underscores
        safe_key = object_key.replace('/', '_').replace('\\', '_')
        return os.path.join(self.local_storage_path, safe_key)

    def _save_to_local_storage(self, object_key: str, json_data: str) -> Optional[str]:
        """
        Save chat history to local file storage.

        Args:
            object_key: The object key
            json_data: The JSON data to save

        Returns:
            The object key if successful, None otherwise
        """
        try:
            local_path = self._get_local_path(object_key)
            with open(local_path, 'w', encoding='utf-8') as f:
                f.write(json_data)
            logger.info(f"Chat history saved to local storage: {local_path}")
            return object_key
        except Exception as e:
            logger.error(f"Error saving chat history to local storage: {str(e)}")
            return None

    def _get_from_local_storage(self, object_key: str) -> Optional[List[Dict[str, Any]]]:
        """
        Retrieve chat history from local file storage.

        Args:
            object_key: The object key

        Returns:
            The list of chat messages if successful, None otherwise
        """
        try:
            local_path = self._get_local_path(object_key)
            if not os.path.exists(local_path):
                logger.warning(f"Chat history not found in local storage: {local_path}")
                return None

            with open(local_path, 'r', encoding='utf-8') as f:
                chat_data = json.load(f)

            return chat_data.get("messages", [])
        except Exception as e:
            logger.error(f"Error retrieving chat history from local storage: {str(e)}")
            return None

    def _save_raw_data_to_local(self, object_key: str, data: str) -> bool:
        """
        Save raw string data to local file storage.

        Args:
            object_key: The object key
            data: The string data to save

        Returns:
            True if successful, False otherwise
        """
        try:
            local_path = self._get_local_path(object_key)
            with open(local_path, 'w', encoding='utf-8') as f:
                f.write(data)
            logger.info(f"Raw data saved to local storage: {local_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving raw data to local storage: {str(e)}")
            return False

    def _get_raw_data_from_local(self, object_key: str) -> Optional[str]:
        """
        Retrieve raw string data from local file storage.

        Args:
            object_key: The object key

        Returns:
            The string data if successful, None otherwise
        """
        try:
            local_path = self._get_local_path(object_key)
            if not os.path.exists(local_path):
                logger.warning(f"Raw data not found in local storage: {local_path}")
                return None

            with open(local_path, 'r', encoding='utf-8') as f:
                data = f.read()

            return data
        except Exception as e:
            logger.error(f"Error retrieving raw data from local storage: {str(e)}")
            return None

# Create a singleton instance
oss_manager = OSSManager()