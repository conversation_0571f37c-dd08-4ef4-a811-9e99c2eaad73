import requests
import time
import json
from datetime import datetime
import random
import string

# Configuration
BASE_URL = "http://localhost:8000/api"
GMAIL_EMAIL = "<EMAIL>"
QQ_EMAIL = "<EMAIL>"

# Generate random credentials for testing
random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
username = f"testuser_{random_suffix}"
password = f"Testpass{random_suffix}"
company_name = "Test Company"
country = "CN"

def random_email():
    """Generate a random email for testing"""
    return f"test_{random_suffix}@example.com"

def test_registration(email_type="gmail"):
    """Test registration with the specified email type"""
    email = GMAIL_EMAIL if email_type == "gmail" else QQ_EMAIL
    email_type_display = "Gmail" if email_type == "gmail" else "QQ"
    
    print(f"\n===== Testing Registration with {email_type_display} Email: {email} =====")
    print(f"Username: {username}")
    print(f"Password: {password}")
    
    # Step 1: Register user
    register_data = {
        "username": username,
        "email": email,
        "password": password,
        "company_name": company_name,
        "country": country
    }
    
    print("\n1. Registering user...")
    register_response = requests.post(f"{BASE_URL}/auth/register", json=register_data)
    
    if register_response.status_code != 201:  # Updated to expect 201 Created
        print(f"Registration failed! Status: {register_response.status_code}")
        print(f"Response: {register_response.text}")
        return
    
    print(f"Registration successful! Response: {register_response.json()}")
    print(f"Check backend console logs for verification code or check {email_type_display} email")
    
    # Step 2: Get verification code from user
    verification_code = input("\nEnter verification code (from console logs or email): ")
    
    # Step 3: Verify email
    verify_data = {
        "email": email,
        "code": verification_code
    }
    
    print("\n2. Verifying email...")
    verify_response = requests.post(f"{BASE_URL}/auth/verify-email", json=verify_data)
    
    if verify_response.status_code != 200:
        print(f"Verification failed! Status: {verify_response.status_code}")
        print(f"Response: {verify_response.text}")
        return
    
    print(f"Verification successful! Response: {verify_response.json()}")
    
    # Get token from response
    token = verify_response.json().get("access_token")
    
    # Step 4: Login with credentials
    print("\n3. Testing login...")
    
    login_data = {
        "username": username,
        "password": password
    }
    
    login_response = requests.post(
        f"{BASE_URL}/auth/token", 
        data=login_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    if login_response.status_code != 200:
        print(f"Login failed! Status: {login_response.status_code}")
        print(f"Response: {login_response.text}")
        return
    
    print(f"Login successful! Response: {login_response.json()}")
    print("\nRegistration flow completed successfully!")

def test_request_verification(email_type="gmail"):
    """Test requesting verification code for existing email"""
    email = GMAIL_EMAIL if email_type == "gmail" else QQ_EMAIL
    email_type_display = "Gmail" if email_type == "gmail" else "QQ"
    
    print(f"\n===== Testing Request Verification for {email_type_display} Email: {email} =====")
    
    # Request verification code
    verification_data = {
        "email": email
    }
    
    print(f"Requesting verification code for {email}...")
    verification_response = requests.post(f"{BASE_URL}/auth/request-verification", json=verification_data)
    
    if verification_response.status_code != 200:
        print(f"Request verification failed! Status: {verification_response.status_code}")
        print(f"Response: {verification_response.text}")
        return
    
    print(f"Verification code requested successfully! Response: {verification_response.json()}")
    print(f"Check backend console logs for verification code or check {email_type_display} email")

if __name__ == "__main__":
    print(f"Testing Account Flow at {datetime.now()}")
    print(f"API URL: {BASE_URL}")
    
    while True:
        print("\nChoose test to run:")
        print("1. Test Registration with Gmail")
        print("2. Test Registration with QQ Email")
        print("3. Test Request Verification for Gmail")
        print("4. Test Request Verification for QQ Email")
        print("5. Exit")
        
        choice = input("\nEnter option number: ")
        
        if choice == "1":
            test_registration("gmail")
        elif choice == "2":
            test_registration("qq")
        elif choice == "3":
            test_request_verification("gmail")
        elif choice == "4":
            test_request_verification("qq")
        elif choice == "5":
            print("Exiting...")
            break
        else:
            print("Invalid option. Please try again.") 