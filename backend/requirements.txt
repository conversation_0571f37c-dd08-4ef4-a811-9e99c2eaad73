fastapi==0.97.0
uvicorn==0.22.0
pydantic==1.10.9
# Use pydantic-settings v1.x for compatibility with pydantic v1.x
python-dotenv==1.0.0
sqlalchemy==2.0.16
# Using MySQL in production, not PostgreSQL
# asyncpg==0.27.0
pymysql==1.1.0
aiomysql==0.2.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
python-dotenv==1.0.0
email-validator==2.0.0
sentry-sdk==1.28.1
prometheus-client==0.17.0
# Only needed for Aliyun OSS integration
aliyun-python-sdk-core==2.13.36
oss2==2.17.0
httpx
six