import os
import pymysql
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

def test_connection():
    """Test direct connection to MySQL RDS."""
    try:
        # Database connection parameters
        host = os.environ.get("RDS_HOSTNAME", "rm-uf6ky293vc3i3l991no.mysql.rds.aliyuncs.com")
        port = int(os.environ.get("RDS_PORT", "3306"))
        user = os.environ.get("RDS_USERNAME", "unit")
        password = os.environ.get("RDS_PASSWORD", "dnBW6x$^53$3Bxn")
        database = os.environ.get("RDS_DB_NAME", "unit_converter")
        
        logger.info(f"Connecting to MySQL RDS at {host}:{port}/{database} as {user}")
        
        # Connect to the database
        conn = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        logger.info("Connection successful!")
        
        # Create cursor and execute a simple query
        cursor = conn.cursor()
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        logger.info(f"Database tables: {tables}")
        
        # Close connections
        cursor.close()
        conn.close()
        
        logger.info("Connection closed")
        return True
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        return False

if __name__ == "__main__":
    test_connection() 