from fastapi import FastAP<PERSON>, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
import sys
from dotenv import load_dotenv
import time
from contextlib import asynccontextmanager
from prometheus_client import Counter, Histogram, generate_latest
from fastapi.responses import Response, JSONResponse
import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration

# Setup Python path to allow local imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_db
import routers.auth as auth_router
import routers.email as email_router
import routers.llm as llm_router
import routers.chat as chat_router
from config import settings

# MySQL adapter for RDS optimization
from mysql_adapter import apply_mysql_optimizations

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize Sentry for error tracking if configured
if settings.SENTRY_DSN:
    sentry_sdk.init(
        dsn=settings.SENTRY_DSN,
        traces_sample_rate=0.1,
        integrations=[FastApiIntegration()]
    )

# Setup Prometheus metrics
REQUESTS = Counter(
    "http_requests_total", 
    "Total count of HTTP requests by method and path", 
    ["method", "path"]
)
RESPONSE_TIME = Histogram(
    "http_request_duration_seconds", 
    "HTTP response time in seconds",
    ["method", "path", "status"]
)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Check if using MySQL and apply optimizations
    if not os.getenv("DATABASE_URL", "").startswith("sqlite"):
        try:
            apply_mysql_optimizations()
            logger.info("MySQL RDS optimizations applied")
        except Exception as e:
            logger.error(f"Failed to apply MySQL optimizations: {e}")
    
    # Initialize the database
    await init_db()
    logger.info("Database initialized")
    
    # Load environment variables
    load_dotenv()
    
    # Startup operations
    logger.info("Application startup complete")
    
    yield
    
    # Shutdown operations
    logger.info("Application shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Steel Unit Converter API",
    description="API for converting steel unit measurements",
    version="1.0.0",
    lifespan=lifespan,
)

# CORS middleware
origins = settings.CORS_ORIGINS.split(",") if settings.CORS_ORIGINS else ["*"]
# Always include localhost origins for development
if "*" not in origins:
    origins.extend(["http://localhost:3000", "http://localhost:5173", "http://localhost:5174", "http://localhost:5175", "http://localhost:5176", "http://localhost:5177", "http://localhost:5178", "http://localhost:5179"])
logger.info(f"Configuring CORS with origins: {origins}")
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
    max_age=86400,  # 24 hours cache for preflight requests
)

# Middleware for monitoring
@app.middleware("http")
async def add_metrics(request: Request, call_next):
    # Record the start time
    start_time = time.time()
    
    # Process the request
    response = await call_next(request)
    
    # Record metrics
    path = request.url.path
    method = request.method
    status = response.status_code
    
    # Skip metrics route itself
    if path != "/metrics":
        REQUESTS.labels(method=method, path=path).inc()
        RESPONSE_TIME.labels(method=method, path=path, status=status).observe(
            time.time() - start_time
        )
    
    return response

# Include routers
app.include_router(auth_router.router, prefix="/api")
app.include_router(email_router.router, prefix="/api")
app.include_router(llm_router.router, prefix="/api")
app.include_router(chat_router.router, prefix="/api")

# Root endpoint
@app.get("/")
async def root():
    return {"message": "Welcome to the Steel Unit Converter API"}

# Health check endpoint
@app.get("/health")
async def health_check():
    # Include database type in health check
    db_type = "MySQL RDS" if not os.getenv("DATABASE_URL", "").startswith("sqlite") else "SQLite"
    return {"status": "healthy", "database": db_type}

# Metrics endpoint for Prometheus
@app.get("/metrics")
async def metrics():
    return Response(content=generate_latest(), media_type="text/plain")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 