import os
import smtplib
import socket
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# SMTP Settings
smtp_server = os.getenv("SMTP_SERVER", "smtp.qiye.aliyun.com")
smtp_port = int(os.getenv("SMTP_PORT", "465"))
smtp_username = os.getenv("SMTP_USERNAME", "<EMAIL>")
smtp_password = os.getenv("SMTP_PASSWORD", "STEELnet456456")

print(f"Testing SMTP connection to: {smtp_server}:{smtp_port}")
print(f"Username: {smtp_username}")
print(f"Password: {'*' * len(smtp_password)}")

# Step 1: Verify DNS resolution
try:
    print("\n1. Testing DNS resolution...")
    addr_info = socket.getaddrinfo(smtp_server, smtp_port, socket.AF_INET, socket.SOCK_STREAM)
    print(f"   ✅ Successfully resolved {smtp_server} to {addr_info[0][4][0]}")
except Exception as e:
    print(f"   ❌ Failed to resolve hostname: {str(e)}")
    exit(1)

# Step 2: Test connection
try:
    print("\n2. Testing SMTP connection...")
    if smtp_port == 465:
        server = smtplib.SMTP_SSL(smtp_server, smtp_port, timeout=10)
        print("   Using SSL connection")
    else:
        server = smtplib.SMTP(smtp_server, smtp_port, timeout=10)
        print("   Using standard connection")
    print(f"   ✅ Successfully connected to {smtp_server}:{smtp_port}")
except Exception as e:
    print(f"   ❌ Failed to connect: {str(e)}")
    exit(1)

# Step 3: Test authentication
try:
    print("\n3. Testing authentication...")
    if smtp_port == 587:
        server.starttls()
        print("   Started TLS")
    server.login(smtp_username, smtp_password)
    print(f"   ✅ Successfully authenticated as {smtp_username}")
except Exception as e:
    print(f"   ❌ Failed to authenticate: {str(e)}")
    server.quit()
    exit(1)

# Step 4: Test sending (optional)
test_send = input("\nDo you want to test sending an email? (y/n): ")
if test_send.lower() == 'y':
    recipient = input("Enter recipient email: ")
    try:
        print(f"\n4. Testing sending email to {recipient}...")
        message = f"""From: {smtp_username}
To: {recipient}
Subject: SMTP Test

This is a test email from the Steel Unit Converter application.
If you received this, the SMTP configuration is working correctly.
"""
        server.sendmail(smtp_username, recipient, message)
        print(f"   ✅ Successfully sent test email to {recipient}")
    except Exception as e:
        print(f"   ❌ Failed to send email: {str(e)}")

print("\nClosing connection...")
server.quit()
print("Test complete!") 
 