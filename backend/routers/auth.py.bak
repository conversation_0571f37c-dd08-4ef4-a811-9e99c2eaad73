from datetime import timed<PERSON><PERSON>, datetime
from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Form, Request
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel, EmailStr
import logging
from sqlalchemy import select, update
import uuid
import os

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from database import get_async_session
from models.user_model import User, UserType
from models.auth_model import VerificationCode
from auth_utils import (
    verify_password, 
    get_password_hash,
    create_access_token,
    verify_token,
    generate_verification_code,
    send_verification_email,
    ACCESS_TOKEN_EXPIRE_MINUTES
)

router = APIRouter(prefix="/api/auth", tags=["authentication"])
logger = logging.getLogger(__name__)

# Pydantic models for request/response
class Token(BaseModel):
    access_token: str
    token_type: str
    username: str
    email: str
    company_name: Optional[str] = None
    country: Optional[str] = None

class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str
    company_name: Optional[str] = None
    country: Optional[str] = None

class UserResponse(BaseModel):
    username: str
    email: str
    company_name: Optional[str] = None
    country: Optional[str] = None
    id: Optional[int] = None
    is_active: Optional[bool] = True
    is_verified: Optional[bool] = False
    _meta: Optional[Dict[str, Any]] = None
    
    class Config:
        orm_mode = True
        # Allow arbitrary types for the _meta field
        arbitrary_types_allowed = True
        # Custom model export config to include _meta in dict export
        @classmethod
        def schema_extra(cls, schema: Dict[str, Any]):
            props = schema.get('properties', {})
            props.pop('_meta', None)
            schema['properties'] = props

class VerifyEmail(BaseModel):
    email: EmailStr
    code: str

class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordResetConfirm(BaseModel):
    email: EmailStr
    code: str
    new_password: str

@router.post(
    "/register",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED
)
async def register_user(
    user: UserCreate,
    background_tasks: BackgroundTasks,
    session: AsyncSession = Depends(get_async_session)
):
    """
    Register a new user.
    """
    try:
        # Check if username already exists
        username_result = await session.execute(select(User).where(User.username == user.username))
        existing_username = username_result.scalars().first()
        if existing_username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )
        
        # Check if email already exists
        email_result = await session.execute(select(User).where(User.email == user.email))
        existing_email = email_result.scalars().first()
        if existing_email:
            # Check if user is verified
            # Check for verification codes that are marked as used
            vcode_result = await session.execute(
                select(VerificationCode)
                .where(VerificationCode.email == user.email)
                .where(VerificationCode.is_used == True)
            )
            verified_code = vcode_result.scalars().first()
            
            if verified_code or existing_email.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
            else:
                # If not verified, allow re-registration
                # We'll update the verification code instead of creating a new user
                verification_code = generate_verification_code()
                expires_at = datetime.utcnow() + timedelta(minutes=30)
                
                # Update the existing verification record
                vcode_result = await session.execute(
                    select(VerificationCode).where(VerificationCode.email == user.email)
                )
                existing_vcode = vcode_result.scalars().first()
                
                if existing_vcode:
                    existing_vcode.code = verification_code
                    existing_vcode.expires_at = expires_at
                    existing_vcode.username = user.username
                    existing_vcode.password_hash = get_password_hash(user.password)
                    existing_vcode.is_used = False
                else:
                    # Create new verification code
                    new_vcode = VerificationCode(
                        email=user.email,
                        code=verification_code,
                        expires_at=expires_at,
                        username=user.username,
                        password_hash=get_password_hash(user.password),
                        is_used=False
                    )
                    session.add(new_vcode)
                
                await session.commit()
                
                # Determine if this is a QQ email
                is_qq_email = '@qq.com' in user.email.lower()
                
                try:
                    # Send verification email
                    background_tasks.add_task(
                        send_verification_email,
                        user.email,
                        verification_code
                    )
                    
                    # Log verification code
                    logger.info(f"Verification code for {user.email}: {verification_code}")
                    
                    # Return appropriate response
                    response = UserResponse(
                        username=user.username,
                        email=user.email,
                        company_name=user.company_name,
                        country=user.country,
                        id=0,  # Placeholder
                        is_active=True,
                        is_verified=False
                    )
                    
                    if is_qq_email:
                        response._meta = {
                            "note": "For QQ email users: Please check your spam folder. The email subject will contain the verification code."
                        }
                    
                    return response
                except Exception as e:
                    logger.error(f"Failed to send verification email: {str(e)}")
                    await session.rollback()
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail=f"Failed to send verification email: {str(e)}"
                    )
        
        # Create verification code for new user
        verification_code = generate_verification_code()
        expires_at = datetime.utcnow() + timedelta(minutes=30)
        
        # Store verification details
        vcode = VerificationCode(
            email=user.email,
            code=verification_code,
            expires_at=expires_at,
            username=user.username,
            password_hash=get_password_hash(user.password),
            is_used=False
        )
        session.add(vcode)
        
        try:
            await session.commit()
            
            # Determine if this is a QQ email
            is_qq_email = '@qq.com' in user.email.lower()
            
            # Send verification email
            background_tasks.add_task(
                send_verification_email,
                user.email,
                verification_code
            )
            
            # Log verification code
            logger.info(f"Verification code for {user.email}: {verification_code}")
            
            # Return appropriate response
            response_data = {
                "username": user.username,
                "email": user.email,
                "company_name": user.company_name,
                "country": user.country,
                "id": 0,  # Placeholder ID until the user is verified
                "is_active": True,
                "is_verified": False,
            }
            
            if is_qq_email:
                response_data["_meta"] = {
                    "note": "For QQ email users: Please check your spam folder. The email subject will contain the verification code."
                }
            
            return response_data
        except Exception as e:
            logger.error(f"Failed to register user: {str(e)}")
            await session.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to register user: {str(e)}"
            )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors and return a generic error message
        logger.error(f"Unexpected error in register_user: {str(e)}")
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during registration. Please try again later."
        )

@router.post("/verify-email", response_model=Token)
async def verify_email(
    verification_data: VerifyEmail,
    session: AsyncSession = Depends(get_async_session)
):
    """
    Verify email with verification code and create user account if needed.
    """
    try:
        # Get verification record
        verification_result = await session.execute(
            select(VerificationCode).where(
                (VerificationCode.email == verification_data.email) &
                (VerificationCode.code == verification_data.code)
            )
        )
        verification = verification_result.scalars().first()
        
        # Verify that the verification code exists and is not expired or used
        if not verification:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification code"
            )
        
        # Check if the code is expired
        if verification.expires_at < datetime.utcnow():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Verification code expired"
            )
        
        # Check if the code has already been used
        if verification.is_used:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Verification code already used"
            )
        
        # Check if user already exists
        user_result = await session.execute(select(User).where(User.email == verification_data.email))
        user = user_result.scalars().first()
        
        if not user:
            # Create new user from verification data
            user = User(
                id=str(uuid.uuid4()),
                username=verification.username,
                email=verification.email,
                hashed_password=verification.password_hash,
                company_name=verification.company_name,
                country=verification.country,
                is_active=True
            )
            session.add(user)
        else:
            # Update user's active status if needed
            user.is_active = True
        
        # Mark verification code as used
        verification.is_used = True
        verification.user_id = user.id
        
        await session.commit()
        
        # Create and return token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
                    data={"sub": user.username},
            expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
                    "username": user.username,
                    "email": user.email,
                    "company_name": user.company_name,
                    "country": user.country
                }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors and return a generic error message
        logger.error(f"Unexpected error in verify_email: {str(e)}")
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during email verification. Please try again later."
        )

@router.post("/token", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    session: AsyncSession = Depends(get_async_session)
):
    """
    Authenticate user and generate JWT token.
    """
    # Find user by username
    result = await session.execute(select(User).where(User.username == form_data.username))
    user = result.scalars().first()
    
    # Verify user exists and password is correct
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Check if user is active
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create and return token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username},
        expires_delta=access_token_expires
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "username": user.username,
        "email": user.email,
        "company_name": user.company_name,
        "country": user.country
    }

@router.post("/request-verification")
async def request_verification(
    email_data: dict,
    background_tasks: BackgroundTasks,
    session: AsyncSession = Depends(get_async_session)
):
    """
    Request a new verification code for the given email.
    """
    try:
        email = email_data.get("email")
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email is required"
            )
        
        # Check if email exists in verification table or user table
        user_result = await session.execute(select(User).where(User.email == email))
        user = user_result.scalars().first()
        
        # Check if the user exists and is already verified (active user means verified)
        if user:
            # A user being active typically means they're verified
            # Check for verification codes that are marked as used
            vcode_result = await session.execute(
                select(VerificationCode)
                .where(VerificationCode.email == email)
                .where(VerificationCode.is_used == True)
            )
            verified_code = vcode_result.scalars().first()
            
            if verified_code:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email is already verified"
                )
        
        # Create verification code
        verification_code = generate_verification_code()
        
        # Calculate expiration time (30 minutes from now)
        expires_at = datetime.utcnow() + timedelta(minutes=30)
        
        # Check if there's an existing verification record
        verification_result = await session.execute(
            select(VerificationCode).where(VerificationCode.email == email)
        )
        existing_verification = verification_result.scalars().first()
        
        if existing_verification:
            # Update the existing verification code
            existing_verification.code = verification_code
            existing_verification.expires_at = expires_at
            existing_verification.is_used = False
        else:
            # If we don't have user info (usually we would have it from registration)
            # We'll just store the email and code for now
            new_verification = VerificationCode(
                email=email,
                code=verification_code,
                expires_at=expires_at,
                username=email.split('@')[0],  # Temporary username from email
                password_hash="",  # This will be updated during the actual registration
                is_used=False
            )
            session.add(new_verification)
        
        await session.commit()
        
        # Determine if this is a QQ email (for improved deliverability)
        is_qq_email = '@qq.com' in email.lower()
        
        # Send verification email in the background with appropriate subject for QQ email
        try:
            background_tasks.add_task(send_verification_email, email, verification_code)
            
            # Log the verification code (for debugging and recovery)
            logger.info(f"Verification code for {email}: {verification_code}")
            
            # Return appropriate response based on email type
            if is_qq_email:
                return {
                    "message": "Verification code sent. Please check your QQ email (including spam folder).",
                    "_meta": {
                        "note": "For QQ email users: Please check your spam folder. The email subject will contain the verification code."
                    }
                }
            else:
                return {"message": "Verification code sent"}
        except Exception as e:
            logger.error(f"Failed to send verification email: {str(e)}")
            await session.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to send verification email: {str(e)}"
            )
    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct status code
        raise
    except Exception as e:
        # Log unexpected errors and return a generic error message
        logger.error(f"Unexpected error in request_verification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred. Please try again later."
        )

@router.post("/login", response_model=Token)
async def login_endpoint(
    request: Request,
    session: AsyncSession = Depends(get_async_session)
):
    """
    Endpoint to handle frontend login requests with email/password in JSON format
    This acts as a proxy to the token endpoint that uses OAuth2PasswordRequestForm
    """
    try:
        # Get JSON data from request
        data = await request.json()
        email = data.get("email")
        password = data.get("password")
        
        if not email or not password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email and password are required"
            )
        
        # Find user by email
        result = await session.execute(select(User).where(User.email == email))
        user = result.scalars().first()
        
        # Verify user exists
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Verify password is correct
        if not verify_password(password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Check if user is active
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Inactive user",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create and return token
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user.username},
            expires_delta=access_token_expires
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "username": user.username,
            "email": user.email,
            "company_name": user.company_name,
            "country": user.country
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors and return a generic error message
        logger.error(f"Unexpected error in login_endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during login. Please try again later."
        )

@router.post("/reset-password/request")
async def request_password_reset(
    request_data: PasswordResetRequest,
    background_tasks: BackgroundTasks,
    session: AsyncSession = Depends(get_async_session)
):
    """
    Request a password reset code for an existing account
    """
    try:
        email = request_data.email
        
        # Check if user exists
        user_result = await session.execute(select(User).where(User.email == email))
        user = user_result.scalars().first()
        
        if not user:
            # For security reasons, still return success even if email doesn't exist
            return {"message": "If an account with this email exists, a password reset link has been sent"}
        
        # Generate verification code for reset
        reset_code = generate_verification_code()
        
        # Calculate expiration time (30 minutes from now)
        expires_at = datetime.utcnow() + timedelta(minutes=30)
        
        # Check if there's an existing verification record
        verification_result = await session.execute(
            select(VerificationCode).where(VerificationCode.email == email)
        )
        existing_verification = verification_result.scalars().first()
        
        if existing_verification:
            # Update the existing verification code
            existing_verification.code = reset_code
            existing_verification.expires_at = expires_at
            # Don't change is_used status - we're just updating the code
        else:
            # Create new verification record for this reset
            new_verification = VerificationCode(
                email=email,
                code=reset_code,
                expires_at=expires_at,
                username=user.username,  # Keep existing username
                password_hash="",  # Will be updated when reset is confirmed
                is_used=False
            )
            session.add(new_verification)
        
        await session.commit()
        
        # Determine if this is a QQ email (for improved deliverability)
        is_qq_email = '@qq.com' in email.lower()
        
        try:
            # Use the same send_verification_email function that works for verification emails
            # This function is properly configured and handles all SMTP settings and error cases
            # Log the reset code for easier testing and recovery
            logger.info(f"Password reset code for {email}: {reset_code}")
            
            # Send verification email in the background
            background_tasks.add_task(
                send_verification_email,
                email,
                reset_code
            )
            
            # Return appropriate response
            if is_qq_email:
                return {
                    "message": "Password reset code sent. Please check your QQ email (including spam folder).",
                    "_meta": {
                        "note": "For QQ email users: Please check your spam folder. The email subject will contain the verification code."
                    }
                }
            else:
                return {"message": "Password reset code sent"}
        except Exception as e:
            logger.error(f"Failed to send password reset email: {str(e)}")
            await session.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to send password reset email: {str(e)}"
            )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors and return a generic error message
        logger.error(f"Unexpected error in request_password_reset: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred. Please try again later."
        )

@router.post("/reset-password/confirm")
async def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    session: AsyncSession = Depends(get_async_session)
):
    """
    Confirm password reset with verification code and update user's password
    """
    try:
        # Get verification record
        verification_result = await session.execute(
            select(VerificationCode).where(
                (VerificationCode.email == reset_data.email) &
                (VerificationCode.code == reset_data.code)
            )
        )
        verification = verification_result.scalars().first()
        
        # Verify that the verification code exists and is not expired or used
        if not verification:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid verification code"
            )
        
        # Check if the code is expired
        if verification.expires_at < datetime.utcnow():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Verification code expired"
            )
        
        # Check if the code has already been used
        if verification.is_used:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Verification code already used"
            )
        
        # Find user by email
        user_result = await session.execute(select(User).where(User.email == reset_data.email))
        user = user_result.scalars().first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update user's password
        user.hashed_password = get_password_hash(reset_data.new_password)
        
        # Mark verification code as used
        verification.is_used = True
        
        await session.commit()
        
        return {"message": "Password reset successful. You can now log in with your new password."}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        # Log unexpected errors and return a generic error message
        logger.error(f"Unexpected error in confirm_password_reset: {str(e)}")
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during password reset. Please try again later."
        )
