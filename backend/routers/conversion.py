from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Request
from fastapi.security import OAuth2PasswordBearer
from pydantic import BaseModel
import logging

from sqlalchemy.ext.asyncio import AsyncSession

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_async_session
from llm.service import llm_service
from models.user_model import User
from models.conversion_model import Conversion
from auth_utils import get_current_user_optional

router = APIRouter(prefix="/api/conversion", tags=["conversion"])
logger = logging.getLogger(__name__)

class QueryRequest(BaseModel):
    text: str
    unit_system: str = "metric"
    model: str = "deepseek-v3-250324"  # Default to v3 model
    function: str = "general"  # general, conversion, or table

class QueryResponse(BaseModel):
    result: Dict[str, Any]
    message: str

@router.post("/", response_model=QueryResponse)
async def process_query(
    request: QueryRequest,
    current_user: Optional[User] = Depends(get_current_user_optional),
    session: AsyncSession = Depends(get_async_session)
):
    """
    Process a query using the appropriate LLM function (general query, unit conversion, or table creation).
    """
    try:
        # Set the appropriate parameters based on the function
        table_mode = request.function == "table"
        function = "conversion" if request.function in ["conversion", "table"] else "general"

        result = await llm_service.convert_units(
            text=request.text,
            unit_system=request.unit_system,
            model=request.model,
            function=function,
            table_mode=table_mode
        )

        # Store the query in the database if user is authenticated
        if current_user:
            try:
                conversion = Conversion(
                    user_id=current_user.id,
                    input_text=request.text,
                    from_unit="various",
                    to_unit="various",
                    input_value=0.0,
                    output_value=0.0,
                    unit_system=request.unit_system,
                    model=request.model,
                    function=request.function
                )
                session.add(conversion)
                await session.commit()
            except Exception as e:
                logger.error(f"Error storing query: {e}")
                # Continue even if storage fails

        return result
    except Exception as e:
        logger.error(f"Error in query endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/functions", response_model=List[Dict[str, Any]])
async def get_available_functions():
    """
    Get the list of available functions that the API can perform.
    """
    try:
        return await llm_service.get_available_functions()
    except Exception as e:
        logger.error(f"Error getting available functions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Backward compatibility for existing unit conversion endpoints
@router.post("/conversion", response_model=QueryResponse)
@router.post("/conversion/", response_model=QueryResponse)
async def convert_units(
    request: QueryRequest,
    current_user: Optional[User] = Depends(get_current_user_optional),
    session: AsyncSession = Depends(get_async_session)
):
    """
    Convert units using the 火山引擎 LLM API (backward compatibility endpoint).
    """
    # Force conversion function
    request.function = "conversion"
    return await process_query(request, current_user, session)
