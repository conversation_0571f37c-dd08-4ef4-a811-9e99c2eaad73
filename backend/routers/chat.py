"""
Chat Router - Handles API endpoints for chat history management

This router provides endpoints for managing chat history, including saving,
retrieving, updating, and deleting chat sessions.
"""
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel
import logging
import uuid

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.future import select
from sqlalchemy import delete, func

from database import AsyncSessionLocal
from auth_utils import get_current_user
from models.user_model import User
from models.chat_history import ChatHistory, ChatLike
from utils.oss_utils import oss_manager

router = APIRouter(prefix="/api/chat", tags=["chat"])
logger = logging.getLogger(__name__)

# Request and response models
class ChatSession(BaseModel):
    session_id: str
    title: Optional[str] = None
    messages: List[Dict[str, Any]]

class ChatMessage(BaseModel):
    content: str
    role: str
    function: str
    timestamp: str

class ChatResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class FeedbackRequest(BaseModel):
    chat_id: int
    message_index: int
    is_liked: bool
    feedback: Optional[str] = None

class SessionsRequest(BaseModel):
    sessions: List[Dict[str, Any]]

class ChatHistoryRequest(BaseModel):
    messages: str = ""  # Compressed messages string

class ChatHistoryResponse(BaseModel):
    messages: str = ""  # Compressed messages string

@router.post("/history", response_model=ChatHistoryResponse)
async def save_chat_history(
    history: ChatHistoryRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Save compressed chat history for a user.
    This endpoint is used for simple chat history storage without session management.
    """
    try:
        # Extract user ID from the detached user object
        user_id = current_user.id

        # Generate a unique key for this user's chat history
        object_key = f"chat_history/{user_id}/global_history.json"

        # Save the compressed messages directly to OSS
        success = oss_manager.save_raw_data(object_key, history.messages)

        if not success:
            logger.error(f"Failed to save chat history for user {user_id}")
            raise HTTPException(status_code=500, detail="Failed to save chat history")

        return {"messages": history.messages}
    except Exception as e:
        logger.error(f"Error saving chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history", response_model=ChatHistoryResponse)
async def get_chat_history(
    current_user: User = Depends(get_current_user)
):
    """
    Get compressed chat history for a user.
    This endpoint is used for simple chat history retrieval without session management.
    """
    try:
        # Extract user ID from the detached user object
        user_id = current_user.id

        # Generate the key for this user's chat history
        object_key = f"chat_history/{user_id}/global_history.json"

        # Try to get the compressed messages from OSS
        data = oss_manager.get_raw_data(object_key)

        if data is None:
            # If no history exists, return empty string
            return {"messages": ""}

        return {"messages": data}
    except Exception as e:
        logger.error(f"Error getting chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/save", response_model=ChatResponse)
async def save_chat_session(
    chat: ChatSession,
    current_user: User = Depends(get_current_user)
):
    """
    Save a chat session to the database and OSS storage.
    """
    try:
        # Extract user ID from the detached user object
        user_id = current_user.id
        # Generate a new session ID if not provided
        session_id = chat.session_id or str(uuid.uuid4())

        # Save messages to OSS
        object_key = oss_manager.save_chat_history(
            user_id=user_id,
            session_id=session_id,
            messages=chat.messages
        )

        if not object_key:
            raise HTTPException(status_code=500, detail="Failed to save chat history to OSS")

        async with AsyncSessionLocal() as session:
            async with session.begin():
                # Create or update chat history record
                result = await session.execute(
                    select(ChatHistory).where(
                        (ChatHistory.user_id == user_id) &
                        (ChatHistory.session_id == session_id)
                    )
                )
                existing_chat = result.scalars().first()

                if existing_chat:
                    # Update existing record
                    existing_chat.title = chat.title or existing_chat.title
                    existing_chat.oss_object_key = object_key
                    existing_chat.message_count = len(chat.messages)
                else:
                    # Create new record
                    new_chat = ChatHistory(
                        user_id=user_id,
                        session_id=session_id,
                        title=chat.title or "Chat Session",
                        oss_object_key=object_key,
                        message_count=len(chat.messages)
                    )
                    session.add(new_chat)

        return {
            "success": True,
            "message": "Chat session saved successfully",
            "data": {"session_id": session_id}
        }
    except Exception as e:
        logger.error(f"Error saving chat session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions", response_model=Dict[str, Any])
async def get_chat_sessions(
    current_user: User = Depends(get_current_user),
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    archived: bool = Query(False)
):
    """
    Get a list of chat sessions for the current user.
    """
    try:
        # Extract user ID from the detached user object
        user_id = current_user.id

        # Create a new session for this request
        async with AsyncSessionLocal() as session:
            # Query for chat sessions
            stmt = select(ChatHistory).where(
                (ChatHistory.user_id == user_id) &
                (ChatHistory.is_archived == archived)
            ).order_by(ChatHistory.last_message_at.desc()).limit(limit).offset(offset)

            result = await session.execute(stmt)

            chat_sessions = []
            for chat in result.scalars().all():
                chat_sessions.append({
                    "id": chat.id,
                    "session_id": chat.session_id,
                    "title": chat.title,
                    "message_count": chat.message_count,
                    "created_at": chat.created_at.isoformat(),
                    "last_message_at": chat.last_message_at.isoformat()
                })

            # Get total count
            count_stmt = select(func.count()).select_from(ChatHistory).where(
                (ChatHistory.user_id == user_id) &
                (ChatHistory.is_archived == archived)
            )
            count_result = await session.execute(count_stmt)
            total_count = count_result.scalar_one()

        return {
            "success": True,
            "data": {
                "sessions": chat_sessions,
                "total": total_count,
                "limit": limit,
                "offset": offset
            }
        }
    except Exception as e:
        logger.error(f"Error getting chat sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sessions", response_model=ChatResponse)
async def save_chat_sessions(
    sessions_data: SessionsRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Save multiple chat sessions for the current user.
    This endpoint is used to sync the frontend session cache with the backend.
    """
    # Extract user ID from the detached user object
    user_id = current_user.id
    try:
        async with AsyncSessionLocal() as session:
            async with session.begin():
                # Process each session
                for chat_data in sessions_data.sessions:
                    session_id = chat_data.get('id') or str(uuid.uuid4())
                    title = chat_data.get('title', 'Chat Session')
                    messages = chat_data.get('messages', [])

                    # Skip sessions with no user messages
                    has_user_messages = any(msg.get('type') == 'user' for msg in messages)
                    if not has_user_messages:
                        continue

                    # Save messages to OSS
                    object_key = oss_manager.save_chat_history(
                        user_id=user_id,
                        session_id=session_id,
                        messages=messages
                    )

                    if not object_key:
                        logger.error(f"Failed to save chat history to OSS for session {session_id}")
                        continue

                    # Check if session already exists
                    result = await session.execute(
                        select(ChatHistory).where(
                            (ChatHistory.user_id == user_id) &
                            (ChatHistory.session_id == session_id)
                        )
                    )
                    existing_chat = result.scalars().first()

                    if existing_chat:
                        # Update existing record
                        existing_chat.title = title
                        existing_chat.oss_object_key = object_key
                        existing_chat.message_count = len(messages)
                    else:
                        # Create new record
                        new_chat = ChatHistory(
                            user_id=user_id,
                            session_id=session_id,
                            title=title,
                            oss_object_key=object_key,
                            message_count=len(messages)
                        )
                        session.add(new_chat)

        return {
            "success": True,
            "message": "Chat sessions saved successfully"
        }
    except Exception as e:
        logger.error(f"Error saving chat sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/messages/{session_id}", response_model=ChatResponse)
async def get_chat_messages(
    session_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get chat messages for a specific session.
    """
    try:
        # Extract user ID from the detached user object
        user_id = current_user.id

        # Use proper async context
        async with AsyncSessionLocal() as session:
            # Get chat history record
            result = await session.execute(
                select(ChatHistory).where(
                    (ChatHistory.user_id == user_id) &
                    (ChatHistory.session_id == session_id)
                )
            )
            chat = result.scalars().first()

            if not chat:
                raise HTTPException(status_code=404, detail="Chat session not found")

        # Get messages from OSS (outside the session context since it's not a DB operation)
        messages = oss_manager.get_chat_history(chat.oss_object_key)

        if messages is None:
            raise HTTPException(status_code=404, detail="Chat messages not found in storage")

        return {
            "success": True,
            "message": "Chat messages retrieved successfully",
            "data": {
                "id": chat.id,
                "session_id": chat.session_id,
                "title": chat.title,
                "messages": messages,
                "created_at": chat.created_at.isoformat()
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chat messages: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/feedback", response_model=ChatResponse)
async def save_message_feedback(
    feedback: FeedbackRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Save feedback (like/dislike) for a specific chat message.
    """
    try:
        # Extract user ID from the detached user object
        user_id = current_user.id

        async with AsyncSessionLocal() as session:
            async with session.begin():
                # Check if the chat exists and belongs to the user
                result = await session.execute(
                    select(ChatHistory).where(
                        (ChatHistory.id == feedback.chat_id) &
                        (ChatHistory.user_id == user_id)
                    )
                )
                chat = result.scalars().first()

                if not chat:
                    raise HTTPException(status_code=404, detail="Chat session not found")

                # Check if feedback already exists
                result = await session.execute(
                    select(ChatLike).where(
                        (ChatLike.user_id == current_user.id) &
                        (ChatLike.chat_id == feedback.chat_id) &
                        (ChatLike.message_index == feedback.message_index)
                    )
                )
                existing_feedback = result.scalars().first()

                if existing_feedback:
                    # Update existing feedback
                    existing_feedback.is_liked = feedback.is_liked
                    existing_feedback.feedback = feedback.feedback
                else:
                    # Create new feedback
                    new_feedback = ChatLike(
                        user_id=user_id,
                        chat_id=feedback.chat_id,
                        message_index=feedback.message_index,
                        is_liked=feedback.is_liked,
                        feedback=feedback.feedback
                    )
                    session.add(new_feedback)

        return {
            "success": True,
            "message": "Feedback saved successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving message feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/session/{session_id}", response_model=ChatResponse)
async def delete_chat_session(
    session_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Delete a chat session and its messages.
    """
    try:
        # Extract user ID from the detached user object
        user_id = current_user.id

        object_key = None
        async with AsyncSessionLocal() as session:
            async with session.begin():
                # Get chat history record
                result = await session.execute(
                    select(ChatHistory).where(
                        (ChatHistory.user_id == user_id) &
                        (ChatHistory.session_id == session_id)
                    )
                )
                chat = result.scalars().first()

                if not chat:
                    raise HTTPException(status_code=404, detail="Chat session not found")

                # Store object key for OSS deletion after DB transaction
                object_key = chat.oss_object_key

                # Delete feedback records
                await session.execute(
                    delete(ChatLike).where(ChatLike.chat_id == chat.id)
                )

                # Delete chat history record
                await session.delete(chat)

        # Delete from OSS after DB transaction is complete
        if object_key:
            oss_result = oss_manager.delete_chat_history(object_key)

            if not oss_result:
                logger.warning(f"Failed to delete chat history from OSS: {object_key}")

        return {
            "success": True,
            "message": "Chat session deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting chat session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.patch("/archive/{session_id}", response_model=ChatResponse)
async def archive_chat_session(
    session_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Archive a chat session.
    """
    try:
        # Extract user ID from the detached user object
        user_id = current_user.id

        async with AsyncSessionLocal() as session:
            async with session.begin():
                # Get chat history record
                result = await session.execute(
                    select(ChatHistory).where(
                        (ChatHistory.user_id == user_id) &
                        (ChatHistory.session_id == session_id)
                    )
                )
                chat = result.scalars().first()

                if not chat:
                    raise HTTPException(status_code=404, detail="Chat session not found")

                # Update archive status
                chat.is_archived = True

        return {
            "success": True,
            "message": "Chat session archived successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error archiving chat session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))