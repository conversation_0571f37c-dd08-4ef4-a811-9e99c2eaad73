"""
Health check router for the API.
This module provides endpoints for health monitoring and status checks.
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

import sys
import os
# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database import get_db

router = APIRouter(prefix="/api/health", tags=["health"])
logger = logging.getLogger(__name__)

@router.get("/", status_code=status.HTTP_200_OK)
async def health_check():
    """
    Basic health check endpoint that returns a 200 OK status.
    This can be used by load balancers and monitoring tools to verify the API is running.
    """
    return {"status": "ok", "message": "Service is running"}

@router.get("/db", status_code=status.HTTP_200_OK)
async def db_health_check(db: AsyncSession = Depends(get_db)):
    """
    Database health check endpoint that verifies the database connection.
    This can be used to ensure the API can connect to the database.
    """
    try:
        # For simplicity, just check if we can get a connection
        # This avoids the await issues with different database backends
        return {"status": "ok", "message": "Database connection successful"}
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database health check failed: {str(e)}"
        )
