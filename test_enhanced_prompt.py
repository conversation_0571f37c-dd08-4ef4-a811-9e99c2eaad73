#!/usr/bin/env python3
"""
Test Enhanced Prompt - Verify the improved prompt generates correct table format
"""

import re

def simulate_enhanced_llm_response():
    """Simulate what the LLM should return with the enhanced prompt"""
    
    # This is what the enhanced prompt should generate
    enhanced_response = """Based on your steel specifications, I'll convert them from imperial to metric units and create a comprehensive table following the strict format requirements:

<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.343in(+/-0.005in) x COIL|7190#|0.38mm(+/-0.04mm) x 59.51mm(+/-0.13mm) x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.406in(+/-0.005in) x COIL|8061#|0.38mm(+/-0.04mm) x 61.11mm(+/-0.13mm) x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 16.50in(+/-0.005in) x COIL|12550#|0.38mm(+/-0.04mm) x 419.1mm(+/-0.13mm) x COIL|5692.7 kg|BA Finish
004|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 19.68in(+/-0.03125in) x COIL|8835#|0.38mm(+/-0.04mm) x 499.87mm(+/-0.79mm) x COIL|4007.5 kg|BA Finish
005|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 47.438in(+/-0.03125in) x COIL|57655#|0.38mm(+/-0.04mm) x 1204.93mm(+/-0.79mm) x COIL|26152.6 kg|BA Finish
006|S/S 430 #2B NO PI|0.015in(+/-0.0015in) x 16.938in(+/-0.005in) x COIL|5000#|0.38mm(+/-0.04mm) x 430.23mm(+/-0.13mm) x COIL|2268.0 kg|#2B Finish
007|S/S 430 #2B NO PI|0.016in(+/-0.0015in) x 19.6875in(+/-0.005in) x COIL|725321#|0.41mm(+/-0.04mm) x 500.06mm(+/-0.13mm) x COIL|329039.9 kg|#2B Finish
008|S/S 430 #2B NO PI|0.016in(+/-0.0015in) x 35.500in(+/-0.03125in) x COIL|122083#|0.41mm(+/-0.04mm) x 901.7mm(+/-0.79mm) x COIL|55376.9 kg|#2B Finish
009|S/S 430 #2B NO PI|0.016in(+/-0.0015in) x 36.000in(+/-0.03125in) x COIL|234265#|0.41mm(+/-0.04mm) x 914.4mm(+/-0.79mm) x COIL|106273.9 kg|#2B Finish
010|S/S 430 #2B NO PI|0.018in(+/-0.0015in) x 36.000in(+/-0.03125in) x COIL|33841#|0.46mm(+/-0.04mm) x 914.4mm(+/-0.79mm) x COIL|15350.9 kg|#2B Finish
</table_stream>

All conversions have been completed with proper tolerances maintained. The table shows both original imperial measurements and converted metric equivalents with appropriate weight conversions from pounds to kilograms."""
    
    return enhanced_response

def validate_table_format(response_text):
    """Validate that the table format follows all the enhanced prompt rules"""
    
    print("🔍 Validating Table Format Against Enhanced Prompt Rules")
    print("=" * 70)
    
    validation_results = {
        'has_table_stream_tags': False,
        'correct_header_format': False,
        'all_rows_have_7_fields': True,
        'no_pipes_in_content': True,
        'no_quotes_in_content': True,
        'uses_x_for_dimensions': True,
        'complete_rows': True,
        'correct_column_count': 0,
        'issues': []
    }
    
    # Check for table_stream tags
    if '<table_stream>' in response_text and '</table_stream>' in response_text:
        validation_results['has_table_stream_tags'] = True
        print("✅ Rule 1: Has <table_stream> tags")
    else:
        validation_results['issues'].append("Missing <table_stream> tags")
        print("❌ Rule 1: Missing <table_stream> tags")
    
    # Extract table content
    match = re.search(r'<table_stream>(.*?)</table_stream>', response_text, re.DOTALL)
    if match:
        table_content = match.group(1).strip()
        lines = [line.strip() for line in table_content.split('\n') if line.strip()]
        
        if lines:
            # Check header format
            header_line = lines[0]
            expected_header = "Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks"
            
            if header_line == expected_header:
                validation_results['correct_header_format'] = True
                print("✅ Rule 2: Correct header format")
            else:
                validation_results['issues'].append(f"Incorrect header format: {header_line}")
                print(f"❌ Rule 2: Incorrect header format")
                print(f"  Expected: {expected_header}")
                print(f"  Got: {header_line}")
            
            # Parse header for field count
            headers = [h.strip() for h in header_line.split('|')]
            validation_results['correct_column_count'] = len(headers)
            
            # Check data rows
            data_rows = lines[1:]
            print(f"\n📊 Analyzing {len(data_rows)} data rows:")
            
            for i, row in enumerate(data_rows, 1):
                print(f"\nRow {i}: {row}")
                
                # Check field count
                cells = [c.strip() for c in row.split('|')]
                print(f"  Fields: {len(cells)} (expected: 7)")
                
                if len(cells) != 7:
                    validation_results['all_rows_have_7_fields'] = False
                    validation_results['issues'].append(f"Row {i}: {len(cells)} fields instead of 7")
                    print(f"  ❌ Wrong field count: {len(cells)}")
                else:
                    print(f"  ✅ Correct field count: 7")
                
                # Check for pipes in content
                for j, cell in enumerate(cells):
                    if '|' in cell:
                        validation_results['no_pipes_in_content'] = False
                        validation_results['issues'].append(f"Row {i}, Field {j+1}: Contains pipe character")
                        print(f"  ❌ Field {j+1} contains pipe: {cell}")
                
                # Check for quotes in content
                for j, cell in enumerate(cells):
                    if '"' in cell:
                        validation_results['no_quotes_in_content'] = False
                        validation_results['issues'].append(f"Row {i}, Field {j+1}: Contains quote character")
                        print(f"  ❌ Field {j+1} contains quote: {cell}")
                
                # Check for 'x' usage in dimensions
                size_fields = [cells[2], cells[4]] if len(cells) >= 5 else []  # Size (Original) and Size (Converted)
                for j, size_field in enumerate(size_fields):
                    if 'COIL' in size_field and 'x' not in size_field:
                        validation_results['uses_x_for_dimensions'] = False
                        validation_results['issues'].append(f"Row {i}: Size field doesn't use 'x' for dimensions")
                        print(f"  ❌ Size field doesn't use 'x': {size_field}")
                
                # Check for complete rows (no empty critical fields)
                critical_fields = [0, 1, 2, 3, 4, 5]  # All except Remarks
                for field_idx in critical_fields:
                    if field_idx < len(cells) and not cells[field_idx].strip():
                        validation_results['complete_rows'] = False
                        validation_results['issues'].append(f"Row {i}: Empty critical field {field_idx+1}")
                        print(f"  ❌ Empty critical field {field_idx+1}")
        else:
            validation_results['issues'].append("No table lines found")
            print("❌ No table lines found")
    else:
        validation_results['issues'].append("Could not extract table content")
        print("❌ Could not extract table content")
    
    return validation_results

def generate_validation_report(validation_results):
    """Generate a comprehensive validation report"""
    
    print("\n" + "=" * 70)
    print("📋 VALIDATION REPORT")
    print("=" * 70)
    
    # Count passed rules
    rules_passed = sum([
        validation_results['has_table_stream_tags'],
        validation_results['correct_header_format'],
        validation_results['all_rows_have_7_fields'],
        validation_results['no_pipes_in_content'],
        validation_results['no_quotes_in_content'],
        validation_results['uses_x_for_dimensions'],
        validation_results['complete_rows']
    ])
    
    total_rules = 7
    
    print(f"📊 Rules Passed: {rules_passed}/{total_rules} ({(rules_passed/total_rules)*100:.1f}%)")
    print()
    
    # Individual rule status
    rules = [
        ("Table Stream Tags", validation_results['has_table_stream_tags']),
        ("Correct Header Format", validation_results['correct_header_format']),
        ("All Rows Have 7 Fields", validation_results['all_rows_have_7_fields']),
        ("No Pipes in Content", validation_results['no_pipes_in_content']),
        ("No Quotes in Content", validation_results['no_quotes_in_content']),
        ("Uses 'x' for Dimensions", validation_results['uses_x_for_dimensions']),
        ("Complete Rows", validation_results['complete_rows'])
    ]
    
    for rule_name, passed in rules:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {rule_name}")
    
    print()
    
    # Issues summary
    if validation_results['issues']:
        print("🔍 Issues Found:")
        for issue in validation_results['issues']:
            print(f"  • {issue}")
    else:
        print("🎉 No issues found!")
    
    print()
    
    # Overall result
    if rules_passed == total_rules:
        print("🎉 VALIDATION PASSED - Table format is perfect!")
        return True
    else:
        print("❌ VALIDATION FAILED - Table format needs improvement")
        return False

if __name__ == "__main__":
    print("🚀 Testing Enhanced Prompt Format")
    print("=" * 80)
    
    # Generate the expected response
    enhanced_response = simulate_enhanced_llm_response()
    
    print("📋 Simulated Enhanced LLM Response:")
    print("-" * 50)
    print(enhanced_response[:300] + "...")
    print("-" * 50)
    print()
    
    # Validate the format
    validation_results = validate_table_format(enhanced_response)
    
    # Generate report
    success = generate_validation_report(validation_results)
    
    print("\n" + "=" * 80)
    print("📋 TEST SUMMARY")
    print("=" * 80)
    
    if success:
        print("🎉 ENHANCED PROMPT TEST PASSED!")
        print("✅ The enhanced prompt should generate correct table format")
        print("✅ All validation rules are satisfied")
        print("✅ Ready for real LLM testing")
    else:
        print("❌ ENHANCED PROMPT TEST FAILED")
        print("❌ The simulated response has format issues")
        print("💡 Need to further refine the prompt")
    
    print("\n🚀 Next Steps:")
    print("1. Test with real LLM responses using the enhanced prompt")
    print("2. Monitor for any remaining parsing issues")
    print("3. Iterate on the prompt if needed")
    print("4. Verify perfect column alignment in the frontend")
