# Production Environment Configuration for <PERSON><PERSON> Deployment

# Environment
ENV=production
DEBUG=False

# Aliyun RDS Database
RDS_HOSTNAME=rm-uf6ky293vc3i3l991no.mysql.rds.aliyuncs.com
RDS_PORT=3306
RDS_DB_NAME=unit_converter
RDS_USERNAME=unit
RDS_PASSWORD=dnBW6x$^53$3Bxn
RDS_DATABASE_URL=mysql://unit:dnBW6x$^53$<EMAIL>:3306/unit_converter

# Database connection pool settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800
SQL_ECHO=False
RDS_MAX_CONNECTIONS=50

# JWT
JWT_SECRET_KEY=your-production-secret-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# Email settings - Aliyun Enterprise Mail (Qiye Mail)
SMTP_SERVER=smtp.qiye.aliyun.com
SMTP_PORT=25
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=STEELnet456456
SMTP_SENDER_NAME=SteelNet
SMTP_DOMAIN=steelnet.ai
SMTP_HOSTNAME=steelnet.ai
# Email rate limiting
EMAIL_RATE_LIMIT=10
EMAIL_RATE_LIMIT_PERIOD=3600

# CORS (comma-separated list of allowed origins)
CORS_ORIGINS=https://steelnet.ai,http://localhost:3000

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# 火山引擎 DeepSeek API
VOLCANO_ENGINE_API_KEY=9ed8bdbe-1fa4-4a97-b4ae-52843714fdca
VOLCANO_API_KEY=9ed8bdbe-1fa4-4a97-b4ae-52843714fdca
VOLCANO_ENGINE_ENDPOINT_ID=deepseek-v3-250324
VOLCANO_ENDPOINT_ID=deepseek-v3-250324
VOLCANO_ENGINE_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
VOLCANO_ENGINE_TIMEOUT=300
VOLCANO_ENGINE_PROMPT_TEMPLATE=llm_prompt.txt

# Memory Management
BACKEND_MEMORY_LIMIT=512m
FRONTEND_MEMORY_LIMIT=256m
WORKERS_PER_CORE=1.0
MAX_WORKERS=4
WEB_CONCURRENCY=2
WORKER_CONNECTIONS=1024

# Monitoring
LOG_LEVEL=WARNING
SENTRY_DSN=
