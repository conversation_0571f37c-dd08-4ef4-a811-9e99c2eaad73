#!/usr/bin/env bash

# Steel Unit Converter - Production Startup Script for Pre-built Frontend
# This script starts the application in production mode using pre-built frontend files

# Set script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
LOG_DIR="$SCRIPT_DIR/logs"
DIST_DIR="$FRONTEND_DIR/dist"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Set default values
ACTION="start"
COMPONENT="all"
SHOW_LOGS_AFTER=false
SETUP_URL_ACCESS=false
LOW_MEMORY_MODE=true  # Always enable low memory mode
MEMORY_PER_WORKER=150
WORKERS=1  # Force single worker
THREADS=4

# Set Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=256"

# Function to check if Python is installed
check_python() {
    echo "Checking for Python..."
    if command -v python3 &> /dev/null; then
        echo "Found Python: $(python3 --version)"
        # Create alias for python -> python3 if needed
        if ! command -v python &> /dev/null; then
            echo "Creating alias for python -> python3"
            alias python=python3
        fi
        return 0
    elif command -v python &> /dev/null; then
        echo "Found Python: $(python --version)"
        return 0
    else
        echo "Python not found. Please install Python 3.8 or higher."
        exit 1
    fi
}

# Function to check if Node.js is installed
check_node() {
    echo "Checking for Node.js..."
    if command -v node &> /dev/null; then
        echo "Found Node.js: $(node --version)"
        return 0
    else
        echo "Node.js not found. Please install Node.js 14 or higher."
        exit 1
    fi
}

# Function to kill existing processes
kill_existing() {
    echo "Checking for existing processes..."
    
    # Kill processes on ports 8000 and 3000
    if command -v lsof &> /dev/null; then
        echo "Killing process on port 8000..."
        lsof -ti:8000 | xargs kill -9 2>/dev/null || true
        
        echo "Killing process on port 3000..."
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    else
        # Alternative method if lsof is not available
        echo "lsof not found, using alternative method to kill processes..."
        
        # Find and kill Python/Gunicorn processes
        pkill -f "gunicorn main:app" 2>/dev/null || true
        pkill -f "uvicorn main:app" 2>/dev/null || true
        
        # Find and kill Node.js processes for frontend
        pkill -f "node.*serve -s dist" 2>/dev/null || true
        pkill -f "node.*vite preview" 2>/dev/null || true
    fi
    
    echo "All existing processes killed."
}

# Function to start the backend
start_backend() {
    echo "Starting backend in production mode..."
    
    # Change directory to backend
    cd "$BACKEND_DIR" || exit
    
    # Create and activate virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo "Creating virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies
    echo "Installing backend dependencies..."
    pip install -r requirements.txt
    pip install gunicorn
    pip install uvicorn
    
    # Set environment variables
    export ENV="production"
    export DEBUG="False"
    
    # Fix import issues
    echo "Fixing import issues..."
    python fix_imports.py
    
    # Check for missing dependencies
    echo "Checking for missing dependencies..."
    python install_missing_deps.py
    
    # Start backend with Gunicorn (single worker, 4 threads)
    echo "Starting backend with Gunicorn (1 worker, 4 threads)..."
    nohup gunicorn main:app \
        --worker-class uvicorn.workers.UvicornWorker \
        --workers 1 \
        --threads 4 \
        --bind 0.0.0.0:8000 \
        --max-requests 500 \
        --max-requests-jitter 50 \
        --timeout 120 \
        --keep-alive 5 \
        --log-level warning \
        --worker-tmp-dir /tmp \
        --preload \
        > "$LOG_DIR/backend.log" 2>&1 &
    
    BACKEND_PID=$!
    echo "Backend started with PID: $BACKEND_PID"
    echo "Backend logs available at: $LOG_DIR/backend.log"
    
    # Save PID to file for later reference
    echo $BACKEND_PID > "$LOG_DIR/backend.pid"
    
    # Return to script directory
    cd "$SCRIPT_DIR" || exit
}

# Function to start the frontend using pre-built dist
start_frontend() {
    echo "Starting frontend in production mode (using pre-built dist)..."
    
    # Change directory to frontend
    cd "$FRONTEND_DIR" || exit
    
    # Check if dist directory exists
    if [ ! -d "$DIST_DIR" ] || [ ! -f "$DIST_DIR/index.html" ]; then
        echo "Error: Pre-built dist directory not found or incomplete."
        echo "Please build the frontend on a development machine and copy the dist directory to $DIST_DIR"
        exit 1
    else
        echo "Using existing pre-built dist directory."
    fi
    
    # Install serve locally if not already installed
    if ! npm list | grep -q serve; then
        echo "Installing serve locally..."
        npm install --no-save serve
    fi
    
    # Use npx to run serve from local node_modules
    echo "Starting frontend server with serve..."
    nohup npx serve -s dist -l tcp://0.0.0.0:3000 --no-clipboard --single > "$LOG_DIR/frontend.log" 2>&1 &
    
    FRONTEND_PID=$!
    echo "Frontend server started with PID: $FRONTEND_PID"
    echo "Frontend logs available at: $LOG_DIR/frontend.log"
    
    # Save PID to file for later reference
    echo $FRONTEND_PID > "$LOG_DIR/frontend.pid"
    
    # Return to script directory
    cd "$SCRIPT_DIR" || exit
}

# Function to set up nginx
setup_nginx() {
    echo "Setting up nginx..."
    
    # Check if nginx is installed
    if ! command -v nginx &> /dev/null; then
        echo "nginx is not installed. Skipping nginx setup."
        return 1
    fi
    
    # Create SSL directory if it doesn't exist
    SSL_DIR="$SCRIPT_DIR/ssl"
    mkdir -p "$SSL_DIR"
    
    # Generate self-signed SSL certificates if they don't exist
    if [ ! -f "$SSL_DIR/cert.pem" ] || [ ! -f "$SSL_DIR/key.pem" ]; then
        echo "Generating self-signed SSL certificates..."
        openssl req -x509 -newkey rsa:2048 -keyout "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.pem" -days 365 -nodes -subj "/CN=localhost" -addext "subjectAltName=DNS:localhost,IP:127.0.0.1"
    fi
    
    # Determine nginx configuration directory
    NGINX_CONF_DIR=""
    if [ -d "/etc/nginx/conf.d" ]; then
        NGINX_CONF_DIR="/etc/nginx/conf.d"
    elif [ -d "/usr/local/etc/nginx/conf.d" ]; then
        NGINX_CONF_DIR="/usr/local/etc/nginx/conf.d"
    elif [ -d "/usr/local/etc/nginx/servers" ]; then
        NGINX_CONF_DIR="/usr/local/etc/nginx/servers"
    else
        echo "Could not find nginx configuration directory. Skipping nginx setup."
        return 1
    fi
    
    # Get public IP address
    PUBLIC_IP=$(curl -s ifconfig.me || echo "localhost")
    
    # Create nginx configuration file
    echo "Creating nginx configuration file..."
    cat > /tmp/steelnet.conf << EOF
# Steel Unit Converter nginx Configuration

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name localhost $PUBLIC_IP;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://\$host\$request_uri;
}

server {
    listen 443 ssl;
    server_name localhost $PUBLIC_IP;
    
    # SSL configuration
    ssl_certificate $SSL_DIR/cert.pem;
    ssl_certificate_key $SSL_DIR/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    
    # SSL optimizations for low memory
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 5m;
    
    # Root directory for static files
    root $DIST_DIR;
    index index.html;
    
    # Serve favicon.ico directly (prevent duplicate handling)
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires max;
    }
    
    # Serve static files directly
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        access_log off;
        expires max;
        add_header Cache-Control "public, no-transform";
    }
    
    # Frontend application - important for SPA routing
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
    
    # Copy configuration file to nginx directory
    if command -v sudo &> /dev/null; then
        sudo cp /tmp/steelnet.conf "$NGINX_CONF_DIR/steelnet.conf"
    else
        cp /tmp/steelnet.conf "$NGINX_CONF_DIR/steelnet.conf"
    fi
    
    # Test and reload nginx
    echo "Testing nginx configuration..."
    if command -v sudo &> /dev/null; then
        sudo nginx -t
        if [ $? -eq 0 ]; then
            echo "nginx configuration is valid. Reloading nginx..."
            if command -v systemctl &> /dev/null; then
                sudo systemctl reload nginx
            else
                sudo nginx -s reload
            fi
        else
            echo "nginx configuration is invalid."
            return 1
        fi
    else
        nginx -t
        if [ $? -eq 0 ]; then
            echo "nginx configuration is valid. Reloading nginx..."
            nginx -s reload
        else
            echo "nginx configuration is invalid."
            return 1
        fi
    fi
    
    echo "nginx setup completed successfully."
    echo "You can now access the application at:"
    echo "  http://$PUBLIC_IP"
    echo "  https://$PUBLIC_IP"
    
    return 0
}

# Function to stop all services
stop_services() {
    kill_existing
    echo "All services stopped."
}

# Function to check service status
check_status() {
    echo "Checking service status..."
    
    # Check if backend is running
    if command -v lsof &> /dev/null; then
        echo "Backend (port 8000):"
        lsof -i:8000 || echo "Backend is not running."
        
        echo "Frontend (port 3000):"
        lsof -i:3000 || echo "Frontend is not running."
    else
        echo "lsof not found, cannot check port status."
    fi
}

# Function to show logs
show_logs() {
    if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "backend" ]; then
        echo "Backend logs:"
        tail -n 50 "$LOG_DIR/backend.log"
    fi
    
    if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "frontend" ]; then
        echo "Frontend logs:"
        tail -n 50 "$LOG_DIR/frontend.log"
    fi
}

# Parse command line arguments
if [ $# -gt 0 ]; then
    case "$1" in
        start)
            ACTION="start"
            shift
            ;;
        stop)
            ACTION="stop"
            shift
            ;;
        status)
            ACTION="status"
            shift
            ;;
        logs)
            ACTION="logs"
            shift
            ;;
        --url-access)
            SETUP_URL_ACCESS=true
            shift
            ;;
        *)
            echo "Unknown action: $1"
            echo "Usage: $0 [start|stop|status|logs|--url-access]"
            exit 1
            ;;
    esac
fi

# Execute action
case "$ACTION" in
    start)
        # Check requirements
        check_python
        check_node
        
        # Kill existing processes
        kill_existing
        
        # Start services
        start_backend
        start_frontend
        
        # Setup nginx if requested
        if [ "$SETUP_URL_ACCESS" = true ]; then
            setup_nginx
        fi
        
        echo "Services started successfully."
        
        # Show summary
        echo ""
        echo "=================================================="
        echo "  Steel Unit Converter - Production Mode  "
        echo "=================================================="
        echo ""
        echo "Application URLs (direct access):"
        echo "  Backend: http://localhost:8000"
        echo "  Frontend: http://localhost:3000"
        echo "  API: http://localhost:8000/api"
        echo ""
        echo "Low memory mode is ENABLED"
        echo ""
        echo "Management commands:"
        echo "  Stop:             ./start-built.sh stop"
        echo "  Status:           ./start-built.sh status"
        echo "  View Logs:        ./start-built.sh logs"
        echo "  Setup URL Access: ./start-built.sh --url-access"
        ;;
    stop)
        stop_services
        ;;
    status)
        check_status
        ;;
    logs)
        show_logs
        ;;
esac

# Make script executable
chmod +x "$0"
