#!/bin/bash

# Script to check and fix nginx configuration for the Steel Unit Converter application
# This script checks if nginx is properly configured and fixes common issues

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
LOG_DIR="$SCRIPT_DIR/logs"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Public IP address
PUBLIC_IP="************"

# Function to print section header
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Function to print success message
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print warning message
print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Function to print error message
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to check if nginx is installed
check_nginx_installed() {
    print_header "Checking if nginx is installed"
    
    if command -v nginx &> /dev/null; then
        print_success "nginx is installed"
        return 0
    else
        print_error "nginx is not installed"
        
        # Ask if user wants to install nginx
        echo -e "\nWould you like to install nginx? (y/n)"
        read -r INSTALL_NGINX
        
        if [[ "$INSTALL_NGINX" =~ ^[Yy]$ ]]; then
            echo "Installing nginx..."
            if command -v apt-get &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y nginx
            elif command -v yum &> /dev/null; then
                sudo yum install -y nginx
            else
                print_error "Could not install nginx. Please install it manually."
                return 1
            fi
            
            # Check if installation was successful
            if command -v nginx &> /dev/null; then
                print_success "nginx installed successfully"
                return 0
            else
                print_error "nginx installation failed"
                return 1
            fi
        else
            return 1
        fi
    fi
}

# Function to check if nginx is running
check_nginx_running() {
    print_header "Checking if nginx is running"
    
    if systemctl is-active --quiet nginx || service nginx status > /dev/null 2>&1; then
        print_success "nginx is running"
        return 0
    else
        print_error "nginx is not running"
        
        # Ask if user wants to start nginx
        echo -e "\nWould you like to start nginx? (y/n)"
        read -r START_NGINX
        
        if [[ "$START_NGINX" =~ ^[Yy]$ ]]; then
            echo "Starting nginx..."
            sudo systemctl start nginx || sudo service nginx start
            
            # Check if nginx started successfully
            if systemctl is-active --quiet nginx || service nginx status > /dev/null 2>&1; then
                print_success "nginx started successfully"
                return 0
            else
                print_error "Failed to start nginx"
                return 1
            fi
        else
            return 1
        fi
    fi
}

# Function to check nginx configuration
check_nginx_config() {
    print_header "Checking nginx configuration"
    
    # Check if nginx configuration is valid
    echo "Testing nginx configuration..."
    if sudo nginx -t; then
        print_success "nginx configuration is valid"
    else
        print_error "nginx configuration is invalid"
        
        # Ask if user wants to create a new configuration
        echo -e "\nWould you like to create a new nginx configuration? (y/n)"
        read -r CREATE_CONFIG
        
        if [[ "$CREATE_CONFIG" =~ ^[Yy]$ ]]; then
            create_nginx_config
        else
            return 1
        fi
    fi
    
    # Check if steelnet.conf exists
    if [ -f "/etc/nginx/conf.d/steelnet.conf" ]; then
        print_success "steelnet.conf exists"
        
        # Check if the configuration includes the public IP
        if grep -q "$PUBLIC_IP" "/etc/nginx/conf.d/steelnet.conf"; then
            print_success "Public IP is configured in nginx"
        else
            print_warning "Public IP is not configured in nginx"
            
            # Ask if user wants to update the configuration
            echo -e "\nWould you like to update the nginx configuration with the public IP? (y/n)"
            read -r UPDATE_CONFIG
            
            if [[ "$UPDATE_CONFIG" =~ ^[Yy]$ ]]; then
                update_nginx_config
            fi
        fi
    else
        print_error "steelnet.conf does not exist"
        
        # Ask if user wants to create a new configuration
        echo -e "\nWould you like to create a new nginx configuration? (y/n)"
        read -r CREATE_CONFIG
        
        if [[ "$CREATE_CONFIG" =~ ^[Yy]$ ]]; then
            create_nginx_config
        else
            return 1
        fi
    fi
    
    return 0
}

# Function to create nginx configuration
create_nginx_config() {
    print_header "Creating nginx configuration"
    
    # Create SSL directory if it doesn't exist
    if [ ! -d "$SCRIPT_DIR/ssl" ]; then
        echo "Creating SSL directory..."
        mkdir -p "$SCRIPT_DIR/ssl"
    fi
    
    # Generate self-signed certificates if they don't exist
    if [ ! -f "$SCRIPT_DIR/ssl/cert.pem" ] || [ ! -f "$SCRIPT_DIR/ssl/key.pem" ]; then
        echo "Generating self-signed SSL certificates..."
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$SCRIPT_DIR/ssl/key.pem" -out "$SCRIPT_DIR/ssl/cert.pem" \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=steelnet.ai"
    fi
    
    # Create nginx configuration
    echo "Creating nginx configuration..."
    cat > /tmp/steelnet.conf << EOF
# Global settings for low memory usage
worker_processes 1;
worker_rlimit_nofile 1024;
events {
    worker_connections 512;
    multi_accept off;
}
http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Buffer size settings (reduced for low memory)
    client_body_buffer_size 8k;
    client_header_buffer_size 1k;
    client_max_body_size 1m;
    large_client_header_buffers 2 1k;
    
    # Gzip settings (save CPU)
    gzip on;
    gzip_min_length 1000;
    gzip_comp_level 2;
    gzip_types text/plain text/css application/javascript application/json;
    
    # HTTP server - redirect to HTTPS
    server {
        listen 80 default_server;
        listen [::]:80 default_server;
        server_name _ $PUBLIC_IP steelnet.ai;
        
        # Redirect all HTTP requests to HTTPS
        return 301 https://\$host\$request_uri;
    }
    
    # HTTPS server
    server {
        listen 443 ssl default_server;
        listen [::]:443 ssl default_server;
        server_name _ $PUBLIC_IP steelnet.ai;
        
        # SSL configuration
        ssl_certificate $SCRIPT_DIR/ssl/cert.pem;
        ssl_certificate_key $SCRIPT_DIR/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
        
        # SSL optimizations
        ssl_session_cache shared:SSL:2m;
        ssl_session_timeout 10m;
        
        # Frontend proxy
        location / {
            proxy_pass http://localhost:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade \$http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host \$host;
            proxy_cache_bypass \$http_upgrade;
            
            # Add caching for static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                proxy_pass http://localhost:3000;
                expires 30d;
                add_header Cache-Control "public, no-transform";
            }
        }
        
        # Backend API proxy
        location /api {
            proxy_pass http://localhost:8000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade \$http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host \$host;
            proxy_cache_bypass \$http_upgrade;
            
            # Timeout settings
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }
}
EOF
    
    # Copy nginx configuration to nginx directory
    echo "Copying nginx configuration to /etc/nginx/conf.d/steelnet.conf..."
    sudo cp /tmp/steelnet.conf /etc/nginx/conf.d/steelnet.conf
    
    # Test nginx configuration
    echo "Testing nginx configuration..."
    if sudo nginx -t; then
        print_success "nginx configuration is valid"
        
        # Reload nginx
        echo "Reloading nginx..."
        sudo systemctl reload nginx || sudo service nginx reload
        
        print_success "nginx configuration created and loaded successfully"
        return 0
    else
        print_error "nginx configuration is invalid"
        return 1
    fi
}

# Function to update nginx configuration
update_nginx_config() {
    print_header "Updating nginx configuration"
    
    # Backup existing configuration
    echo "Backing up existing configuration..."
    sudo cp /etc/nginx/conf.d/steelnet.conf /etc/nginx/conf.d/steelnet.conf.bak
    
    # Update server_name directive
    echo "Updating server_name directive..."
    sudo sed -i "s/server_name .*;/server_name _ $PUBLIC_IP steelnet.ai;/g" /etc/nginx/conf.d/steelnet.conf
    
    # Test nginx configuration
    echo "Testing nginx configuration..."
    if sudo nginx -t; then
        print_success "nginx configuration is valid"
        
        # Reload nginx
        echo "Reloading nginx..."
        sudo systemctl reload nginx || sudo service nginx reload
        
        print_success "nginx configuration updated successfully"
        return 0
    else
        print_error "nginx configuration is invalid"
        
        # Restore backup
        echo "Restoring backup..."
        sudo cp /etc/nginx/conf.d/steelnet.conf.bak /etc/nginx/conf.d/steelnet.conf
        
        return 1
    fi
}

# Function to check if ports are open
check_ports() {
    print_header "Checking if ports are open"
    
    # Check if port 80 is open
    if sudo netstat -tulpn | grep -q ":80 "; then
        print_success "Port 80 is open"
    else
        print_error "Port 80 is not open"
    fi
    
    # Check if port 443 is open
    if sudo netstat -tulpn | grep -q ":443 "; then
        print_success "Port 443 is open"
    else
        print_error "Port 443 is not open"
    fi
    
    # Check if port 3000 is open
    if sudo netstat -tulpn | grep -q ":3000 "; then
        print_success "Port 3000 is open"
    else
        print_error "Port 3000 is not open"
        
        # Ask if user wants to start the frontend
        echo -e "\nWould you like to start the frontend? (y/n)"
        read -r START_FRONTEND
        
        if [[ "$START_FRONTEND" =~ ^[Yy]$ ]]; then
            echo "Starting frontend..."
            ./start-prod.sh --frontend
        fi
    fi
    
    # Check if port 8000 is open
    if sudo netstat -tulpn | grep -q ":8000 "; then
        print_success "Port 8000 is open"
    else
        print_error "Port 8000 is not open"
        
        # Ask if user wants to start the backend
        echo -e "\nWould you like to start the backend? (y/n)"
        read -r START_BACKEND
        
        if [[ "$START_BACKEND" =~ ^[Yy]$ ]]; then
            echo "Starting backend..."
            ./start-prod.sh --backend
        fi
    fi
}

# Function to check nginx logs
check_nginx_logs() {
    print_header "Checking nginx logs"
    
    # Check if nginx error log exists
    if [ -f "/var/log/nginx/error.log" ]; then
        echo "Last 10 lines of nginx error log:"
        sudo tail -n 10 /var/log/nginx/error.log
    else
        print_warning "nginx error log does not exist"
    fi
    
    # Check if nginx access log exists
    if [ -f "/var/log/nginx/access.log" ]; then
        echo -e "\nLast 10 lines of nginx access log:"
        sudo tail -n 10 /var/log/nginx/access.log
    else
        print_warning "nginx access log does not exist"
    fi
}

# Function to restart nginx
restart_nginx() {
    print_header "Restarting nginx"
    
    echo "Restarting nginx..."
    sudo systemctl restart nginx || sudo service nginx restart
    
    # Check if nginx restarted successfully
    if systemctl is-active --quiet nginx || service nginx status > /dev/null 2>&1; then
        print_success "nginx restarted successfully"
        return 0
    else
        print_error "Failed to restart nginx"
        return 1
    fi
}

# Main function
main() {
    print_header "nginx Configuration Check and Fix"
    echo "This script will check and fix nginx configuration for the Steel Unit Converter application"
    echo "Public IP: $PUBLIC_IP"
    echo "Script directory: $SCRIPT_DIR"
    
    # Check if running as root
    if [ "$EUID" -eq 0 ]; then
        print_warning "Running as root. Some checks may not work correctly."
    fi
    
    # Check if nginx is installed
    if ! check_nginx_installed; then
        print_error "nginx is not installed. Please install it and run this script again."
        exit 1
    fi
    
    # Check if nginx is running
    if ! check_nginx_running; then
        print_error "nginx is not running. Please start it and run this script again."
        exit 1
    fi
    
    # Check nginx configuration
    check_nginx_config
    
    # Check if ports are open
    check_ports
    
    # Check nginx logs
    check_nginx_logs
    
    # Ask if user wants to restart nginx
    echo -e "\nWould you like to restart nginx? (y/n)"
    read -r RESTART_NGINX
    
    if [[ "$RESTART_NGINX" =~ ^[Yy]$ ]]; then
        restart_nginx
    fi
    
    print_header "Summary"
    echo "nginx configuration check and fix complete."
    echo "If you're still having issues, please check the logs:"
    echo "  nginx error log: /var/log/nginx/error.log"
    echo "  nginx access log: /var/log/nginx/access.log"
    echo "  Backend logs: $LOG_DIR/backend.log"
    echo "  Frontend logs: $LOG_DIR/frontend.log"
    
    echo -e "\nYou can also try restarting the application with:"
    echo "  ./start-prod.sh --restart --url-access"
}

# Run the main function
main
