const fetch = require('node-fetch');

async function testAPIResponse() {
    try {
        console.log('Testing API response format...');
        
        const response = await fetch('http://localhost:8000/api/llm/stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                text: 'Convert 0.015 inch x 2.343 inch to mm',
                function: 'table',
                unit_system: 'metric'
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        console.log('Response received, reading stream...');
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let fullResponse = '';
        
        while (true) {
            const { done, value } = await reader.read();
            
            if (done) break;
            
            const chunk = decoder.decode(value, { stream: true });
            fullResponse += chunk;
            
            // Log each chunk to see the streaming format
            console.log('Chunk:', JSON.stringify(chunk));
        }
        
        console.log('\n=== FULL RESPONSE ===');
        console.log(fullResponse);
        
        // Check what format the response is in
        if (fullResponse.includes('<table_stream>')) {
            console.log('\n✓ Response contains <table_stream> format');
        } else if (fullResponse.includes('<table_data>')) {
            console.log('\n✓ Response contains <table_data> format');
        } else if (fullResponse.includes('|') && fullResponse.includes('\n|')) {
            console.log('\n✓ Response contains markdown table format');
        } else {
            console.log('\n✗ No table format detected');
        }
        
    } catch (error) {
        console.error('Error testing API:', error);
    }
}

testAPIResponse();
