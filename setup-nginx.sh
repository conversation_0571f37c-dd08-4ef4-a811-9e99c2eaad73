#!/usr/bin/env bash

# Setup Nginx for Steel Unit Converter
# This script installs and configures Nginx for production use

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
LOG_DIR="$SCRIPT_DIR/logs"
SSL_DIR="$SCRIPT_DIR/ssl"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"
mkdir -p "$SSL_DIR"

# Function to check if a command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Function to install Nginx
install_nginx() {
    echo "Installing Nginx..."
    
    if command_exists apt-get; then
        sudo apt-get update && sudo apt-get install -y nginx
        return $?
    elif command_exists yum; then
        sudo yum install -y nginx
        return $?
    elif command_exists brew; then
        brew install nginx
        return $?
    else
        echo "Could not find a package manager to install Nginx."
        return 1
    fi
}

# Function to generate self-signed SSL certificates
generate_ssl_certs() {
    echo "Generating self-signed SSL certificates..."
    
    if [ ! -f "$SSL_DIR/cert.pem" ] || [ ! -f "$SSL_DIR/key.pem" ]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.pem" \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=steelnet.ai"
    else
        echo "SSL certificates already exist."
    fi
}

# Function to create Nginx configuration
create_nginx_config() {
    echo "Creating Nginx configuration..."
    
    # Determine Nginx configuration directory
    NGINX_CONF_DIR=""
    if [ -d "/etc/nginx/conf.d" ]; then
        NGINX_CONF_DIR="/etc/nginx/conf.d"
    elif [ -d "/usr/local/etc/nginx/conf.d" ]; then
        NGINX_CONF_DIR="/usr/local/etc/nginx/conf.d"
    elif [ -d "/usr/local/etc/nginx/servers" ]; then
        NGINX_CONF_DIR="/usr/local/etc/nginx/servers"
    else
        echo "Could not find Nginx configuration directory."
        return 1
    fi
    
    echo "Using Nginx configuration directory: $NGINX_CONF_DIR"
    
    # Create Nginx configuration file
    cat > /tmp/steelnet.conf << EOF
server {
    listen 80;
    server_name localhost;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://\$host\$request_uri;
}

server {
    listen 443 ssl;
    server_name localhost;
    
    # SSL configuration
    ssl_certificate $SSL_DIR/cert.pem;
    ssl_certificate_key $SSL_DIR/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    
    # SSL optimizations
    ssl_session_cache shared:SSL:2m;
    ssl_session_timeout 10m;
    
    # Frontend proxy
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        
        # Add caching for static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://localhost:3000;
            expires 30d;
            add_header Cache-Control "public, no-transform";
        }
    }
    
    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
    
    # Copy configuration file to Nginx directory
    if command_exists sudo; then
        sudo cp /tmp/steelnet.conf "$NGINX_CONF_DIR/steelnet.conf"
    else
        cp /tmp/steelnet.conf "$NGINX_CONF_DIR/steelnet.conf"
    fi
    
    return $?
}

# Function to test and reload Nginx
test_and_reload_nginx() {
    echo "Testing Nginx configuration..."
    
    if command_exists sudo; then
        sudo nginx -t
        if [ $? -eq 0 ]; then
            echo "Nginx configuration is valid. Reloading Nginx..."
            if command_exists systemctl; then
                sudo systemctl reload nginx
            else
                sudo nginx -s reload
            fi
            return $?
        else
            echo "Nginx configuration is invalid."
            return 1
        fi
    else
        nginx -t
        if [ $? -eq 0 ]; then
            echo "Nginx configuration is valid. Reloading Nginx..."
            nginx -s reload
            return $?
        else
            echo "Nginx configuration is invalid."
            return 1
        fi
    fi
}

# Main function
main() {
    echo "Setting up Nginx for Steel Unit Converter..."
    
    # Check if Nginx is installed
    if ! command_exists nginx; then
        echo "Nginx is not installed."
        read -p "Would you like to install Nginx? (y/n) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            install_nginx
            if [ $? -ne 0 ]; then
                echo "Failed to install Nginx."
                exit 1
            fi
        else
            echo "Nginx installation skipped."
            exit 0
        fi
    else
        echo "Nginx is already installed."
    fi
    
    # Generate SSL certificates
    generate_ssl_certs
    
    # Create Nginx configuration
    create_nginx_config
    if [ $? -ne 0 ]; then
        echo "Failed to create Nginx configuration."
        exit 1
    fi
    
    # Test and reload Nginx
    test_and_reload_nginx
    if [ $? -ne 0 ]; then
        echo "Failed to reload Nginx."
        exit 1
    fi
    
    echo "Nginx setup completed successfully."
    echo "The application should be accessible at:"
    echo "  https://localhost"
    echo "  http://localhost (redirects to HTTPS)"
}

# Run main function
main
