#!/usr/bin/env python3
"""
Final test to verify that our table parsing fixes work correctly
"""

import requests
import json
import time
import os

def test_complete_flow():
    """Test the complete flow from backend to frontend parsing"""
    
    # Test data that was causing issues
    test_input = """
    Please convert the following steel specifications from imperial to metric and create a table:

    001 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.343(+/-0.005)" x COIL - 7190#
    002 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.406(+/-0.005)" x COIL - 8061#
    003 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 16.50(+/-0.005)" x COIL - 12550#
    004 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 19.68(+/-0.03125)" x COIL - 8835#
    005 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 47.438(+/-0.03125)" x COIL - 57655#
    006 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 47.000(+/-0.03125)" x COIL - 118001#
    009 - S/S 430 #2B NO PI - 0.016(+/-0.0015)" x 19.69(+/-0.005)" x COIL - 725321#
    010 - S/S 430 #2B NO PI - 0.016(+/-0.0015)" x 35.500(+/-0.03125)" x COIL - 122001#
    011 - S/S 430 #2B NO PI - 0.016(+/-0.0015)" x 36.000(+/-0.03125)" x COIL - 234265#
    012 - S/S 430 #2B NO PI - 0.016(+/-0.0015)" x 48.000(+/-0.03125)" x COIL - 201001#
    013 - S/S 430 #2B NO PI - 0.020(+/-0.0015)" x 36.000(+/-0.03125)" x COIL - 338410#
    """
    
    print("🧪 Final Fix Test - Complete Flow")
    print("=" * 60)
    print()
    
    # Step 1: Test non-streaming endpoint
    print("📋 Step 1: Testing Non-Streaming Endpoint")
    print("-" * 40)
    
    url = "http://localhost:8000/api/llm"
    payload = {
        "text": test_input,
        "unit_system": "metric",
        "function": "table"
    }
    
    try:
        # Clear proxy environment variables
        old_http_proxy = os.environ.get('http_proxy')
        old_https_proxy = os.environ.get('https_proxy')
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']
        
        session = requests.Session()
        session.trust_env = False
        
        print("📡 Sending request to non-streaming endpoint...")
        response = session.post(url, json=payload, timeout=60)
        
        # Restore proxy settings
        if old_http_proxy:
            os.environ['http_proxy'] = old_http_proxy
        if old_https_proxy:
            os.environ['https_proxy'] = old_https_proxy
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Non-streaming request successful")
            
            if 'result' in result and 'converted_text' in result['result']:
                table_content = result['result']['converted_text']
                print(f"📊 Table content length: {len(table_content)}")
                
                # Check for table_stream format
                if '<table_stream>' in table_content and '</table_stream>' in table_content:
                    print("✅ Table stream format detected")
                    
                    # Extract table data
                    start = table_content.find('<table_stream>') + len('<table_stream>')
                    end = table_content.find('</table_stream>')
                    table_data = table_content[start:end].strip()
                    
                    # Analyze table structure
                    lines = table_data.split('\n')
                    if lines:
                        header_line = lines[0]
                        headers = header_line.split('|')
                        print(f"📋 Headers ({len(headers)}): {headers}")
                        
                        data_integrity_ok = True
                        for i, line in enumerate(lines[1:], 1):
                            if line.strip():
                                cells = line.split('|')
                                if len(cells) != len(headers):
                                    print(f"❌ Row {i} column mismatch: expected {len(headers)}, got {len(cells)}")
                                    data_integrity_ok = False
                                else:
                                    print(f"✅ Row {i} column count matches ({len(cells)} cells)")
                        
                        if data_integrity_ok:
                            print("🎉 NON-STREAMING: All rows have correct column count!")
                        else:
                            print("❌ NON-STREAMING: Data integrity issues found")
                            
                        print()
                        print("📄 Sample table data:")
                        print("-" * 30)
                        for i, line in enumerate(lines[:3]):  # Show first 3 lines
                            print(f"Line {i}: {line}")
                        print("-" * 30)
                        
                else:
                    print("❌ No table_stream format found")
            else:
                print("❌ No converted_text found in response")
        else:
            print(f"❌ Non-streaming request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Non-streaming test failed: {e}")
    
    print()
    
    # Step 2: Test streaming endpoint
    print("📋 Step 2: Testing Streaming Endpoint")
    print("-" * 40)
    
    streaming_url = "http://localhost:8000/api/llm/stream"
    
    try:
        # Clear proxy environment variables
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']
        
        session = requests.Session()
        session.trust_env = False
        
        print("📡 Sending request to streaming endpoint...")
        response = session.post(streaming_url, json=payload, stream=True, timeout=60)
        
        if response.status_code == 200:
            print("✅ Streaming request successful")
            
            accumulated_content = ""
            chunk_count = 0
            
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith('data: '):
                    data_part = line[6:]
                    
                    if data_part.strip() == '[DONE]':
                        print("🏁 Stream completed")
                        break
                    
                    try:
                        chunk_data = json.loads(data_part)
                        chunk_count += 1
                        
                        if 'accumulated_content' in chunk_data:
                            accumulated_content = chunk_data['accumulated_content']
                            
                            # Check if we have complete table
                            if '<table_stream>' in accumulated_content and '</table_stream>' in accumulated_content:
                                print(f"✅ Complete table found in chunk {chunk_count}")
                                
                                # Extract and analyze table
                                start = accumulated_content.find('<table_stream>') + len('<table_stream>')
                                end = accumulated_content.find('</table_stream>')
                                table_data = accumulated_content[start:end].strip()
                                
                                lines = table_data.split('\n')
                                if lines:
                                    header_line = lines[0]
                                    headers = header_line.split('|')
                                    print(f"📋 Headers ({len(headers)}): {headers}")
                                    
                                    streaming_integrity_ok = True
                                    for i, line in enumerate(lines[1:], 1):
                                        if line.strip():
                                            cells = line.split('|')
                                            if len(cells) != len(headers):
                                                print(f"❌ Row {i} column mismatch: expected {len(headers)}, got {len(cells)}")
                                                streaming_integrity_ok = False
                                            else:
                                                print(f"✅ Row {i} column count matches ({len(cells)} cells)")
                                    
                                    if streaming_integrity_ok:
                                        print("🎉 STREAMING: All rows have correct column count!")
                                        
                                        # Generate HTML table to verify final output
                                        print()
                                        print("🔧 Generating HTML table...")
                                        html_table = generate_html_table(headers, lines[1:])
                                        
                                        # Save to file for inspection
                                        with open('test_final_output.html', 'w') as f:
                                            f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <title>Final Test Output</title>
    <style>
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ccc; padding: 8px; text-align: left; }}
        th {{ background-color: #f0f0f0; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
    </style>
</head>
<body>
    <h1>Final Test Output - Fixed Table Parsing</h1>
    <p>Generated at: {time.strftime('%Y-%m-%d %H:%M:%S')}</p>
    {html_table}
</body>
</html>
                                            """)
                                        
                                        print("✅ HTML table saved to test_final_output.html")
                                        
                                    else:
                                        print("❌ STREAMING: Data integrity issues found")
                                
                                break  # We found the complete table
                                
                    except json.JSONDecodeError:
                        pass  # Skip invalid JSON chunks
            
            print(f"📊 Total chunks processed: {chunk_count}")
            
        else:
            print(f"❌ Streaming request failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Streaming test failed: {e}")
    
    print()
    print("🏁 Test Complete!")

def generate_html_table(headers, data_lines):
    """Generate HTML table from headers and data lines"""
    html = '<table>'
    
    # Headers
    html += '<thead><tr>'
    for header in headers:
        html += f'<th>{header.strip()}</th>'
    html += '</tr></thead>'
    
    # Data rows
    html += '<tbody>'
    for line in data_lines:
        if line.strip():
            cells = line.split('|')
            html += '<tr>'
            for cell in cells:
                html += f'<td>{cell.strip()}</td>'
            html += '</tr>'
    html += '</tbody>'
    
    html += '</table>'
    return html

if __name__ == "__main__":
    test_complete_flow()
