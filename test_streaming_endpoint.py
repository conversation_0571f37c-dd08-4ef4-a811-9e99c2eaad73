#!/usr/bin/env python3
"""
Test script to check the streaming endpoint and see what data it's actually sending
"""

import requests
import json
import time
import os

def test_streaming_endpoint():
    """Test the streaming endpoint to see the actual data format"""
    
    # Test data
    test_input = """
    Please convert the following steel specifications from imperial to metric and create a table:

    001 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.343(+/-0.005)" x COIL - 7190#
    002 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.406(+/-0.005)" x COIL - 8061#
    003 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 16.50(+/-0.005)" x COIL - 12550#
    """
    
    # API endpoint
    url = "http://localhost:8000/api/llm/stream"
    
    # Request payload
    payload = {
        "text": test_input,
        "unit_system": "metric",
        "function": "table"
    }
    
    print("🧪 Testing Streaming Endpoint")
    print("=" * 50)
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print()
    
    try:
        # Clear proxy environment variables for this session
        old_http_proxy = os.environ.get('http_proxy')
        old_https_proxy = os.environ.get('https_proxy')
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']
        
        # Create a new session without proxy
        session = requests.Session()
        session.trust_env = False  # Don't use environment proxy settings
        
        print("📡 Starting streaming request...")
        response = session.post(url, json=payload, stream=True, timeout=60)
        
        # Restore proxy settings
        if old_http_proxy:
            os.environ['http_proxy'] = old_http_proxy
        if old_https_proxy:
            os.environ['https_proxy'] = old_https_proxy
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            print("✅ Streaming response received!")
            print("📋 Raw streaming data:")
            print("-" * 50)
            
            chunk_count = 0
            total_data = ""
            table_chunks = []
            
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    chunk_count += 1
                    print(f"Chunk {chunk_count}: {repr(line)}")
                    
                    # Check if it's SSE format
                    if line.startswith('data: '):
                        data_part = line[6:]  # Remove 'data: ' prefix
                        
                        if data_part.strip() == '[DONE]':
                            print("🏁 Stream completed")
                            break
                        
                        try:
                            chunk_data = json.loads(data_part)
                            print(f"  📦 Parsed chunk: {chunk_data.get('type', 'unknown')}")
                            
                            if 'content' in chunk_data:
                                content = chunk_data['content']
                                total_data += content
                                
                                # Check for table content
                                if '<table_stream>' in content or '</table_stream>' in content or '|' in content:
                                    table_chunks.append(content)
                                    print(f"  📊 Table content detected: {repr(content)}")
                            
                        except json.JSONDecodeError as e:
                            print(f"  ❌ JSON parse error: {e}")
                            print(f"  Raw data: {repr(data_part)}")
            
            print("-" * 50)
            print()
            
            print("📊 Analysis:")
            print(f"  Total chunks: {chunk_count}")
            print(f"  Total data length: {len(total_data)}")
            print(f"  Table chunks: {len(table_chunks)}")
            print()
            
            if table_chunks:
                print("🔍 Table Content Analysis:")
                print("-" * 30)
                
                # Reconstruct table from chunks
                table_content = ''.join(table_chunks)
                print(f"Reconstructed table content:")
                print(repr(table_content))
                print()
                
                # Check for table_stream tags
                if '<table_stream>' in table_content and '</table_stream>' in table_content:
                    print("✅ Complete table_stream found")
                    
                    # Extract table data
                    start = table_content.find('<table_stream>') + len('<table_stream>')
                    end = table_content.find('</table_stream>')
                    if end != -1:
                        table_data = table_content[start:end].strip()
                        print("📋 Extracted table data:")
                        print(table_data)
                        print()
                        
                        # Analyze table structure
                        lines = table_data.split('\n')
                        if lines:
                            header_line = lines[0]
                            headers = header_line.split('|')
                            print(f"📊 Headers ({len(headers)}): {headers}")
                            
                            for i, line in enumerate(lines[1:], 1):
                                if line.strip():
                                    cells = line.split('|')
                                    print(f"Row {i} ({len(cells)} cells): {cells}")
                                    
                                    if len(cells) != len(headers):
                                        print(f"  ⚠️ Column mismatch! Expected {len(headers)}, got {len(cells)}")
                                    else:
                                        print(f"  ✅ Column count matches")
                else:
                    print("❌ Incomplete table_stream in chunks")
                    print("Available content:")
                    for i, chunk in enumerate(table_chunks):
                        print(f"  Chunk {i+1}: {repr(chunk)}")
            else:
                print("❌ No table content detected in streaming response")
                
            print()
            print("📄 Complete accumulated data:")
            print("-" * 30)
            print(total_data)
            print("-" * 30)
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Backend server not running")
        
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_streaming_endpoint()
