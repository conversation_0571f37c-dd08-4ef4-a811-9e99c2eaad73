<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Table Parsing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .warning {
            color: orange;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Frontend Table Parsing Test</h1>
        <p>Testing frontend table parsing with the exact data from backend</p>

        <div class="test-section">
            <h3>📊 Test 1: Backend Table Data (Perfect Format)</h3>
            <div id="test1-log" class="log"></div>
            <div id="test1-result"></div>
        </div>

        <div class="test-section">
            <h3>🔄 Test 2: Streaming Parser Simulation</h3>
            <div id="test2-log" class="log"></div>
            <div id="test2-result"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Test 3: Direct HTML Conversion</h3>
            <div id="test3-log" class="log"></div>
            <div id="test3-result"></div>
        </div>
    </div>

    <script>
        // Exact table data from backend test
        const backendTableData = `<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.406(+/-0.005)in x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015(+/-0.0015)in x 16.50(+/-0.005)in x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish
004|S/S 430 BA NO PI|0.015(+/-0.0015)in x 19.68(+/-0.03125)in x COIL|8835#|0.38(+/-0.04)mm x 499.87(+/-0.79)mm x COIL|4007.5 kg|BA Finish
005|S/S 430 BA NO PI|0.015(+/-0.0015)in x 47.438(+/-0.03125)in x COIL|57655#|0.38(+/-0.04)mm x 1204.93(+/-0.79)mm x COIL|26151.9 kg|BA Finish
</table_stream>`;

        function log(testId, message, type = 'info') {
            const logElement = document.getElementById(`${testId}-log`);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Test 1: Parse backend table data directly
        function test1_parseBackendData() {
            log('test1', '🚀 Starting Test 1: Backend Table Data Parsing');
            
            try {
                // Extract table content
                const streamMatch = backendTableData.match(/<table_stream>([\s\S]*?)<\/table_stream>/);
                if (!streamMatch) {
                    log('test1', '❌ No table_stream tags found', 'error');
                    return;
                }

                const tableContent = streamMatch[1].trim();
                log('test1', `✅ Extracted table content (${tableContent.length} chars)`);
                
                // Split into lines
                const lines = tableContent.split('\n').filter(line => line.trim());
                log('test1', `📋 Found ${lines.length} lines`);
                
                if (lines.length === 0) {
                    log('test1', '❌ No lines found in table content', 'error');
                    return;
                }

                // Parse headers
                const headerLine = lines[0];
                const headers = headerLine.split('|').map(h => h.trim());
                log('test1', `📊 Headers (${headers.length}): ${headers.join(', ')}`, 'success');

                // Parse data rows
                const dataRows = [];
                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i];
                    const cells = line.split('|').map(c => c.trim());
                    log('test1', `Row ${i}: ${cells.length} cells - ${cells.join(' | ')}`);
                    
                    if (cells.length !== headers.length) {
                        log('test1', `⚠️ Row ${i} column mismatch: expected ${headers.length}, got ${cells.length}`, 'warning');
                    } else {
                        log('test1', `✅ Row ${i} column count matches`, 'success');
                    }
                    
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header] = cells[index] || '';
                    });
                    dataRows.push(row);
                }

                // Generate HTML table
                let html = '<table>';
                html += '<thead><tr>';
                headers.forEach(header => {
                    html += `<th>${escapeHtml(header)}</th>`;
                });
                html += '</tr></thead>';
                
                html += '<tbody>';
                dataRows.forEach(row => {
                    html += '<tr>';
                    headers.forEach(header => {
                        const cellContent = row[header] || '';
                        html += `<td>${escapeHtml(cellContent)}</td>`;
                    });
                    html += '</tr>';
                });
                html += '</tbody></table>';

                document.getElementById('test1-result').innerHTML = html;
                log('test1', '✅ Table rendered successfully', 'success');

            } catch (error) {
                log('test1', `❌ Error: ${error.message}`, 'error');
            }
        }

        // Test 2: Simulate streaming parser
        function test2_streamingParser() {
            log('test2', '🚀 Starting Test 2: Streaming Parser Simulation');
            
            // Simulate streaming chunks
            const chunks = [
                '<table_stream>\nItem Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks\n',
                '001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish\n',
                '002|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.406(+/-0.005)in x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish\n',
                '003|S/S 430 BA NO PI|0.015(+/-0.0015)in x 16.50(+/-0.005)in x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish\n',
                '004|S/S 430 BA NO PI|0.015(+/-0.0015)in x 19.68(+/-0.03125)in x COIL|8835#|0.38(+/-0.04)mm x 499.87(+/-0.79)mm x COIL|4007.5 kg|BA Finish\n',
                '005|S/S 430 BA NO PI|0.015(+/-0.0015)in x 47.438(+/-0.03125)in x COIL|57655#|0.38(+/-0.04)mm x 1204.93(+/-0.79)mm x COIL|26151.9 kg|BA Finish\n',
                '</table_stream>'
            ];

            let buffer = '';
            let chunkIndex = 0;

            function processNextChunk() {
                if (chunkIndex >= chunks.length) {
                    log('test2', '✅ All chunks processed', 'success');
                    return;
                }

                const chunk = chunks[chunkIndex];
                buffer += chunk;
                log('test2', `📦 Chunk ${chunkIndex + 1}: "${chunk.replace(/\n/g, '\\n')}"`);
                log('test2', `📋 Buffer length: ${buffer.length}`);

                // Check if we have complete table
                const streamMatch = buffer.match(/<table_stream>([\s\S]*?)<\/table_stream>/);
                if (streamMatch) {
                    log('test2', '✅ Complete table found in buffer', 'success');
                    
                    const tableContent = streamMatch[1].trim();
                    const lines = tableContent.split('\n').filter(line => line.trim());
                    
                    if (lines.length > 0) {
                        const headers = lines[0].split('|').map(h => h.trim());
                        log('test2', `📊 Parsed ${headers.length} headers: ${headers.join(', ')}`);
                        
                        const dataRows = lines.slice(1).map(line => {
                            const cells = line.split('|').map(c => c.trim());
                            const row = {};
                            headers.forEach((header, index) => {
                                row[header] = cells[index] || '';
                            });
                            return row;
                        });
                        
                        log('test2', `📊 Parsed ${dataRows.length} data rows`);
                        
                        // Generate table HTML
                        let html = '<table>';
                        html += '<thead><tr>';
                        headers.forEach(header => {
                            html += `<th>${escapeHtml(header)}</th>`;
                        });
                        html += '</tr></thead>';
                        
                        html += '<tbody>';
                        dataRows.forEach(row => {
                            html += '<tr>';
                            headers.forEach(header => {
                                const cellContent = row[header] || '';
                                html += `<td>${escapeHtml(cellContent)}</td>`;
                            });
                            html += '</tr>';
                        });
                        html += '</tbody></table>';

                        document.getElementById('test2-result').innerHTML = html;
                    }
                } else {
                    log('test2', '⏳ Waiting for complete table...');
                }

                chunkIndex++;
                setTimeout(processNextChunk, 500); // Simulate streaming delay
            }

            processNextChunk();
        }

        // Test 3: Direct HTML conversion
        function test3_directConversion() {
            log('test3', '🚀 Starting Test 3: Direct HTML Conversion');
            
            try {
                // Extract just the table data (without tags)
                const tableContent = `Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.406(+/-0.005)in x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015(+/-0.0015)in x 16.50(+/-0.005)in x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish
004|S/S 430 BA NO PI|0.015(+/-0.0015)in x 19.68(+/-0.03125)in x COIL|8835#|0.38(+/-0.04)mm x 499.87(+/-0.79)mm x COIL|4007.5 kg|BA Finish
005|S/S 430 BA NO PI|0.015(+/-0.0015)in x 47.438(+/-0.03125)in x COIL|57655#|0.38(+/-0.04)mm x 1204.93(+/-0.79)mm x COIL|26151.9 kg|BA Finish`;

                const lines = tableContent.split('\n');
                const headers = lines[0].split('|').map(h => h.trim());
                
                log('test3', `📊 Headers: ${headers.join(', ')}`);
                
                let html = '<table border="1" cellpadding="4" cellspacing="0" style="border-collapse: collapse; font-family: Arial, sans-serif;">';
                
                // Headers
                html += '<thead><tr style="background-color: #f0f0f0; font-weight: bold;">';
                headers.forEach(header => {
                    html += `<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">${escapeHtml(header)}</th>`;
                });
                html += '</tr></thead>';
                
                // Data rows
                html += '<tbody>';
                for (let i = 1; i < lines.length; i++) {
                    const cells = lines[i].split('|').map(c => c.trim());
                    const rowStyle = i % 2 === 1 ? 'background-color: #ffffff;' : 'background-color: #f9f9f9;';
                    html += `<tr style="${rowStyle}">`;
                    
                    cells.forEach((cell, cellIndex) => {
                        const isNumeric = cellIndex === 0; // Item Code should be right-aligned
                        const textAlign = isNumeric ? 'right' : 'left';
                        html += `<td style="border: 1px solid #ccc; padding: 8px; text-align: ${textAlign};">${escapeHtml(cell)}</td>`;
                    });
                    html += '</tr>';
                    
                    log('test3', `Row ${i}: ${cells.length} cells`);
                }
                html += '</tbody></table>';

                document.getElementById('test3-result').innerHTML = html;
                log('test3', '✅ Direct HTML table generated', 'success');

            } catch (error) {
                log('test3', `❌ Error: ${error.message}`, 'error');
            }
        }

        // Run all tests
        window.onload = function() {
            test1_parseBackendData();
            setTimeout(test2_streamingParser, 1000);
            setTimeout(test3_directConversion, 5000);
        };
    </script>
</body>
</html>
