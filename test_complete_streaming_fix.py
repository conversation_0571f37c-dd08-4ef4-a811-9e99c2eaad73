#!/usr/bin/env python3
"""
Complete test to verify that streaming table display works correctly
Tests both backend streaming and frontend parsing logic
"""

import requests
import json
import time
import os

def test_backend_streaming():
    """Test backend streaming functionality"""
    print("🔧 Testing Backend Streaming...")
    print("-" * 40)
    
    test_input = """
    Please convert the following steel specifications from imperial to metric and create a table:

    001 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.343(+/-0.005)" x COIL - 7190#
    002 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.406(+/-0.005)" x COIL - 8061#
    003 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 16.50(+/-0.005)" x COIL - 12550#
    """
    
    url = "http://localhost:8000/api/llm/stream"
    payload = {
        "text": test_input,
        "unit_system": "metric",
        "function": "table"
    }
    
    try:
        # Clear proxy environment variables
        old_http_proxy = os.environ.get('http_proxy')
        old_https_proxy = os.environ.get('https_proxy')
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']
        
        session = requests.Session()
        session.trust_env = False
        
        print("📡 Sending streaming request...")
        response = session.post(url, json=payload, stream=True, timeout=60)
        
        # Restore proxy settings
        if old_http_proxy:
            os.environ['http_proxy'] = old_http_proxy
        if old_https_proxy:
            os.environ['https_proxy'] = old_https_proxy
        
        if response.status_code == 200:
            print("✅ Backend streaming working")
            
            # Collect streaming data for frontend simulation
            streaming_data = []
            chunk_count = 0
            
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith('data: '):
                    data_part = line[6:]
                    
                    if data_part.strip() == '[DONE]':
                        break
                    
                    try:
                        chunk_data = json.loads(data_part)
                        chunk_count += 1
                        
                        if 'accumulated_content' in chunk_data:
                            accumulated_content = chunk_data['accumulated_content']
                            
                            # Store significant milestones
                            if '<table_stream>' in accumulated_content:
                                if '</table_stream>' in accumulated_content:
                                    streaming_data.append({
                                        'chunk': chunk_count,
                                        'content': accumulated_content,
                                        'milestone': 'COMPLETE'
                                    })
                                    break
                                elif accumulated_content.count('\n') >= 2:  # Headers + at least one row
                                    streaming_data.append({
                                        'chunk': chunk_count,
                                        'content': accumulated_content,
                                        'milestone': 'PARTIAL_WITH_DATA'
                                    })
                                elif accumulated_content.count('\n') >= 1:  # Headers only
                                    streaming_data.append({
                                        'chunk': chunk_count,
                                        'content': accumulated_content,
                                        'milestone': 'HEADERS_ONLY'
                                    })
                                    
                    except json.JSONDecodeError:
                        pass
            
            print(f"📊 Collected {len(streaming_data)} milestone chunks from {chunk_count} total chunks")
            return streaming_data
            
        else:
            print(f"❌ Backend streaming failed: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        return []

def test_frontend_parsing(streaming_data):
    """Test frontend parsing logic with real streaming data"""
    print("\n🎨 Testing Frontend Parsing Logic...")
    print("-" * 40)
    
    if not streaming_data:
        print("❌ No streaming data to test")
        return False
    
    # Simulate the fixed StreamingTableParser logic
    class StreamingTableParser:
        def __init__(self):
            self.headers = []
            self.rows = []
            self.isHeaderParsed = False
            self.buffer = ''
            self.expectedHeaders = ['Item Code', 'Description', 'Size (Original)', 'Customer QTY', 'Size (Converted)', 'Converted QTY', 'Remarks']

        def addChunk(self, accumulatedContent):
            self.buffer = accumulatedContent
            
            # Look for table_stream tags
            streamMatch = None
            if '<table_stream>' in self.buffer:
                start = self.buffer.find('<table_stream>') + len('<table_stream>')
                if '</table_stream>' in self.buffer:
                    end = self.buffer.find('</table_stream>')
                    tableContent = self.buffer[start:end].strip()
                    isComplete = True
                else:
                    tableContent = self.buffer[start:].strip()
                    isComplete = False
                
                if tableContent:
                    return self.parseStreamingContent(tableContent, isComplete)
            
            return { 'headers': self.headers, 'rows': self.rows, 'isValid': False, 'error': 'Waiting for table_stream content' }

        def parseStreamingContent(self, content, isComplete=False):
            lines = content.split('\n')
            lines = [line.strip() for line in lines if line.strip()]
            
            # Parse headers
            if not self.isHeaderParsed and len(lines) > 0:
                headerLine = lines[0]
                headers = headerLine.split('|')
                headers = [h.strip() for h in headers if h.strip()]
                
                if len(headers) >= 6:  # Allow 6 or 7 headers
                    self.headers = headers
                    self.isHeaderParsed = True
                else:
                    return {
                        'headers': self.expectedHeaders,
                        'rows': [],
                        'isValid': False,
                        'error': f'Waiting for complete header line (got {len(headers)} columns)',
                        'isComplete': False
                    }
            
            # Parse data rows
            if self.isHeaderParsed:
                self.rows = []
                dataLines = lines[1:]  # Skip header line
                
                for line in dataLines:
                    cells = line.split('|')
                    cells = [c.strip() for c in cells]
                    
                    # Create row object
                    row = {}
                    for i, header in enumerate(self.headers):
                        row[header] = cells[i] if i < len(cells) else ''
                    self.rows.append(row)
            
            # Validation logic (the key fix)
            hasValidHeaders = len(self.headers) >= 6
            hasRows = len(self.rows) > 0
            
            if isComplete:
                # Final validation: require complete table
                isValid = hasValidHeaders and hasRows
                error = None if isValid else f'Complete table validation failed: {len(self.headers)} headers, {len(self.rows)} rows'
            else:
                # Streaming validation: allow display if we have headers (progressive display)
                isValid = hasValidHeaders
                error = None if isValid else f'Waiting for headers: {len(self.headers)} headers'
            
            return {
                'headers': self.headers,
                'rows': self.rows,
                'isValid': isValid,
                'error': error,
                'isComplete': isComplete
            }
    
    # Test the parser with streaming data
    parser = StreamingTableParser()
    streaming_display_works = True
    
    for i, data in enumerate(streaming_data):
        print(f"\n📦 Testing chunk {data['chunk']} ({data['milestone']}):")
        
        result = parser.addChunk(data['content'])
        
        print(f"   Headers: {len(result['headers'])}")
        print(f"   Rows: {len(result['rows'])}")
        print(f"   Valid: {result['isValid']}")
        print(f"   Complete: {result.get('isComplete', False)}")
        print(f"   Error: {result.get('error', 'None')}")
        
        # Check if streaming display would work
        if data['milestone'] == 'HEADERS_ONLY':
            if not result['isValid']:
                print("   ❌ Headers should be valid for streaming display")
                streaming_display_works = False
            else:
                print("   ✅ Headers valid - streaming display would show table structure")
        
        elif data['milestone'] == 'PARTIAL_WITH_DATA':
            if not result['isValid'] or len(result['rows']) == 0:
                print("   ❌ Partial data should be valid for streaming display")
                streaming_display_works = False
            else:
                print(f"   ✅ Partial data valid - streaming display would show {len(result['rows'])} rows")
        
        elif data['milestone'] == 'COMPLETE':
            if not result['isValid'] or not result.get('isComplete', False):
                print("   ❌ Complete table should be valid and marked complete")
                streaming_display_works = False
            else:
                print(f"   ✅ Complete table valid - final display with {len(result['rows'])} rows")
    
    return streaming_display_works

def main():
    print("🧪 Complete Streaming Fix Test")
    print("=" * 50)
    
    # Test backend streaming
    streaming_data = test_backend_streaming()
    
    # Test frontend parsing
    frontend_works = test_frontend_parsing(streaming_data)
    
    print("\n🏁 Final Results:")
    print("-" * 20)
    
    if streaming_data and frontend_works:
        print("🎉 STREAMING DISPLAY FIX: SUCCESS")
        print("   ✅ Backend streaming works correctly")
        print("   ✅ Frontend parsing supports progressive display")
        print("   ✅ Tables will display during streaming")
        print("   ✅ Data integrity maintained")
    else:
        print("❌ STREAMING DISPLAY FIX: ISSUES DETECTED")
        if not streaming_data:
            print("   ❌ Backend streaming issues")
        if not frontend_works:
            print("   ❌ Frontend parsing issues")

if __name__ == "__main__":
    main()
