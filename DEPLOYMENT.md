# Deployment Guide for Steel Unit Converter on Aliyun

## Infrastructure Requirements

### <PERSON>yun ECS Instance
- Instance Type: ecs.t6-c1m2.large or similar
- Memory: 2GB RAM
- vCPU: 1 core
- System Disk: 40GB ESSD
- Operating System: Ubuntu 20.04 LTS

### Aliyun RDS Instance
- Instance Type: rds.mysql.t1.small or similar
- Database: PostgreSQL 15
- Storage: 20GB
- Network Type: VPC

## Pre-deployment Setup

### 1. RDS Setup
1. Create RDS instance
2. Configure VPC and security groups
3. Create database and user
4. Note down connection details

### 2. ECS Setup
```bash
# Update system
sudo apt-get update && sudo apt-get upgrade -y

# Install required packages
sudo apt-get install -y \
    apt-transport-https \
    ca-certificates \
    curl \
    software-properties-common

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create application directory
mkdir -p /app/steel-converter
cd /app/steel-converter
```

### 3. Memory Optimization
1. Configure swap space:
```bash
# Create 2GB swap file
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

2. Configure system limits:
```bash
# Add to /etc/sysctl.conf
echo "vm.swappiness=10" | sudo tee -a /etc/sysctl.conf
echo "vm.vfs_cache_pressure=50" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## Deployment Steps

### 1. Application Setup
```bash
# Clone repository
git clone <repository-url> .

# Create environment file
cp .env.example .env

# Update environment variables
nano .env
```

### 2. SSL Certificate Setup
```bash
# Create SSL directory
mkdir -p ssl
cd ssl

# Generate self-signed certificate or place your SSL certificates here
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout key.pem -out cert.pem
```

### 3. Start Application
```bash
# Build and start containers
sudo docker-compose up -d --build

# Check logs
sudo docker-compose logs -f
```

## Monitoring and Maintenance

### 1. Resource Monitoring
```bash
# Monitor container resources
docker stats

# Monitor system resources
htop
```

### 2. Log Management
```bash
# View application logs
sudo docker-compose logs -f backend
sudo docker-compose logs -f frontend

# Monitor nginx error log
sudo tail -f /var/log/nginx/error.log
```

### 3. Backup Strategy
1. Configure RDS automatic backups in Aliyun console
2. Set up regular configuration backups:
```bash
# Create backup script
cat > /app/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/app/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR
cp -r /app/steel-converter/.env $BACKUP_DIR/env_$DATE
cp -r /app/steel-converter/ssl $BACKUP_DIR/ssl_$DATE
EOF

chmod +x /app/backup.sh
```

### 4. Performance Optimization
1. Monitor RDS performance metrics in Aliyun console
2. Configure CloudMonitor for ECS monitoring
3. Set up alerts for resource usage

## Troubleshooting

### Common Issues

1. Memory Issues
```bash
# Check memory usage
free -m
docker stats

# Clear page cache if needed
sudo sync; sudo echo 3 > /proc/sys/vm/drop_caches
```

2. Connection Issues
```bash
# Test RDS connection
nc -zv your-rds-instance.rds.aliyuncs.com 3433

# Check container networking
docker network inspect app-network
```

3. Performance Issues
```bash
# Check container logs for bottlenecks
docker-compose logs --tail=100 backend

# Monitor nginx performance
nginx -t
nginx -V
```

## Security Considerations

1. Aliyun Security Groups:
   - Allow only necessary ports (80, 443)
   - Restrict RDS access to ECS security group

2. Application Security:
   - Regular security updates
   - Monitor audit logs
   - Use strong passwords

3. SSL/TLS:
   - Keep certificates up to date
   - Use modern cipher suites
   - Enable HTTP/2

## Scaling Considerations

1. Vertical Scaling:
   - Monitor resource usage
   - Upgrade ECS instance if needed
   - Upgrade RDS instance if needed

2. Horizontal Scaling:
   - Consider using SLB for multiple ECS instances
   - Use RDS read replicas if needed
   - Implement caching with Redis

## Support and Maintenance

1. Regular Maintenance:
```bash
# Update system packages
sudo apt-get update && sudo apt-get upgrade -y

# Update Docker images
docker-compose pull
docker-compose up -d

# Clean up unused resources
docker system prune -af
```

2. Monitoring:
   - Set up CloudMonitor alerts
   - Monitor application logs
   - Check resource usage regularly
