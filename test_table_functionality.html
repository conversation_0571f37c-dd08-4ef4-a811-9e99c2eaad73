<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-input {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
        }
        .test-output {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Steel Unit Converter - Table Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Standard Steel Data</h2>
        <textarea class="test-input" id="test1" placeholder="Enter steel data here...">S/S 430 BA NO PI
.015(+/-.0015) X 2.343"(+/-.005) X COIL 7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL 8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL 12,550#</textarea>
        <button onclick="testTableParsing('test1', 'output1')">Test Parsing</button>
        <button onclick="testStreamingSimulation('test1', 'output1')">Simulate Streaming</button>
        <div id="output1" class="test-output"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Converted Content Format</h2>
        <textarea class="test-input" id="test2" placeholder="Enter converted content...">Based on your steel specifications, here are the conversions:

<converted_content>
| Product Type | Thickness (mm) | Width (mm) | Weight (kg) |
|--------------|----------------|------------|-------------|
| S/S 430 BA NO PI | 0.381 | 59.51 | 3260.18 |
| S/S 430 BA NO PI | 0.381 | 61.11 | 3656.68 |
| S/S 430 BA NO PI | 0.381 | 419.10 | 5692.77 |
</converted_content>

These conversions are based on standard steel density calculations.</textarea>
        <button onclick="testTableParsing('test2', 'output2')">Test Parsing</button>
        <button onclick="testStreamingSimulation('test2', 'output2')">Simulate Streaming</button>
        <div id="output2" class="test-output"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Malformed Table</h2>
        <textarea class="test-input" id="test3" placeholder="Enter malformed data...">This is some text with a broken table:

| Product | Thickness
| S/S 430 | 0.381 | 59.51
| Missing header | data

More text after the table.</textarea>
        <button onclick="testTableParsing('test3', 'output3')">Test Parsing</button>
        <div id="output3" class="test-output"></div>
    </div>

    <script>
        // Simplified table parsing functions (based on the actual implementation)
        function containsMarkdownTable(text) {
            if (!text || typeof text !== 'string') return false;
            const lines = text.trim().split('\n').filter(line => line.trim().length > 0);
            if (lines.length < 2) return false;
            
            // Look for pipe characters and separator lines
            const hasPipes = lines.some(line => line.includes('|'));
            const hasSeparator = lines.some(line => line.match(/^[\|\s]*[-:]+[\|\s-:]*$/));
            
            return hasPipes && (hasSeparator || lines.length >= 3);
        }

        function parseMarkdownTable(markdownTable) {
            try {
                if (!markdownTable || typeof markdownTable !== 'string') {
                    return { headers: [], rows: [], isValid: false, error: 'Empty or invalid table content' };
                }

                const lines = markdownTable.trim().split('\n').filter(line => line.trim().length > 0);
                if (lines.length < 2) {
                    return { headers: [], rows: [], isValid: false, error: 'Insufficient table rows' };
                }

                // Parse headers
                const headerLine = lines[0].trim();
                let headers = [];
                if (headerLine.includes('|')) {
                    const cleanHeader = headerLine.replace(/^\||\|$/g, '');
                    headers = cleanHeader.split('|').map(h => h.trim()).filter(h => h.length > 0);
                } else {
                    headers = headerLine.split(/\s{2,}|\t/).map(h => h.trim()).filter(h => h.length > 0);
                }

                if (headers.length === 0) {
                    return { headers: [], rows: [], isValid: false, error: 'No valid headers found' };
                }

                // Find separator line
                let separatorIndex = -1;
                for (let i = 1; i < Math.min(3, lines.length); i++) {
                    const line = lines[i].trim();
                    if (line.match(/^[\|\s]*[-:]+[\|\s-:]*$/)) {
                        separatorIndex = i;
                        break;
                    }
                }

                let startRowIndex = separatorIndex !== -1 ? separatorIndex + 1 : 1;

                // Parse rows
                const rows = [];
                for (let i = startRowIndex; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line || line.startsWith('<!--') || line.match(/^[\|\s-:]*$/)) continue;

                    let cells = [];
                    if (line.includes('|')) {
                        const cleanLine = line.replace(/^\||\|$/g, '');
                        cells = cleanLine.split('|').map(cell => cell.trim());
                    } else {
                        cells = line.split(/\s{2,}|\t/).map(cell => cell.trim()).filter(cell => cell.length > 0);
                    }

                    if (cells.some(cell => cell.length > 0)) {
                        const row = {};
                        headers.forEach((header, index) => {
                            row[header] = cells[index] || '';
                        });
                        rows.push(row);
                    }
                }

                const isValid = headers.length > 0 && rows.length > 0;
                return { 
                    headers, 
                    rows, 
                    isValid,
                    error: isValid ? undefined : 'No valid table data found'
                };

            } catch (error) {
                return { 
                    headers: [], 
                    rows: [], 
                    isValid: false, 
                    error: `Parsing error: ${error.message}`
                };
            }
        }

        function parseConvertedContent(content) {
            const convertedMatch = content.match(/<converted_content>([\s\S]*?)<\/converted_content>/);
            if (!convertedMatch) {
                return { textContent: content, tableContent: null };
            }

            const convertedContent = convertedMatch[1].trim();
            const textBeforeTag = content.substring(0, convertedMatch.index || 0).trim();
            const textAfterTag = content.substring((convertedMatch.index || 0) + convertedMatch[0].length).trim();
            
            const textContent = [textBeforeTag, textAfterTag].filter(Boolean).join('\n\n');
            
            if (containsMarkdownTable(convertedContent)) {
                return { textContent, tableContent: convertedContent };
            }
            
            return { textContent: textContent + '\n\n' + convertedContent, tableContent: null };
        }

        function renderTable(parsedTable) {
            if (!parsedTable.isValid || !parsedTable.headers || !parsedTable.rows) {
                return `<div class="error">Table format invalid: ${parsedTable.error || 'Unknown error'}</div>`;
            }

            let html = '<table>';
            
            // Headers
            html += '<thead><tr>';
            parsedTable.headers.forEach(header => {
                html += `<th>${escapeHtml(header)}</th>`;
            });
            html += '</tr></thead>';
            
            // Rows
            html += '<tbody>';
            parsedTable.rows.forEach(row => {
                html += '<tr>';
                parsedTable.headers.forEach(header => {
                    const cellContent = row[header] || '';
                    html += `<td>${escapeHtml(cellContent)}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody>';
            
            html += '</table>';
            return html;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function testTableParsing(inputId, outputId) {
            const input = document.getElementById(inputId).value;
            const output = document.getElementById(outputId);
            
            try {
                const { textContent, tableContent } = parseConvertedContent(input);
                
                let result = '<div class="success">Parsing successful!</div>';
                
                if (textContent) {
                    result += `<h4>Text Content:</h4><pre>${escapeHtml(textContent)}</pre>`;
                }
                
                if (tableContent) {
                    result += `<h4>Table Content:</h4>`;
                    const parsedTable = parseMarkdownTable(tableContent);
                    result += renderTable(parsedTable);
                    
                    if (parsedTable.isValid) {
                        result += `<p><strong>Headers:</strong> ${parsedTable.headers.length}, <strong>Rows:</strong> ${parsedTable.rows.length}</p>`;
                    }
                } else {
                    result += '<div class="error">No table content found</div>';
                }
                
                output.innerHTML = result;
            } catch (error) {
                output.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }

        function testStreamingSimulation(inputId, outputId) {
            const input = document.getElementById(inputId).value;
            const output = document.getElementById(outputId);
            
            output.innerHTML = '<div>Simulating streaming...</div>';
            
            // Simulate streaming by adding content gradually
            let currentContent = '';
            const chunks = input.split(' ');
            let chunkIndex = 0;
            
            const streamInterval = setInterval(() => {
                if (chunkIndex < chunks.length) {
                    currentContent += chunks[chunkIndex] + ' ';
                    chunkIndex++;
                    
                    // Test parsing at each step
                    try {
                        const { textContent, tableContent } = parseConvertedContent(currentContent);
                        
                        let result = `<div>Streaming progress: ${chunkIndex}/${chunks.length}</div>`;
                        result += `<div style="background: #f0f0f0; padding: 10px; margin: 10px 0;"><pre>${escapeHtml(currentContent)}</pre></div>`;
                        
                        if (tableContent) {
                            const parsedTable = parseMarkdownTable(tableContent);
                            if (parsedTable.isValid) {
                                result += '<div class="success">Table detected and parsed successfully!</div>';
                                result += renderTable(parsedTable);
                            } else {
                                result += '<div class="error">Table detected but parsing failed</div>';
                            }
                        }
                        
                        output.innerHTML = result;
                    } catch (error) {
                        output.innerHTML += `<div class="error">Streaming error: ${error.message}</div>`;
                    }
                } else {
                    clearInterval(streamInterval);
                    output.innerHTML += '<div class="success">Streaming completed!</div>';
                }
            }, 100);
        }

        // Auto-run first test on page load
        window.onload = function() {
            testTableParsing('test1', 'output1');
        };
    </script>
</body>
</html> 