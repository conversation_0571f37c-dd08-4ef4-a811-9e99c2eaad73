#!/usr/bin/env python3
"""
Database Clearing Script for Steel Unit Converter

This script removes all data from the database while preserving the schema.
It's useful during development to reset the database to a clean state.
"""

import os
import sys
import logging
import argparse
from pathlib import Path
from dotenv import load_dotenv
import asyncio
from typing import Dict, Any, List

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger("clear-database")

def load_env_file(env_file_path):
    """Load environment variables from .env file."""
    logger.info(f"Loading environment variables from {env_file_path}")
    env_vars = {}
    
    try:
        with open(env_file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                key, value = line.split('=', 1)
                env_vars[key.strip()] = value.strip()
        
        # Set environment variables
        for key, value in env_vars.items():
            os.environ[key] = value
        
        logger.info(f"Loaded {len(env_vars)} environment variables")
        return env_vars
    except Exception as e:
        logger.error(f"Failed to load environment variables: {e}")
        return {}

def get_database_type(env_vars):
    """Determine the database type from environment variables."""
    database_url = env_vars.get('DATABASE_URL', '')
    
    if database_url.startswith('sqlite'):
        return 'sqlite'
    elif database_url.startswith('mysql'):
        return 'mysql'
    elif 'RDS_HOSTNAME' in env_vars:
        return 'mysql'
    else:
        return 'sqlite'  # Default to SQLite

def clear_sqlite_database(db_path):
    """Clear all data from SQLite database."""
    logger.info(f"Clearing data from SQLite database at {db_path}")
    
    try:
        import sqlite3
        
        # Connect to the SQLite database
        connection = sqlite3.connect(db_path)
        
        # Create a cursor
        cursor = connection.cursor()
        
        # Get the list of tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor.fetchall()
        
        # Disable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = OFF")
        
        # Begin transaction
        cursor.execute("BEGIN TRANSACTION")
        
        # Delete data from each table
        for table in tables:
            table_name = table[0]
            logger.info(f"Clearing data from table {table_name}")
            cursor.execute(f"DELETE FROM {table_name}")
        
        # Commit the transaction
        connection.commit()
        
        # Enable foreign key constraints
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Close the connection
        connection.close()
        
        logger.info("SQLite database cleared successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to clear SQLite database: {e}")
        return False

def clear_mysql_database(host, port, user, password, database):
    """Clear all data from MySQL database."""
    logger.info(f"Clearing data from MySQL database {database} at {host}:{port}")
    
    try:
        import pymysql
        
        # Connect to the MySQL database
        connection = pymysql.connect(
            host=host,
            port=int(port),
            user=user,
            password=password,
            database=database
        )
        
        # Create a cursor
        cursor = connection.cursor()
        
        # Disable foreign key checks
        cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
        
        # Get the list of tables
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        # Delete data from each table
        for table in tables:
            table_name = table[0]
            logger.info(f"Clearing data from table {table_name}")
            cursor.execute(f"TRUNCATE TABLE {table_name}")
        
        # Enable foreign key checks
        cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
        
        # Commit the changes
        connection.commit()
        
        # Close the connection
        connection.close()
        
        logger.info("MySQL database cleared successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to clear MySQL database: {e}")
        return False

async def clear_database_async():
    """Clear database using SQLAlchemy async API."""
    try:
        # Import necessary modules
        from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
        from sqlalchemy.future import select
        from sqlalchemy.orm import sessionmaker
        
        # Import database configuration
        sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
        from database import DATABASE_URL, async_engine
        
        # Import models
        from models.user_model import User
        from models.conversion_model import Conversion
        from models.chat_history import ChatHistory, ChatLike
        from models.auth_model import VerificationCode
        
        logger.info(f"Clearing database using SQLAlchemy async API with URL: {DATABASE_URL}")
        
        # Create async session
        async_session = sessionmaker(
            async_engine, expire_on_commit=False, class_=AsyncSession
        )
        
        # Define tables in order (to handle foreign key constraints)
        tables = [
            ChatLike,
            ChatHistory,
            Conversion,
            VerificationCode,
            User
        ]
        
        async with async_session() as session:
            # Disable foreign key constraints
            if DATABASE_URL.startswith('mysql'):
                await session.execute("SET FOREIGN_KEY_CHECKS = 0")
            elif DATABASE_URL.startswith('sqlite'):
                await session.execute("PRAGMA foreign_keys = OFF")
            
            # Delete data from each table
            for table in tables:
                logger.info(f"Clearing data from table {table.__tablename__}")
                await session.execute(f"DELETE FROM {table.__tablename__}")
            
            # Enable foreign key constraints
            if DATABASE_URL.startswith('mysql'):
                await session.execute("SET FOREIGN_KEY_CHECKS = 1")
            elif DATABASE_URL.startswith('sqlite'):
                await session.execute("PRAGMA foreign_keys = ON")
            
            # Commit the transaction
            await session.commit()
        
        logger.info("Database cleared successfully using SQLAlchemy async API")
        return True
    except Exception as e:
        logger.error(f"Failed to clear database using SQLAlchemy async API: {e}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Database Clearing Script for Steel Unit Converter')
    parser.add_argument('--env-file', default='.env', help='Path to the environment file (default: .env)')
    parser.add_argument('--force', action='store_true', help='Force clearing without confirmation')
    args = parser.parse_args()
    
    logger.info("Starting database clearing script")
    
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Try to load environment variables from different locations
    env_vars = {}
    
    # First try the specified env file path
    env_file_path = os.path.join(current_dir, args.env_file)
    if os.path.exists(env_file_path):
        logger.info(f"Found .env file at {env_file_path}")
        env_vars = load_env_file(env_file_path)
    
    # If RDS_HOSTNAME is not found, try the backend directory
    if 'RDS_HOSTNAME' not in env_vars and 'DATABASE_URL' not in env_vars:
        backend_env_path = os.path.join(current_dir, 'backend', args.env_file)
        if os.path.exists(backend_env_path):
            logger.info(f"Trying backend .env file at {backend_env_path}")
            env_vars = load_env_file(backend_env_path)
    
    # If RDS_HOSTNAME is still not found, try the parent directory
    if 'RDS_HOSTNAME' not in env_vars and 'DATABASE_URL' not in env_vars:
        parent_env_path = os.path.join(os.path.dirname(current_dir), args.env_file)
        if os.path.exists(parent_env_path):
            logger.info(f"Trying parent directory .env file at {parent_env_path}")
            env_vars = load_env_file(parent_env_path)
    
    # Determine the database type
    database_type = get_database_type(env_vars)
    logger.info(f"Using database type: {database_type}")
    
    # Ask for confirmation unless --force is specified
    if not args.force:
        confirm = input(f"This will delete ALL DATA from the {database_type} database. Are you sure? (y/N): ")
        if confirm.lower() != 'y':
            logger.info("Operation cancelled by user")
            return 0
    
    # Try to clear the database using SQLAlchemy async API first
    try:
        success = asyncio.run(clear_database_async())
        if success:
            logger.info("Database cleared successfully")
            return 0
    except Exception as e:
        logger.warning(f"Failed to clear database using SQLAlchemy async API: {e}")
        logger.info("Falling back to direct database access")
    
    # If SQLAlchemy approach fails, use direct database access
    if database_type == 'sqlite':
        # Find SQLite database path
        sqlite_db_path = env_vars.get('DATABASE_URL', '').replace('sqlite:///', '')
        if not sqlite_db_path:
            # Look for SQLite database in common locations
            possible_paths = [
                os.path.join(current_dir, 'steel_unit_converter.db'),
                os.path.join(current_dir, 'backend', 'steel_unit_converter.db'),
                os.path.join(current_dir, 'database.db'),
                os.path.join(current_dir, 'backend', 'database.db')
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    sqlite_db_path = path
                    break
        
        if not sqlite_db_path:
            logger.error("SQLite database not found")
            return 1
        
        # Clear SQLite database
        if not clear_sqlite_database(sqlite_db_path):
            logger.error("Failed to clear SQLite database")
            return 1
    else:
        # Get MySQL connection details
        rds_hostname = env_vars.get('RDS_HOSTNAME', '')
        rds_port = env_vars.get('RDS_PORT', '3306')
        rds_username = env_vars.get('RDS_USERNAME', '')
        rds_password = env_vars.get('RDS_PASSWORD', '')
        rds_db_name = env_vars.get('RDS_DB_NAME', '')
        
        # Clear MySQL database
        if not clear_mysql_database(rds_hostname, rds_port, rds_username, rds_password, rds_db_name):
            logger.error("Failed to clear MySQL database")
            return 1
    
    logger.info("Database clearing completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
