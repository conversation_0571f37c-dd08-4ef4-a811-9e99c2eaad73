# Low Memory Deployment Guide (2GB Systems)

This guide provides instructions for deploying the Steel Unit Converter application on systems with limited memory (2GB or less).

## Quick Start

To start the application in low memory mode:

```bash
./start-prod-fixed.sh
```

This script will:
- Start the backend with a single Gun<PERSON> worker and 4 threads
- Start the frontend with memory-optimized settings
- Apply various memory optimizations throughout the stack

## Memory Optimizations

The following memory optimizations are applied:

### Backend Optimizations

1. **Single Worker Configuration**:
   - Uses only 1 Gunicorn worker instead of multiple workers
   - Uses 4 threads per worker to handle concurrent requests
   - Reduces memory usage while maintaining concurrency

2. **Gunicorn Settings**:
   - Uses `--max-requests 500` to recycle workers periodically
   - Uses `--max-requests-jitter 50` to prevent all workers from restarting simultaneously
   - Uses `--preload` to share application code between workers
   - Uses `/tmp` for temporary files

### Frontend Optimizations

1. **Node.js Memory Limits**:
   - Sets `--max-old-space-size=256` to limit Node.js heap size
   - Prevents memory-related crashes during build and runtime

2. **Build Optimizations**:
   - Disables minification when necessary to reduce memory usage during build
   - Splits chunks aggressively to reduce individual file sizes
   - Uses ES2015 target for better browser compatibility and smaller bundle size

3. **Serving Optimizations**:
   - Uses `serve` for static file serving with minimal memory footprint
   - Implements fallback mechanisms if the build process fails

### Nginx Optimizations (Optional)

If you choose to use Nginx as a reverse proxy, the `setup-nginx-fixed.sh` script applies these optimizations:

1. **Process and Connection Limits**:
   - Sets `worker_processes 1` to use a single worker
   - Limits file descriptors with `worker_rlimit_nofile 1024`
   - Sets `worker_connections 512` to limit concurrent connections
   - Disables `multi_accept` to reduce CPU usage

2. **Buffer Size Reductions**:
   - Reduces `client_body_buffer_size` to 8KB
   - Reduces `client_header_buffer_size` to 1KB
   - Limits `client_max_body_size` to 1MB
   - Sets `large_client_header_buffers` to 2 1KB buffers

3. **Caching and Compression**:
   - Enables gzip compression with a moderate level (2)
   - Adds caching for static assets with a 30-day expiration
   - Uses a small SSL session cache (1MB)

## Usage

### Starting the Application

```bash
./start-prod-fixed.sh
```

### Stopping the Application

```bash
./start-prod-fixed.sh stop
```

### Checking Status

```bash
./start-prod-fixed.sh status
```

### Viewing Logs

```bash
./start-prod-fixed.sh logs
```

## Setting Up Nginx (Optional)

To set up Nginx as a reverse proxy with memory optimizations:

```bash
./setup-nginx-fixed.sh
```

This will:
1. Install Nginx if not already installed
2. Generate self-signed SSL certificates
3. Create an optimized Nginx configuration for low memory systems
4. Test and reload the Nginx configuration

After setting up Nginx, you can access the application at:
- https://localhost

## Troubleshooting

### Memory Issues

If you encounter memory issues:

1. **Check Memory Usage**:
   ```bash
   # On Linux
   free -m
   
   # On macOS
   vm_stat
   ```

2. **Monitor Process Memory**:
   ```bash
   # Check memory usage of specific processes
   ps -o pid,user,%mem,command ax | grep -E 'gunicorn|node|nginx' | sort -b -k3 -r
   ```

3. **Restart Services**:
   ```bash
   # Restart all services
   ./start-prod-fixed.sh stop
   ./start-prod-fixed.sh
   ```

### Frontend Build Failures

If the frontend build fails due to memory issues:

1. Try building with even lower memory settings:
   ```bash
   cd frontend
   NODE_OPTIONS="--max-old-space-size=128" npm run build -- --mode=production --minify=false
   ```

2. Use pre-built static files if available:
   ```bash
   # Copy pre-built files to the dist directory
   mkdir -p frontend/dist
   cp -r frontend/static/* frontend/dist/
   ```

### Nginx Issues

If Nginx fails to start or serve the application:

1. Check Nginx error logs:
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

2. Verify Nginx configuration:
   ```bash
   sudo nginx -t
   ```

3. Restart Nginx:
   ```bash
   sudo systemctl restart nginx
   # or
   sudo nginx -s reload
   ```

## Production Considerations

For production deployment on low memory systems:

1. **Use a CDN** for static assets to reduce server load
2. **Enable caching** wherever possible
3. **Monitor memory usage** regularly
4. **Schedule periodic restarts** to prevent memory leaks
5. **Consider upgrading memory** if possible for better performance

## Minimum Requirements

- **CPU**: 1 core
- **Memory**: 512MB (absolute minimum), 1GB (recommended minimum)
- **Disk**: 1GB free space
- **Operating System**: Linux (recommended), macOS, Windows with WSL
