#!/bin/bash

# Script to diagnose and fix 500 Internal Server Error
# This script checks logs and common issues that cause 500 errors

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
BACKEND_DIR="$SCRIPT_DIR/backend"
LOG_DIR="$SCRIPT_DIR/logs"

# Function to print section header
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Function to print success message
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print warning message
print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Function to print error message
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to check nginx logs
check_nginx_logs() {
    print_header "Checking Nginx Logs"
    
    # Check if nginx error log exists
    if [ -f "/var/log/nginx/error.log" ]; then
        echo "Last 20 lines of nginx error log:"
        sudo tail -n 20 /var/log/nginx/error.log
    else
        print_warning "nginx error log not found at /var/log/nginx/error.log"
        
        # Try to find nginx error log
        NGINX_ERROR_LOG=$(sudo find /var/log -name "error.log" | grep nginx | head -n 1)
        if [ -n "$NGINX_ERROR_LOG" ]; then
            echo "Found nginx error log at $NGINX_ERROR_LOG"
            echo "Last 20 lines of nginx error log:"
            sudo tail -n 20 "$NGINX_ERROR_LOG"
        else
            print_error "Could not find nginx error log"
        fi
    fi
    
    # Check if nginx access log exists
    if [ -f "/var/log/nginx/access.log" ]; then
        echo -e "\nLast 20 lines of nginx access log:"
        sudo tail -n 20 /var/log/nginx/access.log | grep -E "500|403|404"
    else
        print_warning "nginx access log not found at /var/log/nginx/access.log"
        
        # Try to find nginx access log
        NGINX_ACCESS_LOG=$(sudo find /var/log -name "access.log" | grep nginx | head -n 1)
        if [ -n "$NGINX_ACCESS_LOG" ]; then
            echo "Found nginx access log at $NGINX_ACCESS_LOG"
            echo "Last 20 lines of nginx access log with errors:"
            sudo tail -n 20 "$NGINX_ACCESS_LOG" | grep -E "500|403|404"
        else
            print_error "Could not find nginx access log"
        fi
    fi
}

# Function to check backend logs
check_backend_logs() {
    print_header "Checking Backend Logs"
    
    # Check if backend log exists
    if [ -f "$LOG_DIR/backend.log" ]; then
        echo "Last 20 lines of backend log:"
        tail -n 20 "$LOG_DIR/backend.log"
    else
        print_warning "Backend log not found at $LOG_DIR/backend.log"
    fi
}

# Function to check frontend logs
check_frontend_logs() {
    print_header "Checking Frontend Logs"
    
    # Check if frontend log exists
    if [ -f "$LOG_DIR/frontend.log" ]; then
        echo "Last 20 lines of frontend log:"
        tail -n 20 "$LOG_DIR/frontend.log"
    else
        print_warning "Frontend log not found at $LOG_DIR/frontend.log"
    fi
}

# Function to check file permissions
check_file_permissions() {
    print_header "Checking File Permissions"
    
    # Check frontend dist directory
    echo "Checking frontend dist directory permissions..."
    if [ -d "$FRONTEND_DIR/dist" ]; then
        ls -la "$FRONTEND_DIR/dist"
        
        # Check if nginx user can read the files
        NGINX_USER=$(ps aux | grep -E "nginx.*master" | grep -v grep | awk '{print $1}' | head -n 1)
        if [ -z "$NGINX_USER" ]; then
            NGINX_USER="www-data"  # Default nginx user on Ubuntu
        fi
        
        echo -e "\nChecking if nginx user ($NGINX_USER) can read the files..."
        if sudo -u $NGINX_USER test -r "$FRONTEND_DIR/dist/index.html" 2>/dev/null; then
            print_success "nginx user can read index.html"
        else
            print_error "nginx user cannot read index.html"
            
            # Fix permissions
            echo "Fixing permissions..."
            sudo chmod -R 755 "$FRONTEND_DIR/dist"
            sudo chown -R $NGINX_USER:$NGINX_USER "$FRONTEND_DIR/dist"
            print_success "Permissions fixed"
        fi
    else
        print_error "Frontend dist directory not found at $FRONTEND_DIR/dist"
    fi
    
    # Check favicon.ico
    echo -e "\nChecking favicon.ico permissions..."
    if [ -f "$FRONTEND_DIR/dist/favicon.ico" ]; then
        ls -la "$FRONTEND_DIR/dist/favicon.ico"
        
        # Fix favicon.ico permissions
        echo "Fixing favicon.ico permissions..."
        sudo chmod 644 "$FRONTEND_DIR/dist/favicon.ico"
        sudo chown $NGINX_USER:$NGINX_USER "$FRONTEND_DIR/dist/favicon.ico"
        print_success "favicon.ico permissions fixed"
    else
        print_warning "favicon.ico not found at $FRONTEND_DIR/dist/favicon.ico"
        
        # Create favicon.ico
        echo "Creating favicon.ico..."
        touch "$FRONTEND_DIR/dist/favicon.ico"
        sudo chmod 644 "$FRONTEND_DIR/dist/favicon.ico"
        sudo chown $NGINX_USER:$NGINX_USER "$FRONTEND_DIR/dist/favicon.ico"
        print_success "favicon.ico created and permissions set"
    fi
}

# Function to check nginx configuration
check_nginx_config() {
    print_header "Checking Nginx Configuration"
    
    # Check if nginx is installed
    if ! command -v nginx &> /dev/null; then
        print_error "nginx is not installed"
        return 1
    fi
    
    # Check if steelnet.conf exists
    if [ -f "/etc/nginx/conf.d/steelnet.conf" ]; then
        echo "steelnet.conf content:"
        cat "/etc/nginx/conf.d/steelnet.conf"
        
        # Check for common issues in the configuration
        if ! grep -q "try_files" "/etc/nginx/conf.d/steelnet.conf"; then
            print_warning "Missing try_files directive in nginx configuration"
            
            # Fix nginx configuration
            echo -e "\nFixing nginx configuration..."
            sudo cp "/etc/nginx/conf.d/steelnet.conf" "/etc/nginx/conf.d/steelnet.conf.bak"
            
            # Create a new configuration with try_files directive
            cat > /tmp/steelnet.conf << EOF
server {
    listen 80;
    listen [::]:80;
    server_name steelnet.ai;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://\$host\$request_uri;
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name steelnet.ai;
    
    # SSL configuration
    ssl_certificate $SCRIPT_DIR/ssl/cert.pem;
    ssl_certificate_key $SCRIPT_DIR/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    
    # SSL optimizations
    ssl_session_cache shared:SSL:2m;
    ssl_session_timeout 10m;
    
    # Root directory for static files
    root $FRONTEND_DIR/dist;
    index index.html;
    
    # Serve favicon.ico directly
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires max;
    }
    
    # Serve static files directly
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        access_log off;
        expires max;
        add_header Cache-Control "public, no-transform";
    }
    
    # Frontend application
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
            
            sudo cp /tmp/steelnet.conf "/etc/nginx/conf.d/steelnet.conf"
            
            # Test nginx configuration
            echo "Testing nginx configuration..."
            if sudo nginx -t; then
                print_success "nginx configuration is valid"
                
                # Restart nginx
                echo "Restarting nginx..."
                sudo systemctl restart nginx || sudo service nginx restart
                
                print_success "nginx configuration fixed and restarted"
            else
                print_error "nginx configuration is invalid"
                
                # Restore backup
                sudo cp "/etc/nginx/conf.d/steelnet.conf.bak" "/etc/nginx/conf.d/steelnet.conf"
                
                print_warning "Restored original configuration"
            fi
        fi
    else
        print_error "steelnet.conf not found at /etc/nginx/conf.d/steelnet.conf"
    fi
}

# Function to check backend status
check_backend_status() {
    print_header "Checking Backend Status"
    
    # Check if backend is running
    echo "Checking if backend is running..."
    if pgrep -f "gunicorn" > /dev/null; then
        print_success "Backend is running"
        
        # Check if backend is accessible
        echo "Checking if backend is accessible..."
        if curl -s http://localhost:8000/health > /dev/null; then
            print_success "Backend is accessible"
        else
            print_error "Backend is not accessible"
            
            # Restart backend
            echo "Restarting backend..."
            "$SCRIPT_DIR/start-prod.sh" --backend
            
            print_success "Backend restarted"
        fi
    else
        print_error "Backend is not running"
        
        # Start backend
        echo "Starting backend..."
        "$SCRIPT_DIR/start-prod.sh" --backend
        
        print_success "Backend started"
    fi
}

# Function to check frontend build
check_frontend_build() {
    print_header "Checking Frontend Build"
    
    # Check if frontend dist directory exists
    if [ -d "$FRONTEND_DIR/dist" ]; then
        echo "Frontend dist directory exists"
        
        # Check if index.html exists
        if [ -f "$FRONTEND_DIR/dist/index.html" ]; then
            print_success "index.html exists"
            
            # Check index.html content
            echo "Checking index.html content..."
            if grep -q "<script" "$FRONTEND_DIR/dist/index.html"; then
                print_success "index.html contains script tags"
            else
                print_warning "index.html does not contain script tags"
                
                # Rebuild frontend
                echo "Rebuilding frontend..."
                "$SCRIPT_DIR/start-prod.sh" --frontend
                
                print_success "Frontend rebuilt"
            fi
        else
            print_error "index.html not found"
            
            # Rebuild frontend
            echo "Rebuilding frontend..."
            "$SCRIPT_DIR/start-prod.sh" --frontend
            
            print_success "Frontend rebuilt"
        fi
    else
        print_error "Frontend dist directory not found"
        
        # Build frontend
        echo "Building frontend..."
        "$SCRIPT_DIR/start-prod.sh" --frontend
        
        print_success "Frontend built"
    fi
}

# Function to fix 500 error
fix_500_error() {
    print_header "Fixing 500 Internal Server Error"
    
    # Check logs
    check_nginx_logs
    check_backend_logs
    check_frontend_logs
    
    # Check file permissions
    check_file_permissions
    
    # Check nginx configuration
    check_nginx_config
    
    # Check backend status
    check_backend_status
    
    # Check frontend build
    check_frontend_build
    
    # Restart everything
    echo "Restarting all services..."
    "$SCRIPT_DIR/start-prod.sh" --restart
    
    print_success "All services restarted"
}

# Main function
main() {
    print_header "500 Internal Server Error Diagnosis and Fix"
    echo "This script will diagnose and fix 500 Internal Server Error issues"
    echo "Script directory: $SCRIPT_DIR"
    
    # Fix 500 error
    fix_500_error
    
    print_header "Summary"
    echo "The 500 Internal Server Error should now be fixed."
    echo "You should now be able to access your application at:"
    echo "  HTTPS: https://steelnet.ai"
    
    echo -e "\nIf you're still having issues, please check the logs:"
    echo "  nginx error log: sudo tail -f /var/log/nginx/error.log"
    echo "  nginx access log: sudo tail -f /var/log/nginx/access.log"
    echo "  Backend log: tail -f $LOG_DIR/backend.log"
    echo "  Frontend log: tail -f $LOG_DIR/frontend.log"
}

# Run the main function
main
