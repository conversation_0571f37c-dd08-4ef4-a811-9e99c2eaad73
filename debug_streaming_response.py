#!/usr/bin/env python3
"""
Debug the actual streaming response to see what the LLM is generating
"""
import requests
import json
import time

def debug_streaming_response():
    """Debug the actual streaming response to identify corruption source"""
    
    test_data = """S/S 430  BA  NO PI
 
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#"""

    url = "http://localhost:8000/api/llm/stream"
    payload = {
        "text": test_data,
        "function": "table",
        "unit_system": "metric"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    print("🔍 DEBUGGING STREAMING RESPONSE - RAW LLM OUTPUT")
    print("=" * 80)
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True)
        
        if response.status_code == 200:
            print("✅ Stream started, capturing raw output...")
            accumulated_data = ""
            chunk_count = 0
            
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    
                    if decoded_line.startswith('data: '):
                        try:
                            data_str = decoded_line[6:]
                            if data_str == '[DONE]':
                                print("\\n🏁 Stream completed")
                                break
                            
                            data = json.loads(data_str)
                            if 'content' in data:
                                chunk_content = data['content']
                                accumulated_data += chunk_content
                                chunk_count += 1
                                
                                # Show each chunk for debugging
                                if chunk_content.strip():
                                    print(f"CHUNK {chunk_count:3d}: '{chunk_content}'")
                                
                        except json.JSONDecodeError:
                            pass
            
            print("\\n" + "=" * 80)
            print("📄 COMPLETE RAW RESPONSE:")
            print("=" * 80)
            print(accumulated_data)
            print("=" * 80)
            
            # Parse table_stream content
            import re
            match = re.search(r'<table_stream>\s*([\s\S]*?)\s*</table_stream>', accumulated_data)
            if match:
                table_content = match.group(1).strip()
                print("\\n📊 EXTRACTED TABLE CONTENT:")
                print("=" * 80)
                print(repr(table_content))  # Use repr to show special characters
                print("=" * 80)
                
                # Analyze each line
                lines = table_content.split('\n')
                print(f"\\n🔍 LINE-BY-LINE ANALYSIS ({len(lines)} lines):")
                print("=" * 80)
                
                for i, line in enumerate(lines):
                    if line.strip():
                        pipe_count = line.count('|')
                        print(f"LINE {i:2d}: {pipe_count:2d} pipes | {repr(line)}")
                        
                        if pipe_count > 0:
                            cells = line.split('|')
                            print(f"        CELLS ({len(cells)}): {[repr(cell.strip()) for cell in cells]}")
                        print()
            else:
                print("❌ No table_stream content found")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"💥 Request failed: {e}")

if __name__ == "__main__":
    debug_streaming_response()