# Public IP Access Configuration

This guide explains how the Steel Unit Converter application is configured to be accessible via the public IP address `************`.

## Public IP Configuration

The application is configured to be accessible via the public IP address `************`. This means that users can access the application from anywhere on the internet using this IP address.

### Access URLs

When the application is running with nginx configured, it can be accessed at:

- **Frontend**: https://************
- **API**: https://************/api

HTTP access will automatically redirect to HTTPS for security.

## How It Works

The configuration works through several components:

1. **Nginx Configuration**:
   - Listens on all network interfaces (0.0.0.0)
   - Configured to accept connections on both IPv4 and IPv6
   - Explicitly includes the public IP in the server_name directive
   - Handles SSL termination and proxies requests to the backend and frontend

2. **Backend Server**:
   - Gunicorn is configured to bind to 0.0.0.0:8000
   - This allows it to accept connections from any interface
   - Nginx proxies API requests to this server

3. **Frontend Server**:
   - The serve command is configured to listen on 0.0.0.0:3000
   - This allows it to accept connections from any interface
   - Nginx proxies frontend requests to this server

## Security Considerations

When exposing your application to the internet via a public IP, consider the following security measures:

1. **Firewall Configuration**:
   - Only ports 80 (HTTP) and 443 (HTTPS) should be exposed to the internet
   - All other ports should be blocked by the firewall
   - The backend (8000) and frontend (3000) ports should NOT be directly accessible from the internet

2. **SSL/TLS**:
   - The application uses self-signed SSL certificates by default
   - For production use, consider obtaining proper SSL certificates from a trusted certificate authority
   - Let's Encrypt provides free SSL certificates that are trusted by most browsers

3. **Authentication**:
   - Ensure that all sensitive endpoints require proper authentication
   - Consider implementing rate limiting to prevent brute force attacks
   - Monitor access logs for suspicious activity

## Domain Name Configuration

If you want to use a domain name instead of the IP address, you need to:

1. Register a domain name with a domain registrar
2. Configure DNS records to point to the public IP address:
   - Create an A record for your domain (e.g., steelnet.ai) pointing to ************
   - Create an A record for the www subdomain (e.g., www.steelnet.ai) pointing to ************

3. Update the nginx configuration to include your domain name:
   - The current configuration already includes steelnet.ai in the server_name directive
   - If you use a different domain name, update the server_name directive accordingly

## Troubleshooting

If you cannot access the application via the public IP:

1. **Check if nginx is running**:
   ```bash
   sudo systemctl status nginx
   ```

2. **Check nginx configuration**:
   ```bash
   sudo nginx -t
   ```

3. **Check if the application is running**:
   ```bash
   ./start-prod.sh --status
   ```

4. **Check firewall settings**:
   ```bash
   sudo ufw status
   # or
   sudo iptables -L
   ```

5. **Check if the ports are open**:
   ```bash
   sudo netstat -tulpn | grep -E '80|443|8000|3000'
   ```

6. **Check nginx logs**:
   ```bash
   sudo tail -f /var/log/nginx/error.log
   sudo tail -f /var/log/nginx/access.log
   ```

7. **Check application logs**:
   ```bash
   ./start-prod.sh --logs all
   ```

## Updating the Public IP

If the public IP address changes, you need to update it in the `start-prod.sh` script:

1. Find the line that sets the PUBLIC_IP variable:
   ```bash
   PUBLIC_IP="************"
   ```

2. Update it with the new IP address:
   ```bash
   PUBLIC_IP="your.new.ip.address"
   ```

3. Restart the application:
   ```bash
   ./start-prod.sh --restart --url-access
   ```
