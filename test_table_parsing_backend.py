#!/usr/bin/env python3
"""
Test script to verify table parsing fixes work with the backend
"""

import requests
import json
import time
import os

def test_table_parsing():
    """Test the table parsing functionality with the backend"""
    
    # Test data that should generate a table
    test_input = """
    Please convert the following steel specifications from imperial to metric and create a table:

    001 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.343(+/-0.005)" x COIL - 7190#
    002 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 2.406(+/-0.005)" x COIL - 8061#
    003 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 16.50(+/-0.005)" x COIL - 12550#
    004 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 19.68(+/-0.03125)" x COIL - 8835#
    005 - S/S 430 BA NO PI - 0.015(+/-0.0015)" x 47.438(+/-0.03125)" x COIL - 57655#
    """
    
    # API endpoint
    url = "http://localhost:8000/api/llm"
    
    # Request payload
    payload = {
        "text": test_input,
        "unit_system": "metric",
        "function": "table"
    }
    
    print("🧪 Testing Table Parsing with Backend")
    print("=" * 50)
    print(f"Input: {test_input[:100]}...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print()
    
    try:
        # Make the request
        print("📡 Sending request to backend...")
        # Clear proxy environment variables for this session
        old_http_proxy = os.environ.get('http_proxy')
        old_https_proxy = os.environ.get('https_proxy')
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']

        # Create a new session without proxy
        session = requests.Session()
        session.trust_env = False  # Don't use environment proxy settings

        response = session.post(url, json=payload, timeout=30)

        # Restore proxy settings
        if old_http_proxy:
            os.environ['http_proxy'] = old_http_proxy
        if old_https_proxy:
            os.environ['https_proxy'] = old_https_proxy
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print()
            
            # Print the response structure
            print("📋 Response Structure:")
            for key, value in result.items():
                if isinstance(value, str) and len(value) > 100:
                    print(f"  {key}: {value[:100]}...")
                else:
                    print(f"  {key}: {value}")
            print()
            
            # Check for table content
            table_content = None
            if 'result' in result and 'converted_text' in result['result']:
                table_content = result['result']['converted_text']
                print("📄 Table Content from result.converted_text:")
                print("-" * 30)
                print(table_content)
                print("-" * 30)
                print()
            elif 'message' in result:
                table_content = result['message']
                print("📄 Table Content from message:")
                print("-" * 30)
                print(table_content)
                print("-" * 30)
                print()

            if table_content:
                # Check for table_stream format
                if '<table_stream>' in table_content:
                    print("✅ Found table_stream format!")

                    # Extract table content
                    start = table_content.find('<table_stream>') + len('<table_stream>')
                    end = table_content.find('</table_stream>')
                    if end != -1:
                        table_data = table_content[start:end].strip()
                        print("📊 Extracted Table Content:")
                        print(table_data)
                        print()

                        # Parse the table content
                        lines = table_data.split('\n')
                        if lines:
                            print("🔍 Table Analysis:")
                            print(f"  Total lines: {len(lines)}")
                            
                            if len(lines) > 0:
                                header_line = lines[0]
                                headers = header_line.split('|')
                                print(f"  Headers ({len(headers)}): {headers}")
                                
                                if len(lines) > 1:
                                    print("  Data rows:")
                                    for i, line in enumerate(lines[1:], 1):
                                        cells = line.split('|')
                                        print(f"    Row {i} ({len(cells)} cells): {cells}")
                                        
                                        # Check for column alignment
                                        if len(cells) != len(headers):
                                            print(f"    ⚠️  Column mismatch! Expected {len(headers)}, got {len(cells)}")
                                        else:
                                            print(f"    ✅ Column count matches!")
                    else:
                        print("❌ table_stream tag not properly closed")
                else:
                    print("❌ No table_stream format found in response")

                    # Check for markdown table format
                    if '|' in table_content and '---' in table_content:
                        print("📋 Found markdown table format")
                    else:
                        print("❌ No recognizable table format found")
            else:
                print("❌ No table content found in response")
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Backend server not running")
        print("💡 Please start the backend server first:")
        print("   cd backend && python3 main.py")
        
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def test_health_endpoint():
    """Test if the backend is running"""
    try:
        # Clear proxy environment variables for this session
        old_http_proxy = os.environ.get('http_proxy')
        old_https_proxy = os.environ.get('https_proxy')
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']

        # Create a new session without proxy
        session = requests.Session()
        session.trust_env = False  # Don't use environment proxy settings

        response = session.get("http://localhost:8000/health", timeout=5)

        # Restore proxy settings
        if old_http_proxy:
            os.environ['http_proxy'] = old_http_proxy
        if old_https_proxy:
            os.environ['https_proxy'] = old_https_proxy

        if response.status_code == 200:
            print("✅ Backend is running")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            print(f"Response text: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Backend is not running: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Table Parsing Backend Test")
    print("=" * 50)
    
    # Check if backend is running
    if test_health_endpoint():
        print()
        test_table_parsing()
    else:
        print()
        print("💡 Please start the backend server first:")
        print("   ./start_dev_linux_with_url_access.sh")
        print("   or")
        print("   cd backend && python3 main.py")
