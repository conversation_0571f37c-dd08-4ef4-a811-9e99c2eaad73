param (
    [string]$Mode = "dev" # Default to development mode
)

$BackendPath = Join-Path $PSScriptRoot "backend"
$FrontendPath = Join-Path $PSScriptRoot "frontend"

# Function to check if a command exists
function Test-CommandExists {
    param ($command)
    
    $oldPreference = $ErrorActionPreference
    $ErrorActionPreference = 'stop'
    
    try {
        if (Get-Command $command) { return $true }
    }
    catch { return $false }
    finally { $ErrorActionPreference = $oldPreference }
}

# Check if Python is installed
if (-not (Test-CommandExists python)) {
    Write-Error "Python is not installed. Please install Python 3.8 or higher."
    exit 1
}

# Check if Node.js is installed
if (-not (Test-CommandExists node)) {
    Write-Error "Node.js is not installed. Please install Node.js 14 or higher."
    exit 1
}

# Check if npm is installed
if (-not (Test-CommandExists npm)) {
    Write-Error "npm is not installed. Please install npm."
    exit 1
}

function Start-Backend {
    param (
        [string]$Mode = "dev"
    )
    
    Write-Host "Starting backend in $Mode mode..."
    
    # Change directory to backend
    Set-Location $BackendPath
    
    # Create virtual environment if it doesn't exist
    if (-not (Test-Path (Join-Path $BackendPath "venv"))) {
        Write-Host "Creating virtual environment..."
        python -m venv venv
    }
    
    # Activate virtual environment
    & (Join-Path $BackendPath "venv\Scripts\Activate.ps1")
    
    # Install dependencies if requirements.txt exists
    if (Test-Path (Join-Path $BackendPath "requirements.txt")) {
        Write-Host "Installing backend dependencies..."
        pip install -r requirements.txt
    }
    
    # Set environment variables
    if ($Mode -eq "prod") {
        $env:ENV = "production"
        $env:DEBUG = "False"
    } else {
        $env:ENV = "development"
        $env:DEBUG = "True"
    }
    
    # Start backend server
    if ($Mode -eq "prod") {
        Write-Host "Starting backend in production mode..."
        uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
    } else {
        Write-Host "Starting backend in development mode..."
        uvicorn main:app --reload --host 0.0.0.0 --port 8000
    }
}

function Start-Frontend {
    param (
        [string]$Mode = "dev"
    )
    
    Write-Host "Starting frontend in $Mode mode..."
    
    # Change directory to frontend
    Set-Location $FrontendPath
    
    # Install dependencies if needed
    if (Test-Path (Join-Path $FrontendPath "package.json")) {
        Write-Host "Installing frontend dependencies..."
        npm install
    }
    
    # Start frontend
    if ($Mode -eq "prod") {
        Write-Host "Building and serving frontend in production mode..."
        npm run build
        npm run preview
    } else {
        Write-Host "Starting frontend in development mode..."
        npm run dev
    }
}

# Start both services
if ($Mode -eq "backend") {
    Start-Backend -Mode "dev"
} elseif ($Mode -eq "frontend") {
    Start-Frontend -Mode "dev"
} elseif ($Mode -eq "prod") {
    Start-Job -ScriptBlock { 
        Set-Location $using:BackendPath
        & (Join-Path $using:PSScriptRoot "start.ps1") -Mode "backend-prod" 
    }
    Start-Frontend -Mode "prod"
} elseif ($Mode -eq "backend-prod") {
    Start-Backend -Mode "prod"
} elseif ($Mode -eq "frontend-prod") {
    Start-Frontend -Mode "prod"
} else {
    # Start both in development mode
    Start-Job -ScriptBlock { 
        Set-Location $using:BackendPath
        & (Join-Path $using:PSScriptRoot "start.ps1") -Mode "backend" 
    }
    Start-Frontend -Mode "dev"
}

# Keep the script running
if ($Mode -eq "dev" -or $Mode -eq "prod") {
    try {
        Write-Host "Services started. Press Ctrl+C to stop."
        while ($true) {
            Start-Sleep -Seconds 1
        }
    } finally {
        Get-Job | Stop-Job
        Get-Job | Remove-Job
        Write-Host "All services stopped."
    }
}
