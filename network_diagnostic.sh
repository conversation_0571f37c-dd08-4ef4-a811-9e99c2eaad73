#!/bin/bash

echo "=== Network Diagnostic Tool for SMTP Issues ==="
echo "Timestamp: $(date)"
echo "======================================="

# Check if we're running as root
if [[ $EUID -eq 0 ]]; then
   echo "Running as root - full diagnostic available"
else
   echo "Running as regular user - some checks may be limited"
fi

echo
echo "=== System Information ==="
echo "Hostname: $(hostname)"
echo "OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "Kernel: $(uname -r)"
echo "Architecture: $(uname -m)"

if [ -f /.dockerenv ]; then
    echo "Environment: Docker Container"
else
    echo "Environment: Host System"
fi

echo
echo "=== Network Interface Information ==="
ip addr show | grep -E "inet |UP|DOWN"

echo
echo "=== DNS Configuration ==="
echo "DNS Servers:"
cat /etc/resolv.conf | grep nameserver

echo
echo "=== Testing DNS Resolution ==="
for host in google.com smtp.gmail.com smtp.qq.com smtp.163.com; do
    if nslookup $host >/dev/null 2>&1; then
        echo "✓ DNS resolution for $host: $(nslookup $host | awk '/^Address: / { print $2 }' | tail -1)"
    else
        echo "✗ DNS resolution failed for $host"
    fi
done

echo
echo "=== Testing Basic Connectivity ==="
for host_port in "google.com:443" "smtp.gmail.com:587" "smtp.gmail.com:465" "smtp.qq.com:587" "smtp.qq.com:465"; do
    host=$(echo $host_port | cut -d: -f1)
    port=$(echo $host_port | cut -d: -f2)
    
    if timeout 5 bash -c "</dev/tcp/$host/$port" 2>/dev/null; then
        echo "✓ Connection to $host:$port successful"
    else
        echo "✗ Connection to $host:$port failed"
    fi
done

echo
echo "=== Checking Firewall Status ==="
if command -v ufw >/dev/null 2>&1; then
    echo "UFW Status:"
    ufw status
elif command -v iptables >/dev/null 2>&1; then
    echo "IPTables rules (outbound):"
    iptables -L OUTPUT -n | head -10
else
    echo "No common firewall tools found"
fi

echo
echo "=== Testing Outbound Ports ==="
for port in 25 465 587 2525; do
    echo -n "Testing port $port: "
    if timeout 3 bash -c "</dev/tcp/smtp.gmail.com/$port" 2>/dev/null; then
        echo "✓ Open"
    else
        echo "✗ Blocked/Filtered"
    fi
done

echo
echo "=== Checking for Proxy/NAT ==="
external_ip=$(curl -s --max-time 5 ifconfig.me 2>/dev/null || curl -s --max-time 5 ipinfo.io/ip 2>/dev/null || echo "Unable to detect")
echo "External IP: $external_ip"

local_ip=$(ip route get ******* | awk '{print $7; exit}' 2>/dev/null || echo "Unable to detect")
echo "Local IP: $local_ip"

echo
echo "=== Testing SMTP Servers Directly ==="
test_smtp() {
    local server=$1
    local port=$2
    local use_ssl=$3
    
    echo "Testing $server:$port..."
    
    if [ "$use_ssl" = "ssl" ]; then
        response=$(timeout 10 openssl s_client -connect $server:$port -quiet 2>/dev/null | head -1)
    else
        response=$(timeout 10 telnet $server $port 2>/dev/null | head -2 | tail -1)
    fi
    
    if [[ $response == *"220"* ]]; then
        echo "✓ SMTP server $server:$port responded: $response"
    else
        echo "✗ SMTP server $server:$port did not respond properly"
    fi
}

# Test common SMTP servers
test_smtp "smtp.gmail.com" "587" "starttls"
test_smtp "smtp.gmail.com" "465" "ssl"

echo
echo "=== Environment Variables Check ==="
if [ -f .env ]; then
    echo "Found .env file"
    if grep -q "SMTP_SERVER" .env; then
        smtp_server=$(grep "SMTP_SERVER" .env | cut -d'=' -f2 | tr -d '"')
        smtp_port=$(grep "SMTP_PORT" .env | cut -d'=' -f2 | tr -d '"')
        echo "Configured SMTP server: $smtp_server:$smtp_port"
        
        # Test the configured server
        if [ ! -z "$smtp_server" ] && [ ! -z "$smtp_port" ]; then
            echo "Testing configured SMTP server..."
            if timeout 5 bash -c "</dev/tcp/$smtp_server/$smtp_port" 2>/dev/null; then
                echo "✓ Connection to configured server successful"
            else
                echo "✗ Connection to configured server failed"
            fi
        fi
    else
        echo "No SMTP configuration found in .env"
    fi
else
    echo "No .env file found"
fi

echo
echo "=== Process Information ==="
echo "Python processes:"
ps aux | grep python | grep -v grep | head -5

echo
echo "=== Recent Network Errors ==="
if [ -f /var/log/syslog ]; then
    echo "Recent network-related errors from syslog:"
    tail -100 /var/log/syslog | grep -i "network\|dns\|connection" | tail -5
elif [ -f /var/log/messages ]; then
    echo "Recent network-related errors from messages:"
    tail -100 /var/log/messages | grep -i "network\|dns\|connection" | tail -5
else
    echo "No standard log files accessible"
fi

echo
echo "=== Recommendations ==="
echo "1. If DNS resolution fails:"
echo "   - Check /etc/resolv.conf"
echo "   - Try using public DNS servers (*******, *******)"
echo ""
echo "2. If ports are blocked:"
echo "   - Check cloud provider security groups"
echo "   - Check local firewall (ufw/iptables)"
echo "   - Contact hosting provider about SMTP restrictions"
echo ""
echo "3. If running in container:"
echo "   - Check container network configuration"
echo "   - Ensure container has internet access"
echo ""
echo "4. Alternative solutions:"
echo "   - Use external email service (SendGrid, Mailgun)"
echo "   - Use different SMTP provider"
echo "   - Try different ports (587 vs 465 vs 25)"
echo ""
echo "======================================="
echo "Diagnostic complete. Run this script with: bash network_diagnostic.sh"
echo "For SMTP-specific debugging, run: python3 debug_smtp.py" 