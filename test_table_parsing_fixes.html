<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Parsing Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .test-output {
            background: #e8f5e8;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .error {
            background: #ffe8e8;
            color: #d00;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .debug-info {
            background: #f0f8ff;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Table Parsing Fixes Test</h1>
    <p>This test verifies that the table parsing fixes correctly handle column alignment and delimiter detection.</p>

    <div class="test-section">
        <h2>Test 1: Pipe-delimited with Outer Pipes</h2>
        <div class="test-input" id="test1-input">|Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks|
|001|S/S 430 BA NO PI|0.015" x 2.343" x COIL|7190#|0.38mm x 59.51mm x COIL|3261.2 kg|BA Finish|
|002|S/S 430 BA NO PI|0.015" x 2.406" x COIL|8061#|0.38mm x 61.11mm x COIL|3656.7 kg|BA Finish|</div>
        <div class="test-output" id="test1-output"></div>
        <div class="debug-info" id="test1-debug"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Pipe-delimited without Outer Pipes</h2>
        <div class="test-input" id="test2-input">Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015" x 2.343" x COIL|7190#|0.38mm x 59.51mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015" x 2.406" x COIL|8061#|0.38mm x 61.11mm x COIL|3656.7 kg|BA Finish</div>
        <div class="test-output" id="test2-output"></div>
        <div class="debug-info" id="test2-debug"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Tab-delimited Format</h2>
        <div class="test-input" id="test3-input">Item Code	Description	Size (Original)	Customer QTY	Size (Converted)	Converted QTY	Remarks
001	S/S 430 BA NO PI	0.015" x 2.343" x COIL	7190#	0.38mm x 59.51mm x COIL	3261.2 kg	BA Finish
002	S/S 430 BA NO PI	0.015" x 2.406" x COIL	8061#	0.38mm x 61.11mm x COIL	3656.7 kg	BA Finish</div>
        <div class="test-output" id="test3-output"></div>
        <div class="debug-info" id="test3-debug"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: Streaming Table Format</h2>
        <div class="test-input" id="test4-input"><table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015" x 2.343" x COIL|7190#|0.38mm x 59.51mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015" x 2.406" x COIL|8061#|0.38mm x 61.11mm x COIL|3656.7 kg|BA Finish
</table_stream></div>
        <div class="test-output" id="test4-output"></div>
        <div class="debug-info" id="test4-debug"></div>
    </div>

    <script>
        // Simulate the table parsing functions from the frontend
        function parseMarkdownTable(markdownTable) {
            try {
                if (!markdownTable || typeof markdownTable !== 'string') {
                    return { headers: [], rows: [], isValid: false, error: 'Empty or invalid table content' };
                }

                const lines = markdownTable.trim().split('\n').filter(line => line.trim().length > 0);

                if (lines.length < 1) {
                    return { headers: [], rows: [], isValid: false, error: 'No table content found' };
                }

                // Enhanced header parsing with better detection
                const headerLine = lines[0].trim();
                let headers = [];

                if (headerLine.includes('|')) {
                    // Remove outer pipes if they exist and split
                    const cleanHeader = headerLine.replace(/^\||\|$/g, '');
                    headers = cleanHeader.split('|').map(h => h.trim());
                    console.log('Parsed pipe-delimited headers:', {
                        originalLine: headerLine,
                        cleanedLine: cleanHeader,
                        headers: headers,
                        headerCount: headers.length
                    });
                } else if (headerLine.includes('\t')) {
                    headers = headerLine.split('\t').map(h => h.trim());
                    console.log('Parsed tab-delimited headers:', headers);
                } else if (headerLine.match(/\s{2,}/)) {
                    headers = headerLine.split(/\s{2,}/).map(h => h.trim());
                    console.log('Parsed space-delimited headers:', headers);
                } else {
                    headers = headerLine.split(/[,\s]+/).map(h => h.trim()).filter(h => h.length > 0);
                }

                if (headers.length === 0) {
                    return { headers: [], rows: [], isValid: false, error: 'No valid headers found' };
                }

                // Find the separator line (might not be exactly line 1)
                let separatorIndex = -1;
                for (let i = 1; i < Math.min(3, lines.length); i++) {
                    const line = lines[i].trim();
                    if (line.match(/^[\|\s]*[-:]+[\|\s-:]*$/)) {
                        separatorIndex = i;
                        break;
                    }
                }

                let startRowIndex = separatorIndex !== -1 ? separatorIndex + 1 : 1;

                // Enhanced row parsing
                const rows = [];
                for (let i = startRowIndex; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line || line.startsWith('<!--') || line.match(/^[\|\s-:]*$/)) continue;

                    let cells = [];

                    if (line.includes('|')) {
                        const cleanLine = line.replace(/^\||\|$/g, '');
                        cells = cleanLine.split('|').map(cell => cell.trim());
                        console.log(`Parsed pipe-delimited cells for line ${i}:`, {
                            originalLine: line,
                            cleanedLine: cleanLine,
                            cells: cells,
                            cellCount: cells.length
                        });
                    } else if (line.includes('\t')) {
                        cells = line.split('\t').map(cell => cell.trim());
                        console.log(`Parsed tab-delimited cells for line ${i}:`, cells);
                    } else if (line.match(/\s{2,}/)) {
                        cells = line.split(/\s{2,}/).map(cell => cell.trim());
                        console.log(`Parsed space-delimited cells for line ${i}:`, cells);
                    } else {
                        cells = line.split(/[,\s]+/).map(cell => cell.trim()).filter(cell => cell.length > 0);
                    }

                    if (cells.some(cell => cell.length > 0)) {
                        const row = {};
                        headers.forEach((header, index) => {
                            row[header] = cells[index] || '';
                        });
                        rows.push(row);
                    }
                }

                const isValid = headers.length > 0 && rows.length > 0;
                return { 
                    headers, 
                    rows, 
                    isValid,
                    error: isValid ? undefined : 'No valid table data found'
                };

            } catch (error) {
                console.error('Error parsing markdown table:', error);
                return { 
                    headers: [], 
                    rows: [], 
                    isValid: false, 
                    error: `Parsing error: ${error.message}`
                };
            }
        }

        function parseStreamingTable(content) {
            // Extract content from table_stream tags
            const streamMatch = content.match(/<table_stream>([\s\S]*?)<\/table_stream>/);
            if (streamMatch) {
                return parseMarkdownTable(streamMatch[1].trim());
            }
            return parseMarkdownTable(content);
        }

        function renderTable(parsedTable) {
            if (!parsedTable.isValid) {
                return `<div class="error">Error: ${parsedTable.error}</div>`;
            }

            let html = '<table><thead><tr>';
            parsedTable.headers.forEach(header => {
                html += `<th>${header}</th>`;
            });
            html += '</tr></thead><tbody>';

            parsedTable.rows.forEach(row => {
                html += '<tr>';
                parsedTable.headers.forEach(header => {
                    html += `<td>${row[header] || ''}</td>`;
                });
                html += '</tr>';
            });

            html += '</tbody></table>';
            return html;
        }

        function runTest(testId, parseFunction) {
            const input = document.getElementById(`${testId}-input`).textContent;
            const outputDiv = document.getElementById(`${testId}-output`);
            const debugDiv = document.getElementById(`${testId}-debug`);

            console.log(`\n=== Running ${testId} ===`);
            console.log('Input:', input);

            const parsedTable = parseFunction(input);
            console.log('Parsed result:', parsedTable);

            outputDiv.innerHTML = renderTable(parsedTable);
            
            debugDiv.innerHTML = `
                <strong>Debug Info:</strong><br>
                Headers: ${JSON.stringify(parsedTable.headers)}<br>
                Header Count: ${parsedTable.headers.length}<br>
                Row Count: ${parsedTable.rows.length}<br>
                Is Valid: ${parsedTable.isValid}<br>
                Error: ${parsedTable.error || 'None'}<br>
                First Row: ${JSON.stringify(parsedTable.rows[0] || {})}
            `;
        }

        // Run all tests
        window.onload = function() {
            runTest('test1', parseMarkdownTable);
            runTest('test2', parseMarkdownTable);
            runTest('test3', parseMarkdownTable);
            runTest('test4', parseStreamingTable);
        };
    </script>
</body>
</html>
