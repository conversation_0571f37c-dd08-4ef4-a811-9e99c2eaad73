<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Streaming Simulation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .warning {
            color: orange;
            font-weight: bold;
        }
        .milestone {
            color: blue;
            font-weight: bold;
        }
        .streaming-indicator {
            background: linear-gradient(90deg, #4CAF50, #45a049);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Frontend Streaming Simulation</h1>
        <p>Testing the fixed streaming table display logic</p>

        <div class="test-section">
            <h3>📊 Streaming Table Display Test <span id="streaming-status"></span></h3>
            <div id="test-log" class="log"></div>
            <div id="table-container"></div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : type === 'milestone' ? 'milestone' : '';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function updateStreamingStatus(status) {
            const statusElement = document.getElementById('streaming-status');
            statusElement.innerHTML = `<span class="streaming-indicator">${status}</span>`;
        }

        // Enhanced StreamingTableParser with the fixes
        class StreamingTableParser {
            constructor() {
                this.headers = [];
                this.rows = [];
                this.isHeaderParsed = false;
                this.buffer = '';
                this.expectedHeaders = ['Item Code', 'Description', 'Size (Original)', 'Customer QTY', 'Size (Converted)', 'Converted QTY', 'Remarks'];
            }

            addChunk(accumulatedContent) {
                console.log('🔄 StreamingTableParser.addChunk ENTRY:', {
                    accumulatedLength: accumulatedContent.length,
                    hasTableStreamStart: accumulatedContent.includes('<table_stream>'),
                    hasTableStreamEnd: accumulatedContent.includes('</table_stream>')
                });

                // Use accumulated content directly
                this.buffer = accumulatedContent;

                // Look for table_stream tags
                const streamMatch = this.buffer.match(/<table_stream>([\s\S]*?)(?:<\/table_stream>|$)/);
                if (streamMatch) {
                    const tableContent = streamMatch[1];
                    const isComplete = this.buffer.includes('</table_stream>');

                    console.log('Found table content in buffer:', {
                        tableContentLength: tableContent.length,
                        hasNewlines: tableContent.includes('\n'),
                        lineCount: tableContent.split('\n').length,
                        isComplete: isComplete
                    });

                    // Parse progressively - allow partial tables for streaming display
                    const result = this.parseStreamingContent(tableContent, isComplete);

                    if (isComplete) {
                        console.log('✅ Complete table found and parsed');
                    } else {
                        console.log('🔄 Partial table parsed for streaming display');
                    }

                    return result;
                }

                console.log('No table_stream match yet, returning invalid table');
                return { headers: this.headers, rows: this.rows, isValid: false, error: 'Waiting for table_stream content' };
            }

            parseStreamingContent(content, isComplete = false) {
                console.log('🔄 parseStreamingContent called:', {
                    contentLength: content.length,
                    hasNewlines: content.includes('\n'),
                    isComplete: isComplete
                });

                const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0);
                console.log('📋 Split into lines:', {
                    totalLines: lines.length,
                    lines: lines
                });

                // Parse headers
                if (!this.isHeaderParsed && lines.length > 0) {
                    const headerLine = lines[0];
                    const headers = headerLine.split('|').map(h => h.trim()).filter(h => h.length > 0);

                    if (headers.length >= 6) { // Allow 6 or 7 headers
                        console.log('✅ Header line found, setting headers');
                        this.headers = headers;
                        this.isHeaderParsed = true;
                    } else {
                        console.log(`⏳ Header line incomplete: ${headers.length} columns`);
                        return {
                            headers: this.expectedHeaders,
                            rows: [],
                            isValid: false,
                            error: `Waiting for complete header line (got ${headers.length} columns)`,
                            isComplete: false
                        };
                    }
                }

                // Parse data rows
                if (this.isHeaderParsed) {
                    this.rows = [];
                    const dataLines = lines.slice(1); // Skip header line

                    for (let i = 0; i < dataLines.length; i++) {
                        const line = dataLines[i];
                        const cells = line.split('|').map(c => c.trim());

                        // Create row object, padding with empty strings if needed
                        const row = {};
                        this.headers.forEach((header, index) => {
                            row[header] = cells[index] || '';
                        });
                        this.rows.push(row);
                        console.log(`✅ Row ${i + 1} parsed: ${cells.length} cells`);
                    }
                }

                // For streaming display: valid if we have headers (even if table isn't complete)
                // For final validation: valid only if complete table with correct structure
                const hasValidHeaders = this.headers.length >= 6;
                const hasRows = this.rows.length > 0;

                let isValid, error;

                if (isComplete) {
                    // Final validation: require complete table
                    isValid = hasValidHeaders && hasRows;
                    error = isValid ? undefined : `Complete table validation failed: ${this.headers.length} headers, ${this.rows.length} rows`;
                } else {
                    // Streaming validation: allow display if we have headers (progressive display)
                    isValid = hasValidHeaders;
                    error = isValid ? undefined : `Waiting for headers: ${this.headers.length} headers`;
                }

                const result = {
                    headers: this.headers,
                    rows: this.rows,
                    isValid,
                    error,
                    isComplete
                };

                console.log('🎉 Streaming table parse result:', {
                    isValid: result.isValid,
                    isComplete: result.isComplete,
                    headerCount: result.headers.length,
                    rowCount: result.rows.length,
                    streamingMode: !isComplete ? 'PROGRESSIVE_DISPLAY' : 'FINAL_COMPLETE'
                });

                return result;
            }

            reset() {
                this.headers = [];
                this.rows = [];
                this.isHeaderParsed = false;
                this.buffer = '';
            }
        }

        function renderTable(tableData, isStreaming = false) {
            const container = document.getElementById('table-container');
            
            if (!tableData.isValid || tableData.headers.length === 0) {
                container.innerHTML = `<p>⏳ Waiting for table data... ${tableData.error || ''}</p>`;
                return;
            }

            let html = '<table>';
            
            // Headers
            html += '<thead><tr>';
            tableData.headers.forEach(header => {
                html += `<th>${escapeHtml(header || 'Column')}</th>`;
            });
            html += '</tr></thead>';
            
            // Data rows
            html += '<tbody>';
            if (tableData.rows.length === 0 && isStreaming) {
                // Show placeholder row while streaming
                html += '<tr>';
                tableData.headers.forEach(() => {
                    html += '<td style="color: #999; font-style: italic;">Loading...</td>';
                });
                html += '</tr>';
            } else {
                tableData.rows.forEach((row, index) => {
                    html += '<tr>';
                    tableData.headers.forEach(header => {
                        const cellContent = row[header] || '';
                        html += `<td>${escapeHtml(cellContent)}</td>`;
                    });
                    html += '</tr>';
                });
            }
            html += '</tbody></table>';

            // Add streaming indicator
            if (isStreaming && !tableData.isComplete) {
                html += `<p style="color: #666; font-style: italic; margin-top: 10px;">
                    📡 Streaming... ${tableData.rows.length} rows loaded
                </p>`;
            } else if (tableData.isComplete) {
                html += `<p style="color: #4CAF50; font-weight: bold; margin-top: 10px;">
                    ✅ Table complete - ${tableData.rows.length} rows
                </p>`;
            }

            container.innerHTML = html;
        }

        // Simulate streaming with real backend data
        async function simulateStreaming() {
            log('🚀 Starting streaming simulation...', 'milestone');
            updateStreamingStatus('CONNECTING');

            const parser = new StreamingTableParser();
            let chunkCount = 0;

            // Simulate progressive streaming chunks (based on real backend data)
            const streamingChunks = [
                // Headers appear
                '<table_stream>\nItem Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks',
                // First row appears
                '<table_stream>\nItem Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks\n001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish',
                // Second row appears
                '<table_stream>\nItem Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks\n001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish\n002|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.406(+/-0.005)in x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish',
                // Third row appears
                '<table_stream>\nItem Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks\n001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish\n002|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.406(+/-0.005)in x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish\n003|S/S 430 BA NO PI|0.015(+/-0.0015)in x 16.50(+/-0.005)in x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish',
                // Table completes
                '<table_stream>\nItem Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks\n001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish\n002|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.406(+/-0.005)in x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish\n003|S/S 430 BA NO PI|0.015(+/-0.0015)in x 16.50(+/-0.005)in x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish\n</table_stream>'
            ];

            for (let i = 0; i < streamingChunks.length; i++) {
                await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate delay

                chunkCount++;
                const chunk = streamingChunks[i];
                
                log(`📦 Processing chunk ${chunkCount}/${streamingChunks.length}`, 'info');
                
                // Parse the chunk
                const tableData = parser.addChunk(chunk);
                
                // Update status
                if (i === 0) {
                    updateStreamingStatus('HEADERS LOADED');
                    log('📋 Headers detected and displayed', 'milestone');
                } else if (i === 1) {
                    updateStreamingStatus('FIRST ROW LOADED');
                    log('📊 First row displayed', 'milestone');
                } else if (i < streamingChunks.length - 1) {
                    updateStreamingStatus(`${tableData.rows.length} ROWS LOADED`);
                    log(`📈 ${tableData.rows.length} rows displayed`, 'milestone');
                } else {
                    updateStreamingStatus('COMPLETE');
                    log('✅ Table streaming complete', 'success');
                }
                
                // Render the table
                const isStreaming = i < streamingChunks.length - 1;
                renderTable(tableData, isStreaming);
                
                log(`Table state: ${tableData.isValid ? 'VALID' : 'INVALID'}, Headers: ${tableData.headers.length}, Rows: ${tableData.rows.length}, Complete: ${tableData.isComplete || false}`, 'info');
            }

            log('🎉 Streaming simulation complete!', 'success');
        }

        // Start the simulation when page loads
        window.onload = function() {
            simulateStreaming();
        };
    </script>
</body>
</html>
