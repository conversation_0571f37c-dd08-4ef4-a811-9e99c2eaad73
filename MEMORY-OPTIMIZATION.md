# Memory Optimization Guide

This guide explains how the Steel Unit Converter application is optimized for low memory environments, particularly for systems with 2GB of RAM.

## Automatic Memory Optimization

The application automatically detects low memory systems (less than 2.5GB of RAM) and applies optimizations. You can also explicitly enable low memory mode with the `--low-memory` flag:

```bash
./start-prod.sh --restart --low-memory
```

## Memory Optimization Features

### Backend Optimizations

1. **Dynamic Worker Calculation**:
   - Automatically calculates the optimal number of Gunicorn workers based on available memory
   - Uses a formula that reserves at least 512MB for the system and other processes
   - Limits workers to a maximum of 2 for 2GB systems
   - Uses more threads instead of workers to save memory

2. **Gunicorn Optimizations**:
   - Uses `--worker-tmp-dir /dev/shm` to store temporary files in memory
   - Uses `--preload` to share application code between workers
   - Reduces `--max-requests` to 500 to prevent memory leaks
   - Uses 4 threads per worker to handle concurrent requests efficiently

### Frontend Optimizations

1. **Node.js Memory Limits**:
   - Sets `--max-old-space-size=256` to limit Node.js heap size
   - Uses `--gc-interval=100` to trigger garbage collection more frequently
   - Applies these limits to both build and runtime processes

2. **Build Optimizations**:
   - Splits chunks more aggressively to reduce individual file sizes
   - Disables sourcemaps to save memory
   - Uses ES2015 target for better browser compatibility and smaller bundle size
   - Limits asset inline size to 4KB

3. **Runtime Optimizations**:
   - Uses `serve` instead of Vite's preview server for lower memory usage
   - Adds `--no-clipboard` and `--single` flags to reduce memory usage
   - Falls back to Vite preview with memory limits if `serve` fails

### Nginx Optimizations

1. **Process and Connection Limits**:
   - Sets `worker_processes 1` to use a single worker
   - Limits file descriptors with `worker_rlimit_nofile 1024`
   - Sets `worker_connections 512` to limit concurrent connections
   - Disables `multi_accept` to reduce CPU usage

2. **Buffer Size Reductions**:
   - Reduces `client_body_buffer_size` to 8KB
   - Reduces `client_header_buffer_size` to 1KB
   - Limits `client_max_body_size` to 1MB
   - Sets `large_client_header_buffers` to 2 1KB buffers

3. **Caching and Compression**:
   - Enables gzip compression with a moderate level (2)
   - Adds caching for static assets with a 30-day expiration
   - Uses a small SSL session cache (2MB)

## Memory Usage Monitoring

You can monitor memory usage with the following commands:

```bash
# Check overall memory usage
free -m

# Check memory usage of specific processes
ps -o pid,user,%mem,command ax | grep -E 'gunicorn|node|nginx' | sort -b -k3 -r

# Monitor memory usage in real-time
top -o %MEM
```

## Troubleshooting Memory Issues

If you encounter memory issues:

1. **Check for Memory Leaks**:
   ```bash
   # Monitor memory usage over time
   watch -n 5 'ps -o pid,%mem,rss,command ax | grep -E "gunicorn|node|nginx" | sort -b -k2 -r'
   ```

2. **Restart Services**:
   ```bash
   # Restart all services
   ./start-prod.sh --restart --low-memory
   
   # Restart only the backend
   ./start-prod.sh --restart --backend --low-memory
   
   # Restart only the frontend
   ./start-prod.sh --restart --frontend --low-memory
   ```

3. **Reduce Worker Count**:
   If memory issues persist, you can manually set a lower worker count by editing the `start-prod.sh` script and changing the `WORKERS` variable calculation.

## Advanced Memory Optimization

For extremely memory-constrained environments (less than 1GB of RAM):

1. **Use a Single Worker**:
   ```bash
   # Edit start-prod.sh to force a single worker
   WORKERS=1
   ```

2. **Disable Nginx**:
   Access the application directly without using Nginx as a reverse proxy.

3. **Use Static Frontend**:
   Serve the frontend as static files from Nginx instead of using a Node.js server.

4. **Disable SSL**:
   Use HTTP instead of HTTPS to save memory used by SSL processing.

## Memory Requirements

Minimum memory requirements for different configurations:

- **Full Stack (Backend + Frontend + Nginx)**: 1.5GB RAM
- **Backend Only**: 512MB RAM
- **Frontend Only**: 256MB RAM
- **Nginx Only**: 64MB RAM

For optimal performance on a 2GB system, we recommend:
- 1 Gunicorn worker with 4 threads
- Node.js with 256MB memory limit
- Nginx with minimal configuration
