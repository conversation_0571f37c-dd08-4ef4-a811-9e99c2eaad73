#!/usr/bin/env python3
"""
Debug Table Parsing - Capture and analyze actual LLM responses
"""

import requests
import json
import re
import time

def test_with_debug_logging():
    """Test the LLM endpoint and capture detailed debug information"""
    
    # Test case from testCase.txt
    test_input = """
Please convert the following steel specifications from imperial to metric and create a table:

S/S 430 BA NO PI
.015(+/-.0015) X 2.343"(+/-.005) X COIL - 7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL - 8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL - 12,550#
.015(+/-.0015) X 19.68"(+/-.03125) X COIL - 8,835#
.015(+/-.0015) X 47.438"(+/-.03125) X COIL - 57,655#

S/S 430 #2B NO PI
.015(+/-.0015) X 16.938"(+/-.005) X COIL - 5,000#
.016(+/-.0015) X 19.6875"(+/-.005) X COIL - 725,321#
.016(+/-.0015) X 35.500"(+/-.03125) X COIL - 122,083#
.016(+/-.0015) X 36.000"(+/-.03125) X COIL - 234,265#
.018(+/-.0015) X 36.000"(+/-.03125) X COIL - 33,841#
"""
    
    url = "http://localhost:8000/api/llm"
    
    payload = {
        "text": test_input,
        "unit_system": "metric",
        "function": "table"
    }
    
    print("🔍 DEBUG: Testing LLM Endpoint with Detailed Logging")
    print("=" * 70)
    print(f"Input: {test_input[:150]}...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print()
    
    try:
        print("📡 Sending request to LLM endpoint...")
        response = requests.post(url, json=payload, timeout=120)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Request successful!")
            print()
            
            # Print the COMPLETE raw response
            print("🔍 COMPLETE RAW LLM RESPONSE:")
            print("=" * 50)
            if 'message' in result:
                raw_message = result['message']
                print(raw_message)
                print("=" * 50)
                print()
                
                # Save the raw response to a file for analysis
                with open('debug_llm_response.txt', 'w', encoding='utf-8') as f:
                    f.write("=== RAW LLM RESPONSE ===\n")
                    f.write(raw_message)
                    f.write("\n\n=== RESPONSE ANALYSIS ===\n")
                
                # Analyze the response structure
                print("📊 RESPONSE STRUCTURE ANALYSIS:")
                print("-" * 40)
                print(f"Total length: {len(raw_message)} characters")
                print(f"Number of lines: {len(raw_message.split(chr(10)))}")
                print(f"Contains <table_stream>: {'<table_stream>' in raw_message}")
                print(f"Contains </table_stream>: {'</table_stream>' in raw_message}")
                print(f"Number of pipe characters: {raw_message.count('|')}")
                print()
                
                # Extract and analyze table content
                if '<table_stream>' in raw_message and '</table_stream>' in raw_message:
                    print("✅ Found table_stream tags")
                    
                    # Extract table content
                    match = re.search(r'<table_stream>(.*?)</table_stream>', raw_message, re.DOTALL)
                    if match:
                        table_content = match.group(1).strip()
                        
                        print("📋 EXTRACTED TABLE CONTENT:")
                        print("-" * 30)
                        print(repr(table_content))  # Use repr to show special characters
                        print("-" * 30)
                        print()
                        
                        # Analyze each line
                        lines = table_content.split('\n')
                        print(f"📝 LINE-BY-LINE ANALYSIS ({len(lines)} lines):")
                        print("-" * 40)
                        
                        for i, line in enumerate(lines):
                            line_stripped = line.strip()
                            if line_stripped:
                                pipe_count = line_stripped.count('|')
                                print(f"Line {i}: {pipe_count} pipes")
                                print(f"  Raw: {repr(line_stripped)}")
                                
                                if '|' in line_stripped:
                                    # Try parsing with our logic
                                    clean_line = re.sub(r'^\||\|$', '', line_stripped)
                                    cells = [c.strip() for c in clean_line.split('|')]
                                    print(f"  Parsed cells ({len(cells)}): {cells}")
                                else:
                                    print(f"  No pipes found")
                                print()
                        
                        # Test our parsing logic
                        print("🧪 TESTING OUR PARSING LOGIC:")
                        print("-" * 40)
                        
                        lines_filtered = [line.strip() for line in lines if line.strip()]
                        
                        if lines_filtered:
                            # Parse header
                            header_line = lines_filtered[0]
                            print(f"Header line: {repr(header_line)}")
                            
                            if '|' in header_line:
                                clean_header = re.sub(r'^\||\|$', '', header_line)
                                headers = [h.strip() for h in clean_header.split('|')]
                                print(f"Parsed headers ({len(headers)}): {headers}")
                                
                                # Parse data rows
                                data_rows = lines_filtered[1:]
                                print(f"Data rows to parse: {len(data_rows)}")
                                
                                for i, row in enumerate(data_rows):
                                    if '|' in row:
                                        clean_row = re.sub(r'^\||\|$', '', row)
                                        cells = [c.strip() for c in clean_row.split('|')]
                                        print(f"  Row {i+1}: {len(cells)} cells vs {len(headers)} headers")
                                        
                                        if len(cells) != len(headers):
                                            print(f"    ❌ COLUMN MISMATCH!")
                                            print(f"    Expected: {len(headers)} columns")
                                            print(f"    Got: {len(cells)} columns")
                                            print(f"    Raw row: {repr(row)}")
                                            print(f"    Parsed cells: {cells}")
                                        else:
                                            print(f"    ✅ Column count matches")
                                            
                                            # Show mapping
                                            for j, (header, cell) in enumerate(zip(headers, cells)):
                                                print(f"      {header}: {cell}")
                                    else:
                                        print(f"  Row {i+1}: No pipes found - {repr(row)}")
                            else:
                                print("❌ Header line has no pipes")
                        else:
                            print("❌ No valid lines found")
                    else:
                        print("❌ Could not extract table content")
                else:
                    print("❌ No table_stream tags found")
                    print("Looking for other table indicators...")
                    
                    # Check for other table formats
                    if '|' in raw_message:
                        print("Found pipe characters - analyzing...")
                        lines_with_pipes = [line for line in raw_message.split('\n') if '|' in line]
                        print(f"Lines with pipes: {len(lines_with_pipes)}")
                        for i, line in enumerate(lines_with_pipes[:5]):  # Show first 5
                            print(f"  {i+1}: {repr(line.strip())}")
                    else:
                        print("No pipe characters found in response")
                
                return True
            else:
                print("❌ No 'message' field in response")
                print(f"Response keys: {list(result.keys())}")
                return False
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Backend server not running")
        print("💡 Please start the backend server:")
        print("   cd backend && python3 main.py")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_saved_response():
    """Analyze the saved response file"""
    try:
        with open('debug_llm_response.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("\n📁 ANALYZING SAVED RESPONSE:")
        print("=" * 50)
        print("File saved successfully for further analysis")
        print("You can examine 'debug_llm_response.txt' for the complete raw response")
        
    except FileNotFoundError:
        print("No saved response file found")

if __name__ == "__main__":
    print("🚀 Starting Table Parsing Debug Session")
    print("=" * 80)
    
    # Test health endpoint first
    try:
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ Backend is running")
        else:
            print(f"⚠️  Backend health check returned: {health_response.status_code}")
    except:
        print("❌ Backend is not running")
        print("💡 Please start the backend server first:")
        print("   cd backend && python3 main.py")
        exit(1)
    
    print()
    
    # Run the debug test
    success = test_with_debug_logging()
    
    # Analyze saved response
    analyze_saved_response()
    
    print("\n" + "=" * 80)
    print("📋 DEBUG SESSION SUMMARY")
    print("=" * 80)
    
    if success:
        print("✅ Debug session completed successfully")
        print("📁 Check 'debug_llm_response.txt' for the complete raw response")
        print("🔍 Review the line-by-line analysis above")
        print("💡 Look for column mismatches and parsing issues")
    else:
        print("❌ Debug session failed")
        print("💡 Check backend server status and connectivity")
    
    print("\n🔧 Next Steps:")
    print("1. Review the raw LLM response for format issues")
    print("2. Check if the LLM is following the prompt correctly")
    print("3. Adjust the prompt if the LLM output format is wrong")
    print("4. Fix parsing logic if the format is correct but parsing fails")
