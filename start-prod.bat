@echo off
setlocal enabledelayedexpansion

echo ===================================================
echo Steel Unit Converter - Production Mode (Windows)
echo ===================================================

:: Set script directory
set "SCRIPT_DIR=%~dp0"
set "SCRIPT_DIR=%SCRIPT_DIR:~0,-1%"
set "FRONTEND_DIR=%SCRIPT_DIR%\frontend"
set "BACKEND_DIR=%SCRIPT_DIR%\backend"
set "LOG_DIR=%SCRIPT_DIR%\logs"

:: Create logs directory if it doesn't exist
if not exist "%LOG_DIR%" (
    mkdir "%LOG_DIR%"
    echo Created logs directory: %LOG_DIR%
)

:: Parse command line arguments
set ACTION=start
set COMPONENT=all
set SHOW_LOGS=false
set URL_ACCESS=false
set LOW_MEMORY=false
set AUTO_FIX=true

:parse_args
if "%~1"=="" goto :end_parse_args
if /i "%~1"=="--restart" (
    set ACTION=restart
    shift
    goto :parse_args
)
if /i "%~1"=="--stop" (
    set ACTION=stop
    shift
    goto :parse_args
)
if /i "%~1"=="--status" (
    set ACTION=status
    shift
    goto :parse_args
)
if /i "%~1"=="--backend" (
    set COMPONENT=backend
    shift
    goto :parse_args
)
if /i "%~1"=="--frontend" (
    set COMPONENT=frontend
    shift
    goto :parse_args
)
if /i "%~1"=="--show-logs" (
    set SHOW_LOGS=true
    shift
    goto :parse_args
)
if /i "%~1"=="--url-access" (
    set URL_ACCESS=true
    shift
    goto :parse_args
)
if /i "%~1"=="--low-memory" (
    set LOW_MEMORY=true
    echo Low memory mode enabled (optimized for 2GB RAM systems)
    set NODE_OPTIONS=--max-old-space-size=256
    shift
    goto :parse_args
)
if /i "%~1"=="fix-nginx" (
    set ACTION=fix-nginx
    shift
    goto :parse_args
)
if /i "%~1"=="fix-git" (
    set ACTION=fix-git
    shift
    goto :parse_args
)
if /i "%~1"=="fix-all" (
    set ACTION=fix-all
    shift
    goto :parse_args
)
if /i "%~1"=="--help" (
    echo Usage: %0 [OPTIONS]
    echo.
    echo Options:
    echo   --restart       Stop existing processes and start new ones
    echo   --stop          Stop all processes and exit
    echo   --status        Check if services are running
    echo   --backend       Only operate on backend
    echo   --frontend      Only operate on frontend
    echo   --show-logs     Show logs after starting
    echo   --url-access    Set up URL access via IIS or nginx
    echo   --low-memory    Optimize for low memory environments (2GB RAM)
    echo   fix-nginx       Fix nginx/IIS configuration issues
    echo   fix-git         Fix git network connectivity issues
    echo   fix-all         Fix all issues (nginx, frontend serving, favicon, git, etc.)
    echo   --help          Show this help message
    exit /b 0
)
shift
goto :parse_args
:end_parse_args

:: Function to kill existing processes
:kill_existing
echo Terminating existing processes...

:: Kill any running Node.js processes (frontend)
taskkill /F /IM node.exe /T 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Frontend processes terminated successfully.
) else (
    echo No active frontend processes found.
)

:: Kill any running Python/Uvicorn processes (backend)
taskkill /F /IM python.exe /T 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Backend processes terminated successfully.
) else (
    echo No active backend processes found.
)

:: Kill any running serve processes
taskkill /F /FI "WINDOWTITLE eq serve*" 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Serve processes terminated successfully.
) else (
    echo No active serve processes found.
)

echo All existing processes terminated.
exit /b 0

:: Function to check Python
:check_python
echo Checking Python installation...
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python is not installed or not in PATH. Please install Python 3.8 or higher.
    exit /b 1
)

:: Check Python version
for /f "tokens=2" %%V in ('python --version 2^>^&1') do (
    echo Found Python version: %%V
)
exit /b 0

:: Function to check Node.js
:check_node
echo Checking Node.js installation...
node --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Node.js is not installed or not in PATH. Please install Node.js 14 or higher.
    exit /b 1
)

:: Check Node.js version
for /f "tokens=1" %%V in ('node --version') do (
    echo Found Node.js version: %%V
)
exit /b 0

:: Function to start backend
:start_backend
echo Starting backend in production mode...

:: Change to backend directory
cd "%BACKEND_DIR%"

:: Install dependencies if needed
if not exist "%BACKEND_DIR%\venv" (
    echo Creating virtual environment...
    python -m venv venv
    call venv\Scripts\activate.bat
    echo Installing Python dependencies...
    pip install -r requirements.txt
) else (
    call venv\Scripts\activate.bat
)

:: Fix import issues
echo Fixing import issues...
python fix_imports.py

:: Run dependency checker to install missing packages
echo Checking for missing dependencies...
python install_missing_deps.py

:: Start backend server with Gunicorn for production
echo Starting backend with Gunicorn...
start cmd /k "title Backend Server && cd %BACKEND_DIR% && call venv\Scripts\activate.bat && python -m gunicorn main:app --worker-class uvicorn.workers.UvicornWorker --workers 2 --threads 4 --bind 0.0.0.0:8000 --max-requests 500 --max-requests-jitter 50 --timeout 120 --keep-alive 5 --log-level warning > %LOG_DIR%\backend.log 2>&1"

echo Backend started successfully.
echo Backend logs available at: %LOG_DIR%\backend.log

:: Return to script directory
cd "%SCRIPT_DIR%"
exit /b 0

:: Function to start frontend
:start_frontend
echo Starting frontend in production mode...

:: Change to frontend directory
cd "%FRONTEND_DIR%"

:: Install dependencies if needed
if not exist "%FRONTEND_DIR%\node_modules" (
    echo Installing Node.js dependencies...
    call npm install
)

:: Build frontend with optimized settings
echo Building frontend with optimized settings...
if "%LOW_MEMORY%"=="true" (
    set NODE_OPTIONS=--max-old-space-size=512
) else (
    set NODE_OPTIONS=--max-old-space-size=1024
)
call npm run build

:: Check if serve is installed, if not install it
call npx serve --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Installing 'serve' for production static file serving...
    call npm install -g serve
)

:: Start frontend server using serve with memory optimization
echo Starting frontend server with serve...
if "%LOW_MEMORY%"=="true" (
    set NODE_OPTIONS=--max-old-space-size=256
) else (
    set NODE_OPTIONS=--max-old-space-size=512
)

start cmd /k "title Frontend Server && cd %FRONTEND_DIR% && npx serve -s dist -l tcp://0.0.0.0:3000 --no-clipboard --single --cors > %LOG_DIR%\frontend.log 2>&1"

echo Frontend started successfully.
echo Frontend logs available at: %LOG_DIR%\frontend.log

:: Return to script directory
cd "%SCRIPT_DIR%"
exit /b 0

:: Function to check services
:check_services
echo Checking if services are running...

:: Check if backend is running
echo Backend (port 8000):
netstat -ano | findstr ":8000" | findstr "LISTENING"
if %ERRORLEVEL% EQU 0 (
    echo Backend is running.
) else (
    echo Backend is not running.
)

:: Check if frontend is running
echo Frontend (port 3000):
netstat -ano | findstr ":3000" | findstr "LISTENING"
if %ERRORLEVEL% EQU 0 (
    echo Frontend is running.
) else (
    echo Frontend is not running.
)

exit /b 0

:: Function to fix git network issues
:fix_git_network
echo Checking and fixing git network issues...

:: Test git connectivity
echo Testing git connectivity...
git ls-remote https://github.com/git/git.git HEAD >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Git connectivity is working properly.
    exit /b 0
)

echo Git connectivity issues detected. Attempting to fix...

:: Check if we're behind a proxy
echo Checking for proxy environment variables...
if defined http_proxy (
    echo HTTP proxy detected: %http_proxy%
    git config --global http.proxy "%http_proxy%"
    echo Configured git to use HTTP proxy: %http_proxy%
)

if defined https_proxy (
    echo HTTPS proxy detected: %https_proxy%
    git config --global https.proxy "%https_proxy%"
    echo Configured git to use HTTPS proxy: %https_proxy%
)

:: Try setting git to use https instead of git protocol
echo Setting git to use HTTPS instead of git protocol...
git config --global url."https://".insteadOf git://

:: Increase git buffer size
echo Increasing git buffer size...
git config --global http.postBuffer 524288000

:: Disable SSL verification (only as a last resort)
echo WARNING: As a last resort, disabling SSL verification. This is not recommended for security reasons.
git config --global http.sslVerify false

:: Test git connectivity again
echo Testing git connectivity again...
git ls-remote https://github.com/git/git.git HEAD >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Git connectivity is now working properly.
    exit /b 0
) else (
    echo Git connectivity issues persist. Please check your network settings manually.
    exit /b 1
)

:: Function to fix all issues
:fix_all_issues
echo Checking and fixing all issues...

:: Fix git network issues
call :fix_git_network

:: Check if ports are open
echo Checking if ports are open...
echo Port 80 (HTTP):
netstat -ano | findstr ":80" | findstr "LISTENING"
if %ERRORLEVEL% NEQ 0 (
    echo Port 80 is not open
)

echo Port 443 (HTTPS):
netstat -ano | findstr ":443" | findstr "LISTENING"
if %ERRORLEVEL% NEQ 0 (
    echo Port 443 is not open
)

echo Port 8000 (Backend):
netstat -ano | findstr ":8000" | findstr "LISTENING"
if %ERRORLEVEL% NEQ 0 (
    echo Port 8000 is not open
)

echo Port 3000 (Frontend):
netstat -ano | findstr ":3000" | findstr "LISTENING"
if %ERRORLEVEL% NEQ 0 (
    echo Port 3000 is not open
)

exit /b 0

:: Main execution
if "%ACTION%"=="start" (
    :: Check requirements
    call :check_python
    call :check_node

    :: Kill existing processes
    call :kill_existing

    :: Start services based on component
    if "%COMPONENT%"=="all" (
        call :start_backend
        call :start_frontend
    ) else if "%COMPONENT%"=="backend" (
        call :start_backend
    ) else if "%COMPONENT%"=="frontend" (
        call :start_frontend
    )

    :: Fix all issues if auto-fix is enabled
    if "%AUTO_FIX%"=="true" (
        call :fix_all_issues
    )

    echo Services started successfully.

    :: Show status
    call :check_services
) else if "%ACTION%"=="restart" (
    :: Kill existing processes
    call :kill_existing

    :: Check requirements
    call :check_python
    call :check_node

    :: Start services based on component
    if "%COMPONENT%"=="all" (
        call :start_backend
        call :start_frontend
    ) else if "%COMPONENT%"=="backend" (
        call :start_backend
    ) else if "%COMPONENT%"=="frontend" (
        call :start_frontend
    )

    :: Fix all issues if auto-fix is enabled
    if "%AUTO_FIX%"=="true" (
        call :fix_all_issues
    )

    echo Services restarted successfully.

    :: Show status
    call :check_services
) else if "%ACTION%"=="stop" (
    call :kill_existing
    echo All services stopped.
) else if "%ACTION%"=="status" (
    call :check_services
) else if "%ACTION%"=="fix-git" (
    call :fix_git_network
    echo Git network issues fixed.
) else if "%ACTION%"=="fix-all" (
    call :fix_all_issues
    echo All issues fixed.
)

echo.
echo ==================================================
echo  Steel Unit Converter - Production Mode (Windows)
echo ==================================================
echo.
echo Management commands:
echo   Start/Restart:    %0 --restart
echo   Stop:             %0 --stop
echo   Status:           %0 --status
echo   Backend Only:     %0 --backend
echo   Frontend Only:    %0 --frontend
echo   Fix Git Network:  %0 fix-git
echo   Fix All Issues:   %0 fix-all
echo.
if "%LOW_MEMORY%"=="true" (
    echo Low memory mode is currently ENABLED
) else (
    echo Low memory mode is currently disabled
)
echo.

endlocal
