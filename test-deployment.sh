#!/usr/bin/env bash

# Test Deployment Script for Steel Unit Converter
# This script tests the full deployment process including nginx

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
LOG_DIR="$SCRIPT_DIR/logs"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to check if a command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Function to print colored output
print_info() {
    echo -e "\033[0;34m[INFO]\033[0m $1"
}

print_success() {
    echo -e "\033[0;32m[SUCCESS]\033[0m $1"
}

print_warning() {
    echo -e "\033[0;33m[WARNING]\033[0m $1"
}

print_error() {
    echo -e "\033[0;31m[ERROR]\033[0m $1"
}

# Function to check if a port is open
check_port() {
    local port=$1
    local service=$2
    
    print_info "Checking if $service is running on port $port..."
    
    if command_exists lsof; then
        if lsof -i:$port | grep LISTEN &> /dev/null; then
            print_success "$service is running on port $port"
            return 0
        else
            print_error "$service is not running on port $port"
            return 1
        fi
    elif command_exists netstat; then
        if netstat -tuln | grep ":$port " &> /dev/null; then
            print_success "$service is running on port $port"
            return 0
        else
            print_error "$service is not running on port $port"
            return 1
        fi
    else
        print_warning "Cannot check if $service is running (neither lsof nor netstat is available)"
        return 2
    fi
}

# Function to check if nginx is installed and running
check_nginx() {
    print_info "Checking if nginx is installed..."
    
    if command_exists nginx; then
        print_success "nginx is installed"
        
        print_info "Checking if nginx is running..."
        if command_exists systemctl; then
            if systemctl is-active --quiet nginx; then
                print_success "nginx is running"
                return 0
            else
                print_error "nginx is not running"
                return 1
            fi
        elif command_exists service; then
            if service nginx status &> /dev/null; then
                print_success "nginx is running"
                return 0
            else
                print_error "nginx is not running"
                return 1
            fi
        elif command_exists brew; then
            if brew services list | grep nginx | grep started &> /dev/null; then
                print_success "nginx is running"
                return 0
            else
                print_error "nginx is not running"
                return 1
            fi
        else
            print_warning "Cannot check if nginx is running"
            return 2
        fi
    else
        print_error "nginx is not installed"
        return 1
    fi
}

# Function to test HTTP endpoints
test_endpoint() {
    local url=$1
    local expected_status=$2
    local description=$3
    
    print_info "Testing $description at $url..."
    
    if command_exists curl; then
        local status=$(curl -s -o /dev/null -w "%{http_code}" $url)
        
        if [ "$status" = "$expected_status" ]; then
            print_success "$description is accessible (Status: $status)"
            return 0
        else
            print_error "$description returned unexpected status: $status (Expected: $expected_status)"
            return 1
        fi
    else
        print_warning "Cannot test $description (curl is not available)"
        return 2
    fi
}

# Function to test the backend
test_backend() {
    print_info "Testing backend..."
    
    # Check if backend is running
    check_port 8000 "Backend"
    
    # Test backend endpoints
    test_endpoint "http://localhost:8000/api/health" "200" "Backend health endpoint"
}

# Function to test the frontend
test_frontend() {
    print_info "Testing frontend..."
    
    # Check if frontend is running
    check_port 3000 "Frontend"
    
    # Test frontend endpoint
    test_endpoint "http://localhost:3000" "200" "Frontend"
}

# Function to test nginx
test_nginx() {
    print_info "Testing nginx..."
    
    # Check if nginx is installed and running
    check_nginx
    
    # Check if nginx is listening on port 80
    check_port 80 "nginx HTTP"
    
    # Check if nginx is listening on port 443
    check_port 443 "nginx HTTPS"
    
    # Test nginx endpoints
    test_endpoint "http://localhost" "301" "HTTP to HTTPS redirect"
    test_endpoint "https://localhost" "200" "HTTPS frontend"
    test_endpoint "https://localhost/api/health" "200" "HTTPS backend health endpoint"
}

# Function to test memory usage
test_memory_usage() {
    print_info "Testing memory usage..."
    
    if command_exists free; then
        print_info "System memory usage:"
        free -m
    elif command_exists vm_stat; then
        print_info "System memory usage (macOS):"
        vm_stat
    else
        print_warning "Cannot check system memory usage"
    fi
    
    print_info "Process memory usage:"
    if command_exists ps; then
        ps -o pid,user,%mem,command ax | grep -E 'gunicorn|node|nginx' | sort -b -k3 -r | head -10
    else
        print_warning "Cannot check process memory usage"
    fi
}

# Main function
main() {
    print_info "Starting deployment test..."
    
    # Stop any existing processes
    print_info "Stopping any existing processes..."
    "$SCRIPT_DIR/start-prod-fixed.sh" stop
    
    # Start the application
    print_info "Starting the application..."
    "$SCRIPT_DIR/start-prod-fixed.sh"
    
    # Wait for services to start
    print_info "Waiting for services to start..."
    sleep 5
    
    # Test backend
    test_backend
    
    # Test frontend
    test_frontend
    
    # Check if nginx is installed
    if command_exists nginx; then
        # Set up nginx
        print_info "Setting up nginx..."
        "$SCRIPT_DIR/setup-nginx-fixed.sh"
        
        # Wait for nginx to start
        print_info "Waiting for nginx to start..."
        sleep 5
        
        # Test nginx
        test_nginx
    else
        print_warning "Skipping nginx tests (nginx is not installed)"
    fi
    
    # Test memory usage
    test_memory_usage
    
    print_info "Deployment test completed."
}

# Run the main function
main
