# 钢铁智联 | Steel Unit Converter

A comprehensive unit conversion application specifically designed for the steel industry, featuring an AI-powered chat interface that supports both metric and imperial unit conversions.

## Features

* **Unit Conversion**
  - Support for both metric and imperial unit systems
  - Clean, ChatGPT-style interface for natural interactions
  - Handles complex unit conversions with AI assistance
  - Accepts various input formats (e.g., `0.75" x 10" x 5'`, `10英寸转换为毫米`)

* **User System**
  - User registration and authentication
  - Email verification
  - Subscription management
  - Usage tracking for free vs. paid accounts

* **Multilingual Support**
  - Both Chinese and English interfaces
  - Automatic language detection from user input

## Technology Stack

### Frontend
- React with TypeScript
- Material-UI for components
- ChatGPT-style minimalist UI design
- Responsive design for mobile and desktop

### Backend
- Python with FastAPI
- PostgreSQL for data storage
- Redis for caching
- JWT authentication
- Volcengine AI API integration

## UI Design

The application features a clean, minimalist UI inspired by ChatGPT:

- **Dark sidebar** with navigation options
- **Light chat area** with clear user/assistant message distinction
- **Simple input box** at the bottom for entering conversion requests
- **Clean results display** with copy functionality

## Setup Instructions

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables in `.env`:
   ```
   DATABASE_URL=postgresql://user:password@localhost/unit_converter
   REDIS_URL=redis://localhost
   VOLCENGINE_API_KEY=your_api_key_here
   SECRET_KEY=your_secret_key_here
   ```

4. Start the backend server:
   ```bash
   uvicorn main:app --reload
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

## Deployment Options

The application can be deployed to various cloud platforms using the provided deployment scripts:

### Using Docker Compose (Local or VM)

```bash
# Copy environment file example and edit
cp .env.example .env

# Start the application
docker-compose up -d
```

### Cloud Deployment

We provide deployment scripts for various cloud providers:

#### Linux/macOS:

```bash
# Deploy to Aliyun ECS (default)
./cloud-deploy.sh

# Deploy to AWS
./cloud-deploy.sh --target aws

# View all options
./cloud-deploy.sh --help
```

#### Windows:

```powershell
# Deploy to Aliyun ECS (default)
.\cloud-deploy.ps1

# Deploy to Azure
.\cloud-deploy.ps1 -Target azure

# View all options
.\cloud-deploy.ps1 -Help
```

### Supported Cloud Providers

- **Aliyun ECS** (fully implemented)
- **AWS** (script placeholder available)
- **Azure** (script placeholder available)
- **Google Cloud Platform** (script placeholder available)

## API Documentation

The backend API documentation is available at `http://localhost:8000/docs` when the server is running.

## Testing

Test cases are available in `testCase.txt`. The application has been verified to handle these test cases correctly.

## License

MIT License
