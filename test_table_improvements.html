<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格功能改进测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-input {
            width: 100%;
            height: 120px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
        }
        .result-table {
            margin: 15px 0;
            border-collapse: collapse;
            width: 100%;
        }
        .result-table th, .result-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .result-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .result-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .copy-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .copy-button:hover {
            background: #0056b3;
        }
        .copy-button.success {
            background: #28a745;
        }
        .error {
            color: red;
            font-style: italic;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>表格功能改进测试</h1>
        <p>测试新的表格解析和复制功能，支持多种格式和Excel兼容性</p>
    </div>

    <div class="container">
        <div class="test-section">
            <h3>测试1: 标准Markdown表格</h3>
            <textarea class="test-input" id="test1">| 材料 | 厚度(mm) | 宽度(mm) | 重量(kg) |
|------|----------|----------|----------|
| S/S 430 BA | 0.38 | 59.5 | 7190 |
| S/S 430 BA | 0.38 | 61.1 | 8061 |
| S/S 430 BA | 0.38 | 419.1 | 12550 |</textarea>
            <button class="copy-button" onclick="testTable('test1', 1)">解析并测试复制</button>
            <div id="result1"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 制表符分隔表格</h3>
            <textarea class="test-input" id="test2">材料	厚度(mm)	宽度(mm)	重量(kg)
S/S 430 BA	0.38	59.5	7190
S/S 430 BA	0.38	61.1	8061
S/S 430 BA	0.38	419.1	12550</textarea>
            <button class="copy-button" onclick="testTable('test2', 2)">解析并测试复制</button>
            <div id="result2"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 空格分隔表格</h3>
            <textarea class="test-input" id="test3">材料          厚度(mm)    宽度(mm)    重量(kg)
S/S 430 BA    0.38        59.5        7190
S/S 430 BA    0.38        61.1        8061
S/S 430 BA    0.38        419.1       12550</textarea>
            <button class="copy-button" onclick="testTable('test3', 3)">解析并测试复制</button>
            <div id="result3"></div>
        </div>

        <div class="test-section">
            <h3>测试4: 复杂钢材数据表格</h3>
            <textarea class="test-input" id="test4">| 规格 | 厚度 | 宽度 | 长度 | 重量 | 材质 |
|------|------|------|------|------|------|
| .015(+/-.0015) X 2.343"(+/-.005) X COIL | 0.38mm | 59.5mm | COIL | 7190# | S/S 430 BA |
| .015(+/-.0015) X 2.406"(+/-.005) X COIL | 0.38mm | 61.1mm | COIL | 8061# | S/S 430 BA |
| .015(+/-.0015) X 16.50"(+/-.005) X COIL | 0.38mm | 419.1mm | COIL | 12550# | S/S 430 BA |</textarea>
            <button class="copy-button" onclick="testTable('test4', 4)">解析并测试复制</button>
            <div id="result4"></div>
        </div>
    </div>

    <script>
        // 从前端代码复制的表格解析函数
        function parseMarkdownTable(markdownTable) {
            try {
                if (!markdownTable || typeof markdownTable !== 'string') {
                    return { headers: [], rows: [], isValid: false, error: 'Empty or invalid table content' };
                }

                const lines = markdownTable.trim().split('\n').filter(line => line.trim().length > 0);

                if (lines.length < 1) {
                    return { headers: [], rows: [], isValid: false, error: 'No table content found' };
                }

                // Enhanced header parsing with better detection
                const headerLine = lines[0].trim();
                let headers = [];
                
                if (headerLine.includes('|')) {
                    // Remove outer pipes if they exist and split
                    const cleanHeader = headerLine.replace(/^\||\|$/g, '');
                    headers = cleanHeader.split('|').map(h => h.trim()).filter(h => h.length > 0);
                } else {
                    // Try multiple separation methods for non-pipe tables
                    if (headerLine.includes('\t')) {
                        // Tab-separated
                        headers = headerLine.split('\t').map(h => h.trim()).filter(h => h.length > 0);
                    } else if (headerLine.match(/\s{2,}/)) {
                        // Multiple spaces
                        headers = headerLine.split(/\s{2,}/).map(h => h.trim()).filter(h => h.length > 0);
                    } else {
                        // Single space or comma separated as fallback
                        headers = headerLine.split(/[,\s]+/).map(h => h.trim()).filter(h => h.length > 0);
                    }
                }

                if (headers.length === 0) {
                    return { headers: [], rows: [], isValid: false, error: 'No valid headers found' };
                }

                // Find separator line (for markdown tables)
                let separatorIndex = -1;
                for (let i = 1; i < lines.length; i++) {
                    if (lines[i].match(/^[\|\s-:]*$/)) {
                        separatorIndex = i;
                        break;
                    }
                }

                let startRowIndex = separatorIndex !== -1 ? separatorIndex + 1 : 1;

                // Enhanced row parsing with better format detection
                const rows = [];
                for (let i = startRowIndex; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line || line.startsWith('<!--') || line.match(/^[\|\s-:]*$/)) continue;

                    let cells = [];
                    
                    if (line.includes('|')) {
                        // Remove outer pipes if they exist and split
                        const cleanLine = line.replace(/^\||\|$/g, '');
                        cells = cleanLine.split('|').map(cell => cell.trim());
                    } else {
                        // Enhanced separation detection matching header parsing
                        if (line.includes('\t')) {
                            // Tab-separated
                            cells = line.split('\t').map(cell => cell.trim()).filter(cell => cell.length > 0);
                        } else if (line.match(/\s{2,}/)) {
                            // Multiple spaces
                            cells = line.split(/\s{2,}/).map(cell => cell.trim()).filter(cell => cell.length > 0);
                        } else {
                            // Single space or comma separated as fallback
                            cells = line.split(/[,\s]+/).map(cell => cell.trim()).filter(cell => cell.length > 0);
                        }
                    }

                    // Only add row if it has meaningful content and matches expected column count
                    if (cells.some(cell => cell.length > 0)) {
                        const row = {};
                        headers.forEach((header, index) => {
                            row[header] = cells[index] || '';
                        });
                        rows.push(row);
                    }
                }

                const isValid = headers.length > 0 && rows.length > 0;
                return { 
                    headers, 
                    rows, 
                    isValid,
                    error: isValid ? undefined : 'No valid table data found'
                };

            } catch (error) {
                console.error('Error parsing markdown table:', error);
                return { 
                    headers: [], 
                    rows: [], 
                    isValid: false, 
                    error: `Parsing error: ${error.message}`
                };
            }
        }

        function tableToExcelHTML(table) {
            const { headers, rows, isValid } = table;
            
            if (!isValid || headers.length === 0 || rows.length === 0) {
                return '';
            }

            try {
                // Create HTML table with Excel-compatible formatting
                let html = '<table border="1" cellpadding="4" cellspacing="0" style="border-collapse: collapse; font-family: Arial, sans-serif;">';
                
                // Add header row with styling
                html += '<thead><tr style="background-color: #f0f0f0; font-weight: bold;">';
                headers.forEach(header => {
                    const safeHeader = escapeHtml(header);
                    html += `<th style="border: 1px solid #ccc; padding: 8px; text-align: left;">${safeHeader}</th>`;
                });
                html += '</tr></thead>';
                
                // Add data rows with styling
                html += '<tbody>';
                rows.forEach((row, rowIndex) => {
                    const rowStyle = rowIndex % 2 === 0 ? 'background-color: #ffffff;' : 'background-color: #f9f9f9;';
                    html += `<tr style="${rowStyle}">`;
                    headers.forEach(header => {
                        const cellContent = row[header] || '';
                        const safeCellContent = escapeHtml(cellContent);
                        // Detect if content is numeric for right alignment
                        const isNumeric = /^[\d,.-]+$/.test(cellContent.trim());
                        const textAlign = isNumeric ? 'right' : 'left';
                        html += `<td style="border: 1px solid #ccc; padding: 8px; text-align: ${textAlign};">${safeCellContent}</td>`;
                    });
                    html += '</tr>';
                });
                html += '</tbody>';
                
                html += '</table>';
                
                return html;
            } catch (error) {
                console.error('Error converting table to HTML:', error);
                return '';
            }
        }

        function tableToTSV(table) {
            const { headers, rows, isValid } = table;
            
            if (!isValid || headers.length === 0 || rows.length === 0) {
                return '';
            }

            try {
                // Create TSV format
                let tsv = headers.join('\t') + '\n';
                
                rows.forEach(row => {
                    const rowData = headers.map(header => {
                        const cellContent = row[header] || '';
                        // Escape tabs and newlines in cell content
                        return cellContent.replace(/\t/g, ' ').replace(/\n/g, ' ').replace(/\r/g, '');
                    });
                    tsv += rowData.join('\t') + '\n';
                });
                
                return tsv;
            } catch (error) {
                console.error('Error converting table to TSV:', error);
                return '';
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function testTable(inputId, testNumber) {
            const input = document.getElementById(inputId).value;
            const resultDiv = document.getElementById(`result${testNumber}`);
            
            // Parse the table
            const parsedTable = parseMarkdownTable(input);
            
            if (!parsedTable.isValid) {
                resultDiv.innerHTML = `<div class="error">解析失败: ${parsedTable.error}</div>`;
                return;
            }

            // Generate HTML table for display
            const htmlTable = tableToExcelHTML(parsedTable);
            const tsvData = tableToTSV(parsedTable);
            
            resultDiv.innerHTML = `
                <div class="success">✓ 解析成功! 找到 ${parsedTable.headers.length} 列, ${parsedTable.rows.length} 行数据</div>
                <div style="margin: 10px 0;">
                    <button class="copy-button" onclick="copyToClipboard('${testNumber}', 'html')">复制HTML格式</button>
                    <button class="copy-button" onclick="copyToClipboard('${testNumber}', 'tsv')">复制TSV格式</button>
                    <button class="copy-button" onclick="copyToClipboard('${testNumber}', 'both')">复制双格式(推荐)</button>
                </div>
                <div style="margin: 10px 0;">
                    <strong>预览:</strong>
                    ${htmlTable}
                </div>
                <details style="margin-top: 10px;">
                    <summary>查看TSV数据</summary>
                    <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">${tsvData}</pre>
                </details>
            `;
            
            // Store data for copying
            window[`tableData${testNumber}`] = { html: htmlTable, tsv: tsvData, parsed: parsedTable };
        }

        async function copyToClipboard(testNumber, format) {
            const data = window[`tableData${testNumber}`];
            if (!data) return;

            try {
                if (format === 'html') {
                    await navigator.clipboard.writeText(data.html);
                    showCopySuccess('HTML格式已复制到剪贴板');
                } else if (format === 'tsv') {
                    await navigator.clipboard.writeText(data.tsv);
                    showCopySuccess('TSV格式已复制到剪贴板');
                } else if (format === 'both') {
                    // Try to copy both formats
                    try {
                        const clipboardItem = new ClipboardItem({
                            'text/html': new Blob([data.html], { type: 'text/html' }),
                            'text/plain': new Blob([data.tsv], { type: 'text/plain' })
                        });
                        await navigator.clipboard.write([clipboardItem]);
                        showCopySuccess('双格式已复制! 可直接粘贴到Excel');
                    } catch (clipboardError) {
                        // Fallback to TSV
                        await navigator.clipboard.writeText(data.tsv);
                        showCopySuccess('TSV格式已复制(降级模式)');
                    }
                }
            } catch (error) {
                console.error('复制失败:', error);
                alert('复制失败: ' + error.message);
            }
        }

        function showCopySuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;
            successDiv.style.position = 'fixed';
            successDiv.style.top = '20px';
            successDiv.style.right = '20px';
            successDiv.style.background = 'white';
            successDiv.style.padding = '10px 20px';
            successDiv.style.border = '2px solid #28a745';
            successDiv.style.borderRadius = '4px';
            successDiv.style.zIndex = '1000';
            
            document.body.appendChild(successDiv);
            
            setTimeout(() => {
                document.body.removeChild(successDiv);
            }, 3000);
        }
    </script>
</body>
</html>
