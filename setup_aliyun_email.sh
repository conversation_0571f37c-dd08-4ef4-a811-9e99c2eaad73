#!/bin/bash

echo "=== Aliyun Enterprise Email Configuration ==="
echo "Timestamp: $(date)"
echo "============================================="

# Function to print colored output
print_info() {
    echo -e "\e[34m[INFO]\e[0m $1"
}

print_success() {
    echo -e "\e[32m[SUCCESS]\e[0m $1"
}

print_error() {
    echo -e "\e[31m[ERROR]\e[0m $1"
}

print_warning() {
    echo -e "\e[33m[WARNING]\e[0m $1"
}

# Check if we're on Aliyun
if [[ $(hostname) == *"aliyun"* ]] || [[ $(hostname) == *"iZ"* ]]; then
    print_info "Detected Aliyun ECS environment"
else
    print_warning "This script is optimized for Aliyun ECS. Results may vary on other platforms."
fi

echo
echo "=== Aliyun Enterprise Email Setup Guide ==="
echo "Before configuring, please ensure you have:"
echo "1. ✅ Aliyun Enterprise Email account"
echo "2. ✅ SMTP service enabled in admin panel"
echo "3. ✅ Valid email credentials"
echo "4. ✅ Domain verification completed (if using custom domain)"
echo

# Function to backup existing .env
backup_env() {
    if [ -f .env ]; then
        backup_file=".env.backup.$(date +%Y%m%d_%H%M%S)"
        cp .env "$backup_file"
        print_success "Backed up existing .env to $backup_file"
    fi
}

# Function to test Aliyun Enterprise Email connection
test_aliyun_email_connection() {
    local smtp_server=$1
    local smtp_port=$2
    
    print_info "Testing connection to $smtp_server:$smtp_port..."
    
    if timeout 10 bash -c "</dev/tcp/$smtp_server/$smtp_port" 2>/dev/null; then
        print_success "✓ Connection to $smtp_server:$smtp_port successful"
        return 0
    else
        print_error "✗ Connection to $smtp_server:$smtp_port failed"
        return 1
    fi
}

# Function to update .env file with Aliyun Enterprise Email settings
update_aliyun_email_config() {
    local smtp_server=$1
    local smtp_port=$2
    local smtp_username=$3
    local smtp_password=$4
    
    print_info "Updating .env file with Aliyun Enterprise Email settings..."
    
    if [ -f .env ]; then
        # Update existing settings
        sed -i.bak "s/^SMTP_SERVER=.*/SMTP_SERVER=$smtp_server/" .env
        sed -i.bak "s/^SMTP_PORT=.*/SMTP_PORT=$smtp_port/" .env
        sed -i.bak "s/^SMTP_USERNAME=.*/SMTP_USERNAME=$smtp_username/" .env
        sed -i.bak "s/^SMTP_PASSWORD=.*/SMTP_PASSWORD=$smtp_password/" .env
        
        # Add settings if they don't exist
        grep -q "^SMTP_SERVER=" .env || echo "SMTP_SERVER=$smtp_server" >> .env
        grep -q "^SMTP_PORT=" .env || echo "SMTP_PORT=$smtp_port" >> .env
        grep -q "^SMTP_USERNAME=" .env || echo "SMTP_USERNAME=$smtp_username" >> .env
        grep -q "^SMTP_PASSWORD=" .env || echo "SMTP_PASSWORD=$smtp_password" >> .env
        
        # Add Aliyun-specific settings
        grep -q "^SMTP_SENDER_NAME=" .env || echo "SMTP_SENDER_NAME=SteelNet" >> .env
        grep -q "^SMTP_DOMAIN=" .env || echo "SMTP_DOMAIN=steelnet.ai" >> .env
        
        print_success "✅ .env file updated with Aliyun Enterprise Email settings"
    else
        print_error "❌ .env file not found. Please run this script from the project root directory."
        exit 1
    fi
}

echo "=== Configuration Options ==="
echo "1. Configure with custom domain (smtp.yourdomain.com)"
echo "2. Configure with default Aliyun server (smtp.qiye.aliyun.com)"
echo "3. Test existing configuration"
echo "4. Show current configuration"
echo

read -p "Choose an option (1-4): " choice

case $choice in
    1)
        print_info "Configuring Aliyun Enterprise Email with custom domain..."
        echo
        read -p "Enter your domain (e.g., yourdomain.com): " domain
        read -p "Enter your email username (e.g., user@$domain): " email_username
        read -s -p "Enter your email password: " email_password
        echo
        
        smtp_server="smtp.$domain"
        smtp_port="465"
        
        backup_env
        
        # Test connection first
        if test_aliyun_email_connection "$smtp_server" "$smtp_port"; then
            update_aliyun_email_config "$smtp_server" "$smtp_port" "$email_username" "$email_password"
            print_success "✅ Aliyun Enterprise Email configured successfully with custom domain!"
        else
            print_error "❌ Connection test failed. Please check:"
            print_error "   - Domain DNS settings"
            print_error "   - SMTP service is enabled"
            print_error "   - Firewall settings"
        fi
        ;;
        
    2)
        print_info "Configuring Aliyun Enterprise Email with default server..."
        echo
        read -p "Enter your email username (complete email address): " email_username
        read -s -p "Enter your email password: " email_password
        echo
        
        smtp_server="smtp.qiye.aliyun.com"
        smtp_port="465"
        
        backup_env
        
        # Test connection first
        if test_aliyun_email_connection "$smtp_server" "$smtp_port"; then
            update_aliyun_email_config "$smtp_server" "$smtp_port" "$email_username" "$email_password"
            print_success "✅ Aliyun Enterprise Email configured successfully!"
        else
            print_error "❌ Connection test failed. Please check:"
            print_error "   - SMTP service is enabled in Aliyun Email admin panel"
            print_error "   - Credentials are correct"
            print_error "   - Network connectivity"
        fi
        ;;
        
    3)
        print_info "Testing existing Aliyun Enterprise Email configuration..."
        
        if [ -f .env ]; then
            smtp_server=$(grep "^SMTP_SERVER=" .env | cut -d'=' -f2 | tr -d '"')
            smtp_port=$(grep "^SMTP_PORT=" .env | cut -d'=' -f2 | tr -d '"')
            smtp_username=$(grep "^SMTP_USERNAME=" .env | cut -d'=' -f2 | tr -d '"')
            
            if [ ! -z "$smtp_server" ] && [ ! -z "$smtp_port" ]; then
                echo "Current configuration:"
                echo "  Server: $smtp_server"
                echo "  Port: $smtp_port"
                echo "  Username: $smtp_username"
                echo
                
                test_aliyun_email_connection "$smtp_server" "$smtp_port"
                
                # Run Python SMTP test
                print_info "Running Python SMTP test..."
                python3 debug_smtp.py
            else
                print_error "❌ SMTP configuration not found in .env file"
            fi
        else
            print_error "❌ .env file not found"
        fi
        ;;
        
    4)
        print_info "Current Aliyun Enterprise Email configuration:"
        
        if [ -f .env ]; then
            echo "=== Current .env SMTP Settings ==="
            grep "^SMTP_" .env || echo "No SMTP settings found"
        else
            print_error "❌ .env file not found"
        fi
        ;;
        
    *)
        print_error "❌ Invalid choice. Exiting."
        exit 1
        ;;
esac

echo
echo "=== Important Notes for Aliyun Enterprise Email ==="
echo "📧 SMTP Configuration:"
echo "   - Server: smtp.qiye.aliyun.com or smtp.yourdomain.com"
echo "   - Port: 465 (SSL) - Recommended"
echo "   - Port: 80 (Plain) - Alternative"
echo "   - Port: 25 - Avoid (blocked on Aliyun ECS)"
echo
echo "🔧 Admin Panel Settings:"
echo "   1. Login to Aliyun Email admin panel"
echo "   2. Go to 'Organization & Users' > 'Email Management'"
echo "   3. Enable 'POP3/SMTP Service' for the user"
echo "   4. Enable 'IMAP/SMTP Service' for the user"
echo "   5. Consider enabling 'Third-party Client Security Password'"
echo
echo "🔒 Security Recommendations:"
echo "   - Use SSL encryption (port 465)"
echo "   - Enable security password for third-party clients"
echo "   - Monitor email sending logs"
echo "   - Set up SPF/DKIM records for better deliverability"
echo
echo "🚀 Next Steps:"
echo "   1. Restart your application to apply new settings"
echo "   2. Test email sending functionality"
echo "   3. Monitor application logs for any issues"
echo
print_success "Aliyun Enterprise Email configuration completed!" 