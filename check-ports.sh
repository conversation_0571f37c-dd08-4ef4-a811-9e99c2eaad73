#!/bin/bash

# Script to check if ports are open and accessible
# This script checks if ports 80, 443, 3000, and 8000 are open and accessible

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Public IP address
PUBLIC_IP="************"

# Function to print section header
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Function to print success message
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print warning message
print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Function to print error message
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to check if a port is listening locally
check_local_port() {
    local port=$1
    local description=$2
    
    # Try different methods to check if port is listening
    if command -v netstat &> /dev/null; then
        if netstat -tulpn 2>/dev/null | grep -q ":$port "; then
            print_success "Port $port ($description) is listening locally"
            return 0
        fi
    elif command -v ss &> /dev/null; then
        if ss -tulpn 2>/dev/null | grep -q ":$port "; then
            print_success "Port $port ($description) is listening locally"
            return 0
        fi
    elif command -v lsof &> /dev/null; then
        if lsof -i :$port 2>/dev/null | grep -q "LISTEN"; then
            print_success "Port $port ($description) is listening locally"
            return 0
        fi
    else
        # Fallback to /dev/tcp if no tools are available (Bash only)
        if timeout 1 bash -c "echo > /dev/tcp/localhost/$port" 2>/dev/null; then
            print_success "Port $port ($description) is listening locally"
            return 0
        fi
    fi
    
    print_error "Port $port ($description) is NOT listening locally"
    return 1
}

# Function to check if a port is accessible from the internet
check_external_port() {
    local port=$1
    local description=$2
    
    # Use curl to check if the port is accessible
    echo "Testing if port $port is accessible from the internet..."
    
    # For HTTP/HTTPS ports, use curl
    if [ "$port" -eq 80 ] || [ "$port" -eq 443 ]; then
        local protocol="http"
        if [ "$port" -eq 443 ]; then
            protocol="https"
        fi
        
        # Use curl to check if the port is accessible
        if curl -s -k -o /dev/null -w "%{http_code}" $protocol://$PUBLIC_IP:$port | grep -q "200\|301\|302"; then
            print_success "Port $port ($description) is accessible from the internet"
            return 0
        else
            print_error "Port $port ($description) is NOT accessible from the internet"
            return 1
        fi
    else
        # For other ports, use telnet or nc
        if command -v nc &> /dev/null; then
            if nc -z -w 5 $PUBLIC_IP $port; then
                print_success "Port $port ($description) is accessible from the internet"
                return 0
            fi
        elif command -v telnet &> /dev/null; then
            if echo quit | telnet $PUBLIC_IP $port 2>/dev/null | grep -q Connected; then
                print_success "Port $port ($description) is accessible from the internet"
                return 0
            fi
        else
            print_warning "Cannot check if port $port is accessible from the internet (nc or telnet not installed)"
            return 2
        fi
        
        print_error "Port $port ($description) is NOT accessible from the internet"
        return 1
    fi
}

# Function to check firewall status
check_firewall() {
    print_header "Checking firewall status"
    
    # Check if firewall is installed and running
    if command -v ufw &> /dev/null; then
        echo "UFW firewall status:"
        sudo ufw status
        
        # Check if ports 80 and 443 are allowed
        if sudo ufw status | grep -E "80|443" | grep -q "ALLOW"; then
            print_success "Ports 80 and 443 are allowed in UFW"
        else
            print_warning "Ports 80 and 443 may not be allowed in UFW"
            
            # Ask if user wants to allow these ports
            echo -e "\nWould you like to allow ports 80 and 443 in UFW? (y/n)"
            read -r ALLOW_PORTS
            
            if [[ "$ALLOW_PORTS" =~ ^[Yy]$ ]]; then
                echo "Allowing ports 80 and 443 in UFW..."
                sudo ufw allow 80/tcp
                sudo ufw allow 443/tcp
                print_success "Ports allowed successfully"
            fi
        fi
    elif command -v firewall-cmd &> /dev/null; then
        echo "FirewallD status:"
        sudo firewall-cmd --state
        
        # Check if ports 80 and 443 are allowed
        if sudo firewall-cmd --list-ports | grep -E "80|443" > /dev/null; then
            print_success "Ports 80 and 443 are allowed in FirewallD"
        else
            print_warning "Ports 80 and 443 may not be allowed in FirewallD"
            
            # Ask if user wants to allow these ports
            echo -e "\nWould you like to allow ports 80 and 443 in FirewallD? (y/n)"
            read -r ALLOW_PORTS
            
            if [[ "$ALLOW_PORTS" =~ ^[Yy]$ ]]; then
                echo "Allowing ports 80 and 443 in FirewallD..."
                sudo firewall-cmd --permanent --add-port=80/tcp
                sudo firewall-cmd --permanent --add-port=443/tcp
                sudo firewall-cmd --reload
                print_success "Ports allowed successfully"
            fi
        fi
    elif command -v iptables &> /dev/null; then
        echo "IPTables rules:"
        sudo iptables -L | grep -E "80|443" || echo "No explicit rules for ports 80/443"
    else
        print_warning "No firewall management tool found (ufw, firewall-cmd, iptables)"
    fi
}

# Function to check Alibaba Cloud security group
check_aliyun_security() {
    print_header "Checking Alibaba Cloud security group"
    
    # Check if we can access the metadata service
    if curl -s http://***************/latest/meta-data/ > /dev/null; then
        print_success "Running on Alibaba Cloud"
        
        # Get instance ID
        INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
        echo "Instance ID: $INSTANCE_ID"
        
        # Get region ID
        REGION_ID=$(curl -s http://***************/latest/meta-data/region-id)
        echo "Region ID: $REGION_ID"
        
        # Check if aliyun CLI is installed
        if command -v aliyun &> /dev/null; then
            print_success "Aliyun CLI is installed"
            
            # Try to get security group information
            echo "Fetching security group information..."
            aliyun ecs DescribeSecurityGroups
            
            # Ask for security group ID
            echo -e "\nPlease enter your security group ID from the above list:"
            read -r SECURITY_GROUP_ID
            
            # Get security group rules
            echo "Fetching security group rules for $SECURITY_GROUP_ID..."
            aliyun ecs DescribeSecurityGroupAttribute --SecurityGroupId $SECURITY_GROUP_ID
            
            # Check if ports 80 and 443 are allowed
            if aliyun ecs DescribeSecurityGroupAttribute --SecurityGroupId $SECURITY_GROUP_ID | grep -E "Port.*80|Port.*443" | grep -q "Accept"; then
                print_success "Ports 80 and 443 are allowed in the security group"
            else
                print_error "Ports 80 and 443 may not be allowed in the security group"
                
                # Ask if user wants to add the rules
                echo -e "\nWould you like to add rules for ports 80 and 443? (y/n)"
                read -r ADD_RULES
                
                if [[ "$ADD_RULES" =~ ^[Yy]$ ]]; then
                    echo "Adding rule for port 80..."
                    aliyun ecs AuthorizeSecurityGroup --SecurityGroupId $SECURITY_GROUP_ID --IpProtocol tcp --PortRange 80/80 --SourceCidrIp 0.0.0.0/0
                    
                    echo "Adding rule for port 443..."
                    aliyun ecs AuthorizeSecurityGroup --SecurityGroupId $SECURITY_GROUP_ID --IpProtocol tcp --PortRange 443/443 --SourceCidrIp 0.0.0.0/0
                    
                    print_success "Rules added successfully"
                fi
            fi
        else
            print_warning "Aliyun CLI is not installed"
            print_warning "Please check security group settings manually in the Alibaba Cloud console"
            echo "Please follow these steps:"
            echo "1. Log in to the Alibaba Cloud console"
            echo "2. Go to Elastic Compute Service (ECS)"
            echo "3. Click on 'Security Groups' in the left menu"
            echo "4. Find the security group associated with your instance"
            echo "5. Click on 'Add Rules' and add the following rules:"
            echo "   - Protocol: TCP, Port Range: 80/80, Authorization Object: 0.0.0.0/0"
            echo "   - Protocol: TCP, Port Range: 443/443, Authorization Object: 0.0.0.0/0"
        fi
    else
        print_warning "Not running on Alibaba Cloud or metadata service is not accessible"
    fi
}

# Main function
main() {
    print_header "Port Accessibility Check"
    echo "This script will check if ports are open and accessible"
    echo "Public IP: $PUBLIC_IP"
    
    # Check if running as root
    if [ "$EUID" -eq 0 ]; then
        print_warning "Running as root. Some checks may not work correctly."
    fi
    
    # Check if ports are listening locally
    print_header "Checking if ports are listening locally"
    check_local_port 80 "HTTP"
    check_local_port 443 "HTTPS"
    check_local_port 3000 "Frontend"
    check_local_port 8000 "Backend"
    
    # Check if ports are accessible from the internet
    print_header "Checking if ports are accessible from the internet"
    check_external_port 80 "HTTP"
    check_external_port 443 "HTTPS"
    
    # Check firewall status
    check_firewall
    
    # Check Alibaba Cloud security group
    check_aliyun_security
    
    print_header "Summary"
    echo "If ports 80 and 443 are not accessible from the internet, please check your security group settings"
    echo "You can also try the following:"
    echo "1. Restart nginx: sudo systemctl restart nginx"
    echo "2. Check nginx configuration: sudo nginx -t"
    echo "3. Check nginx logs: sudo tail -f /var/log/nginx/error.log"
    echo "4. Restart the application: ./start-prod.sh --restart --url-access"
}

# Run the main function
main
