#!/usr/bin/env python3
"""
Mock Test for Table Parsing - Simulate LLM Response and Test Frontend Parsing
"""

import re
import json

def simulate_llm_response():
    """Simulate what the LLM should return based on our prompt"""
    
    # This is what the LLM should generate based on our updated prompt
    mock_response = """Based on your steel specifications, I'll convert them from imperial to metric units and create a comprehensive table:

<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015(+/-0.0015)" x 2.343(+/-0.005)" x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015(+/-0.0015)" x 2.406(+/-0.005)" x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015(+/-0.0015)" x 16.50(+/-0.005)" x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish
004|S/S 430 BA NO PI|0.015(+/-0.0015)" x 19.68(+/-0.03125)" x COIL|8835#|0.38(+/-0.04)mm x 499.87(+/-0.79)mm x COIL|4007.5 kg|BA Finish
005|S/S 430 BA NO PI|0.015(+/-0.0015)" x 47.438(+/-0.03125)" x COIL|57655#|0.38(+/-0.04)mm x 1204.93(+/-0.79)mm x COIL|26152.6 kg|BA Finish
006|S/S 430 #2B NO PI|0.015(+/-0.0015)" x 16.938(+/-0.005)" x COIL|5000#|0.38(+/-0.04)mm x 430.23(+/-0.13)mm x COIL|2268.0 kg|#2B Finish
007|S/S 430 #2B NO PI|0.016(+/-0.0015)" x 19.6875(+/-0.005)" x COIL|725321#|0.41(+/-0.04)mm x 500.06(+/-0.13)mm x COIL|329039.9 kg|#2B Finish
008|S/S 430 #2B NO PI|0.016(+/-0.0015)" x 35.500(+/-0.03125)" x COIL|122083#|0.41(+/-0.04)mm x 901.7(+/-0.79)mm x COIL|55376.9 kg|#2B Finish
009|S/S 430 #2B NO PI|0.016(+/-0.0015)" x 36.000(+/-0.03125)" x COIL|234265#|0.41(+/-0.04)mm x 914.4(+/-0.79)mm x COIL|106273.9 kg|#2B Finish
010|S/S 430 #2B NO PI|0.018(+/-0.0015)" x 36.000(+/-0.03125)" x COIL|33841#|0.46(+/-0.04)mm x 914.4(+/-0.79)mm x COIL|15350.9 kg|#2B Finish
</table_stream>

All conversions have been completed with proper tolerances maintained. The table shows both original imperial measurements and converted metric equivalents with appropriate weight conversions from pounds to kilograms."""
    
    return mock_response

def test_frontend_parsing_logic(response_text):
    """Test the frontend parsing logic with the mock response"""
    
    print("🧪 Testing Frontend Parsing Logic")
    print("=" * 60)
    
    # Simulate the parseConvertedContent function from ConversionChat.tsx
    def parseConvertedContent(content):
        try:
            print('parseConvertedContent: Starting parse with content length:', len(content))

            # Check for table_stream tags first
            tableStreamMatch = re.search(r'<table_stream>(.*?)</table_stream>', content, re.DOTALL)
            if tableStreamMatch:
                tableContent = tableStreamMatch.group(1).strip()
                remainingContent = re.sub(r'<table_stream>.*?</table_stream>', '', content, flags=re.DOTALL).strip()
                
                print('Found table_stream content:', tableContent[:100] + "...")
                
                # Parse the table format using robust delimiter detection
                lines = [line.strip() for line in tableContent.split('\n') if line.strip()]
                
                if len(lines) >= 2:
                    headerLine = lines[0]
                    headers = []
                    delimiter = '|'
                    
                    # Robust delimiter detection
                    if '|' in headerLine:
                        cleanHeader = re.sub(r'^\||\|$', '', headerLine)
                        headers = [h.strip() for h in cleanHeader.split('|')]
                        delimiter = '|'
                    elif '\t' in headerLine:
                        headers = [h.strip() for h in headerLine.split('\t')]
                        delimiter = '\t'
                    elif re.search(r'\s{2,}', headerLine):
                        headers = [h.strip() for h in re.split(r'\s{2,}', headerLine)]
                        delimiter = 'space'
                    else:
                        headers = [headerLine.strip()]
                        delimiter = 'single'
                    
                    print('Parsed headers:', {'headers': headers, 'delimiter': delimiter, 'count': len(headers)})
                    
                    rows = []
                    for lineIndex, line in enumerate(lines[1:]):
                        cells = []
                        
                        if delimiter == '|':
                            cleanLine = re.sub(r'^\||\|$', '', line)
                            cells = [c.strip() for c in cleanLine.split('|')]
                        elif delimiter == '\t':
                            cells = [c.strip() for c in line.split('\t')]
                        elif delimiter == 'space':
                            cells = [c.strip() for c in re.split(r'\s{2,}', line)]
                        else:
                            cells = [line.strip()]
                        
                        print(f'Row {lineIndex}:', {'cells': cells, 'count': len(cells)})
                        
                        row = {}
                        for index, header in enumerate(headers):
                            row[header] = cells[index] if index < len(cells) else ''
                        rows.append(row)
                    
                    return {
                        'hasTable': True,
                        'tableContent': '',
                        'cleanContent': remainingContent,
                        'tableData': {'headers': headers, 'rows': rows}
                    }
            
            return {
                'hasTable': False,
                'tableContent': '',
                'cleanContent': content
            }
            
        except Exception as error:
            print('Parse error:', error)
            return {
                'hasTable': False,
                'tableContent': '',
                'cleanContent': content,
                'error': str(error)
            }
    
    # Test the parsing
    result = parseConvertedContent(response_text)
    
    print("\n📊 Parsing Results:")
    print("-" * 40)
    print(f"Has Table: {result['hasTable']}")
    
    if result['hasTable'] and 'tableData' in result:
        tableData = result['tableData']
        headers = tableData['headers']
        rows = tableData['rows']
        
        print(f"Headers ({len(headers)}): {headers}")
        print(f"Rows: {len(rows)}")
        
        # Expected headers
        expected_headers = ['Item Code', 'Description', 'Size (Original)', 'Customer QTY', 'Size (Converted)', 'Converted QTY', 'Remarks']
        
        # Validate structure
        header_count_ok = len(headers) == len(expected_headers)
        print(f"Header count correct: {header_count_ok} (expected {len(expected_headers)}, got {len(headers)})")
        
        # Check each row
        all_rows_valid = True
        for i, row in enumerate(rows):
            row_keys = list(row.keys())
            cells_count = len([v for v in row.values() if v])  # Non-empty cells
            
            if len(row_keys) != len(headers):
                print(f"❌ Row {i+1}: Key count mismatch (expected {len(headers)}, got {len(row_keys)})")
                all_rows_valid = False
            else:
                print(f"✅ Row {i+1}: {cells_count} non-empty cells")
                
                # Show first few rows in detail
                if i < 3:
                    for header in headers:
                        value = row.get(header, '')
                        print(f"    {header}: {value}")
        
        if header_count_ok and all_rows_valid:
            print("\n🎉 ALL PARSING TESTS PASSED!")
            print("✅ Correct number of headers")
            print("✅ All rows have correct structure")
            print("✅ Table parsing is working correctly")
            return True
        else:
            print("\n❌ PARSING TESTS FAILED")
            if not header_count_ok:
                print("❌ Header count mismatch")
            if not all_rows_valid:
                print("❌ Some rows have structural issues")
            return False
    else:
        print("❌ No table data found or parsing failed")
        if 'error' in result:
            print(f"Error: {result['error']}")
        return False

def test_problematic_cases():
    """Test with problematic cases that might cause column misalignment"""
    
    print("\n🔧 Testing Problematic Cases")
    print("=" * 50)
    
    # Case with missing data and special characters
    problematic_response = """<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
006|S/S 430 BA NO PI|0.015(+/-0.0015)" x 47.000(+/-0.031|118001#|0.38(+/-0.04)mm x 1193.8(+/-0.79)mm x COIL|53524.9 kg|
007|S/S 430 BA NO PI|0.015(+/-0.0015)" x 35.438(+/-0.03125)" x COIL|62515#|0.38(+/-0.04)mm x 900.1(+/-0.79)mm x COIL|28356.8 kg|BA Finish
008|S/S 430 #2B NO PI|0.015(+/-0.0015)" x 16.938(+/-0.005)" x COIL|5000#|0.38(+/-0.04)mm x 430.2(+/-0.13)mm x COIL|2268.0 kg|#2B Finish
</table_stream>"""
    
    print("Testing with problematic data (missing fields, special characters)...")
    result = test_frontend_parsing_logic(problematic_response)
    
    return result

if __name__ == "__main__":
    print("🚀 Starting Mock Table Parsing Test")
    print("=" * 70)
    
    # Test 1: Perfect case
    print("📋 Test 1: Perfect Table Format")
    mock_response = simulate_llm_response()
    
    print("Mock LLM Response (first 200 chars):")
    print(mock_response[:200] + "...")
    print()
    
    perfect_result = test_frontend_parsing_logic(mock_response)
    
    # Test 2: Problematic cases
    problematic_result = test_problematic_cases()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 FINAL TEST SUMMARY")
    print("=" * 70)
    
    if perfect_result and problematic_result:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Perfect table format works correctly")
        print("✅ Problematic cases handled gracefully")
        print("✅ Frontend parsing logic is robust")
        print("\n💡 The table parsing fixes are working correctly!")
        print("💡 Ready to test with real LLM responses")
    else:
        print("❌ SOME TESTS FAILED")
        if not perfect_result:
            print("❌ Perfect table format test failed")
        if not problematic_result:
            print("❌ Problematic cases test failed")
        print("\n💡 Need to review and fix the parsing logic")
    
    print("\n🚀 Next Steps:")
    print("1. Fix any LLM connectivity issues (SOCKS proxy)")
    print("2. Test with real LLM responses")
    print("3. Verify in the frontend application")
    print("4. Iterate until perfect column alignment")
