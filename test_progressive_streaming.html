<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progressive Streaming Table Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .streaming-output {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            min-height: 200px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            color: #28a745;
            font-weight: bold;
            margin-left: 10px;
        }
        .progress {
            background: #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .progress-bar {
            background: #007bff;
            height: 20px;
            border-radius: 4px;
            transition: width 0.3s ease;
            color: white;
            text-align: center;
            line-height: 20px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Progressive Streaming Table Test</h1>
    <p>This test simulates how tables are parsed progressively during streaming, line by line.</p>

    <div class="test-section">
        <h2>Streaming Table Parser Test</h2>
        <p>Click the button below to simulate streaming table data line by line:</p>
        
        <button class="btn" onclick="startStreamingTest()">Start Streaming Test</button>
        <button class="btn" onclick="resetTest()">Reset</button>
        
        <div class="progress">
            <div class="progress-bar" id="progress-bar" style="width: 0%;">0%</div>
        </div>
        
        <div class="streaming-output" id="streaming-output">
            <p>Click "Start Streaming Test" to begin...</p>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Data</h2>
        <p>The test will stream this table data progressively:</p>
        <pre id="test-data">
&lt;table_stream&gt;
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015(+/-0.0015) x 2.343"(+/-0.005) x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015(+/-0.0015) x 2.406"(+/-0.005) x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015(+/-0.0015) x 16.50"(+/-0.005) x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish
004|S/S 430 BA NO PI|0.015(+/-0.0015) x 19.68"(+/-0.03125) x COIL|8835#|0.38(+/-0.04)mm x 499.87(+/-0.79)mm x COIL|4007.9 kg|BA Finish
005|S/S 430 BA NO PI|0.015(+/-0.0015) x 47.438"(+/-0.03125) x COIL|57655#|0.38(+/-0.04)mm x 1204.93(+/-0.79)mm x COIL|26154.4 kg|BA Finish
&lt;/table_stream&gt;
        </pre>
    </div>

    <script>
        // Streaming Table Parser (simplified version for testing)
        class StreamingTableParser {
            constructor() {
                this.headers = [];
                this.rows = [];
                this.isHeaderParsed = false;
                this.buffer = '';
            }

            addChunk(chunk) {
                this.buffer += chunk;
                
                // Look for table_stream tags
                const streamMatch = this.buffer.match(/<table_stream>([\s\S]*?)(?:<\/table_stream>|$)/);
                if (streamMatch) {
                    const tableContent = streamMatch[1];
                    return this.parseStreamingContent(tableContent);
                }
                
                return { headers: this.headers, rows: this.rows, isValid: false };
            }

            parseStreamingContent(content) {
                const lines = content.split('\n').filter(line => line.trim());
                
                if (lines.length === 0) {
                    return { headers: this.headers, rows: this.rows, isValid: false };
                }

                // Parse headers if not done yet
                if (!this.isHeaderParsed && lines.length > 0) {
                    this.headers = lines[0].split('|').map(h => h.trim()).filter(h => h.length > 0);
                    this.isHeaderParsed = true;
                }

                // Parse data rows (skip header line)
                const dataLines = lines.slice(1);
                this.rows = dataLines.map(line => {
                    const cells = line.split('|').map(c => c.trim());
                    const row = {};
                    this.headers.forEach((header, index) => {
                        row[header] = cells[index] || '';
                    });
                    return row;
                });

                const isValid = this.headers.length > 0;
                return { 
                    headers: this.headers, 
                    rows: this.rows, 
                    isValid,
                    error: isValid ? undefined : 'Incomplete table data'
                };
            }

            reset() {
                this.headers = [];
                this.rows = [];
                this.isHeaderParsed = false;
                this.buffer = '';
            }

            isComplete() {
                return this.buffer.includes('</table_stream>');
            }
        }

        let parser = new StreamingTableParser();
        let streamingInterval = null;

        const testData = `<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015(+/-0.0015) x 2.343"(+/-0.005) x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015(+/-0.0015) x 2.406"(+/-0.005) x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015(+/-0.0015) x 16.50"(+/-0.005) x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish
004|S/S 430 BA NO PI|0.015(+/-0.0015) x 19.68"(+/-0.03125) x COIL|8835#|0.38(+/-0.04)mm x 499.87(+/-0.79)mm x COIL|4007.9 kg|BA Finish
005|S/S 430 BA NO PI|0.015(+/-0.0015) x 47.438"(+/-0.03125) x COIL|57655#|0.38(+/-0.04)mm x 1204.93(+/-0.79)mm x COIL|26154.4 kg|BA Finish
</table_stream>`;

        function renderTable(tableData) {
            if (!tableData.isValid || !tableData.headers.length) {
                return '<p style="color: #666; font-style: italic;">Waiting for table data...</p>';
            }

            let html = `<h4>📊 Progressive Table (${tableData.rows.length} rows)</h4>`;
            html += '<table>';
            html += '<thead><tr>';
            tableData.headers.forEach(header => {
                html += `<th>${header}</th>`;
            });
            html += '</tr></thead>';
            html += '<tbody>';
            tableData.rows.forEach(row => {
                html += '<tr>';
                tableData.headers.forEach(header => {
                    html += `<td>${row[header] || ''}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody></table>';
            
            return html;
        }

        function startStreamingTest() {
            resetTest();
            
            const output = document.getElementById('streaming-output');
            const progressBar = document.getElementById('progress-bar');
            
            let charIndex = 0;
            const chunkSize = 5; // Characters per chunk
            
            streamingInterval = setInterval(() => {
                if (charIndex < testData.length) {
                    const chunk = testData.slice(charIndex, charIndex + chunkSize);
                    charIndex += chunkSize;
                    
                    // Add chunk to parser
                    const tableData = parser.addChunk(chunk);
                    
                    // Update progress
                    const progress = Math.round((charIndex / testData.length) * 100);
                    progressBar.style.width = progress + '%';
                    progressBar.textContent = progress + '%';
                    
                    // Render current table state
                    let html = `<h4>Streaming Progress: ${charIndex}/${testData.length} characters</h4>`;
                    html += `<p><strong>Buffer:</strong> ${parser.buffer.length} chars</p>`;
                    html += `<p><strong>Headers parsed:</strong> ${parser.isHeaderParsed ? 'Yes' : 'No'}</p>`;
                    html += `<p><strong>Current headers:</strong> ${tableData.headers.join(', ')}</p>`;
                    html += `<p><strong>Rows parsed:</strong> ${tableData.rows.length}</p>`;
                    html += renderTable(tableData);
                    
                    output.innerHTML = html;
                } else {
                    clearInterval(streamingInterval);
                    progressBar.style.width = '100%';
                    progressBar.textContent = '100% - Complete!';
                    
                    // Final render
                    const finalTableData = parser.addChunk('');
                    let html = '<h4>✅ Streaming Complete!</h4>';
                    html += `<p><strong>Final table:</strong> ${finalTableData.headers.length} columns, ${finalTableData.rows.length} rows</p>`;
                    html += renderTable(finalTableData);
                    
                    output.innerHTML = html;
                }
            }, 100); // Update every 100ms
        }

        function resetTest() {
            if (streamingInterval) {
                clearInterval(streamingInterval);
                streamingInterval = null;
            }
            
            parser.reset();
            
            const output = document.getElementById('streaming-output');
            const progressBar = document.getElementById('progress-bar');
            
            output.innerHTML = '<p>Click "Start Streaming Test" to begin...</p>';
            progressBar.style.width = '0%';
            progressBar.textContent = '0%';
        }
    </script>
</body>
</html>
