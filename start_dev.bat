@echo off
echo Terminating existing frontend and backend processes...

:: Kill any running Node.js processes (frontend)
taskkill /F /IM node.exe /T 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Frontend processes terminated successfully.
) else (
    echo No active frontend processes found.
)

:: Kill any running Python/Uvicorn processes (backend)
taskkill /F /IM python.exe /T 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Backend processes terminated successfully.
) else (
    echo No active backend processes found.
)

echo Starting new development servers...
start cmd /k "cd frontend && npm run dev"
start cmd /k "cd backend && uvicorn main:app --reload"
echo Development servers started.
