# Environment
ENV=production
DEBUG=False

# Aliyun RDS Database
RDS_DATABASE_URL=postgresql://username:<EMAIL>:3433/steel_converter
RDS_MAX_CONNECTIONS=50
RDS_POOL_SIZE=5
RDS_MAX_OVERFLOW=10

# JWT
JWT_SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# Email
SMTP_SERVER=smtp.aliyun.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password

# CORS (comma-separated list of allowed origins)
CORS_ORIGINS=http://localhost:5173,http://localhost:5177,https://your-domain.com

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# 火山引擎 DeepSeek API 
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_API_URL=https://api.volcengine.com/v1/llm/generation
DEEPSEEK_MODEL=deepseek-v2
DEEPSEEK_TIMEOUT=30

# Memory Management
BACKEND_MEMORY_LIMIT=512m
FRONTEND_MEMORY_LIMIT=256m
WORKERS_PER_CORE=1.0
MAX_WORKERS=4
WEB_CONCURRENCY=1
WORKER_CONNECTIONS=1024

# Monitoring
LOG_LEVEL=INFO
SENTRY_DSN=your-sentry-dsn
