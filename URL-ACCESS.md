# Accessing the Application via URL

This guide explains how to make your locally running Steel Unit Converter application accessible via URL, either from your local network or from the internet.

## Prerequisites

- The application is running locally (backend on port 8000, frontend on port 3000)
- You have administrative access to your machine
- For internet access: You have control over your router or can use a tunneling service

## Option 1: Local Network Access

This option allows other devices on your local network to access the application using your machine's local IP address.

### Using the Automated Script

1. Run the provided script with the `--local` option:

```bash
./make-public.sh --local
```

2. The script will:
   - Check if the application is running
   - Install and configure nginx as a reverse proxy
   - Display the URL to access the application

3. Access the application using the provided URL (e.g., http://*************)

### Manual Configuration

If you prefer to configure things manually:

1. Find your local IP address:
   ```bash
   ip addr show | grep -E "inet .* global" | grep -v docker
   ```

2. Install nginx:
   ```bash
   sudo apt-get update && sudo apt-get install -y nginx
   ```

3. Create an nginx configuration file:
   ```bash
   sudo nano /etc/nginx/conf.d/steelnet.conf
   ```

4. Add the following configuration:
   ```
   server {
       listen 80;
       server_name _;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }

       location /api {
           proxy_pass http://localhost:8000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

5. Test and restart nginx:
   ```bash
   sudo nginx -t
   sudo systemctl restart nginx
   ```

6. Access the application using your local IP address (e.g., http://*************)

## Option 2: Internet Access via ngrok

This option allows access to the application from anywhere on the internet using a tunneling service called ngrok.

### Using the Automated Script

1. Run the provided script with the `--ngrok` option:

```bash
./make-public.sh --ngrok
```

2. The script will:
   - Check if the application is running
   - Install ngrok if not already installed
   - Create tunnels for both the backend and frontend
   - Display the URLs to access the application

3. Access the application using the provided URLs

### Manual Configuration with ngrok

1. Install ngrok:
   ```bash
   curl -s https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-amd64.tgz -o ngrok.tgz
   tar xvzf ngrok.tgz
   sudo mv ngrok /usr/local/bin/
   ```

2. Sign up for a free ngrok account at https://ngrok.com/

3. Authenticate ngrok with your authtoken:
   ```bash
   ngrok config add-authtoken YOUR_AUTH_TOKEN
   ```

4. Create tunnels for both the backend and frontend:
   ```bash
   # For the backend
   ngrok http 8000
   
   # For the frontend (in a separate terminal)
   ngrok http 3000
   ```

5. Access the application using the URLs provided by ngrok

## Option 3: Internet Access via Port Forwarding

This option allows access to the application from the internet using your public IP address and port forwarding.

### Prerequisites

- You have access to your router's administration interface
- Your ISP provides you with a public IP address (not behind CGNAT)

### Steps

1. Find your local IP address:
   ```bash
   ip addr show | grep -E "inet .* global" | grep -v docker
   ```

2. Access your router's administration interface (usually http://*********** or http://***********)

3. Find the port forwarding section (may be called "Virtual Server", "Port Forwarding", or similar)

4. Create two port forwarding rules:
   - Forward external port 80 to internal port 3000 on your local IP address (for the frontend)
   - Forward external port 8080 to internal port 8000 on your local IP address (for the backend)

5. Find your public IP address:
   ```bash
   curl https://api.ipify.org
   ```

6. Access the application using your public IP address:
   - Frontend: http://YOUR_PUBLIC_IP
   - Backend API: http://YOUR_PUBLIC_IP:8080

### Optional: Set Up a Domain Name

1. Register a domain name with a domain registrar (e.g., Namecheap, GoDaddy)

2. Set up DNS A records to point to your public IP address:
   - Create an A record for your domain (e.g., steelnet.example.com) pointing to your public IP address

3. Configure nginx to use your domain name:
   ```
   server {
       listen 80;
       server_name steelnet.example.com;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }

       location /api {
           proxy_pass http://localhost:8000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

4. Access the application using your domain name (e.g., http://steelnet.example.com)

## Troubleshooting

### Cannot Access the Application

1. Check if the application is running:
   ```bash
   ./start-prod.sh --status
   ```

2. Check if the ports are open:
   ```bash
   nc -z localhost 8000
   nc -z localhost 3000
   ```

3. Check if nginx is running:
   ```bash
   sudo systemctl status nginx
   ```

4. Check nginx error logs:
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

5. Check firewall settings:
   ```bash
   sudo ufw status
   ```

6. If using a firewall, allow the necessary ports:
   ```bash
   sudo ufw allow 80/tcp
   sudo ufw allow 8080/tcp
   ```

### ngrok Issues

1. Check ngrok logs:
   ```bash
   tail -f logs/ngrok-backend.log
   tail -f logs/ngrok-frontend.log
   ```

2. Restart ngrok:
   ```bash
   pkill ngrok
   ./make-public.sh --ngrok
   ```

3. Check ngrok status:
   ```bash
   curl http://localhost:4040/api/tunnels
   ```

## Security Considerations

When exposing your application to the internet, consider the following security measures:

1. Set up HTTPS with Let's Encrypt for secure connections
2. Implement proper authentication and authorization
3. Use a firewall to restrict access to necessary ports only
4. Regularly update your software and dependencies
5. Monitor access logs for suspicious activity
