#!/bin/bash

# Script to make the application accessible from the internet
# This script provides options for port forwarding and tunneling

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
LOG_DIR="$SCRIPT_DIR/logs"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to get public IP address
get_public_ip() {
    echo "Getting public IP address..."
    PUBLIC_IP=$(curl -s https://api.ipify.org)
    if [ -z "$PUBLIC_IP" ]; then
        echo "Failed to get public IP address. Trying alternative method..."
        PUBLIC_IP=$(curl -s https://ifconfig.me)
    fi
    
    if [ -z "$PUBLIC_IP" ]; then
        echo "Failed to get public IP address. Please check your internet connection."
        return 1
    fi
    
    echo "Your public IP address is: $PUBLIC_IP"
    return 0
}

# Function to get local IP address
get_local_ip() {
    echo "Getting local IP address..."
    if command -v ip &> /dev/null; then
        LOCAL_IP=$(ip addr show | grep -E "inet .* global" | grep -v docker | awk '{print $2}' | cut -d/ -f1 | head -n 1)
    elif command -v ifconfig &> /dev/null; then
        LOCAL_IP=$(ifconfig | grep -E "inet .* broadcast" | awk '{print $2}' | head -n 1)
    else
        echo "Failed to get local IP address. Please install 'ip' or 'ifconfig'."
        return 1
    fi
    
    if [ -z "$LOCAL_IP" ]; then
        echo "Failed to get local IP address."
        return 1
    fi
    
    echo "Your local IP address is: $LOCAL_IP"
    return 0
}

# Function to check if ports are open
check_ports() {
    echo "Checking if ports 8000 and 3000 are open..."
    
    # Check if netcat is installed
    if ! command -v nc &> /dev/null; then
        echo "netcat (nc) is not installed. Installing..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y netcat
        elif command -v yum &> /dev/null; then
            sudo yum install -y nc
        else
            echo "Failed to install netcat. Please install it manually."
            return 1
        fi
    fi
    
    # Check if port 8000 is open
    if nc -z localhost 8000; then
        echo "Port 8000 (backend) is open."
    else
        echo "Port 8000 (backend) is not open. Please start the backend first."
        return 1
    fi
    
    # Check if port 3000 is open
    if nc -z localhost 3000; then
        echo "Port 3000 (frontend) is open."
    else
        echo "Port 3000 (frontend) is not open. Please start the frontend first."
        return 1
    fi
    
    return 0
}

# Function to install and run ngrok
setup_ngrok() {
    echo "Setting up ngrok for tunneling..."
    
    # Check if ngrok is installed
    if ! command -v ngrok &> /dev/null; then
        echo "ngrok is not installed. Installing..."
        
        # Download and install ngrok
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y unzip
        elif command -v yum &> /dev/null; then
            sudo yum install -y unzip
        fi
        
        # Download ngrok
        curl -s https://bin.equinox.io/c/bNyj1mQVY4c/ngrok-v3-stable-linux-amd64.tgz -o ngrok.tgz
        tar xvzf ngrok.tgz -C /tmp
        sudo mv /tmp/ngrok /usr/local/bin/
        rm ngrok.tgz
        
        echo "ngrok installed successfully."
    fi
    
    # Check if ngrok is authenticated
    if ! ngrok config check &> /dev/null; then
        echo "ngrok is not authenticated. Please visit https://dashboard.ngrok.com/get-started/your-authtoken"
        echo "to get your authtoken, then enter it below:"
        read -p "Authtoken: " NGROK_AUTHTOKEN
        ngrok config add-authtoken $NGROK_AUTHTOKEN
    fi
    
    # Start ngrok for backend
    echo "Starting ngrok tunnel for backend (port 8000)..."
    nohup ngrok http 8000 > "$LOG_DIR/ngrok-backend.log" 2>&1 &
    NGROK_BACKEND_PID=$!
    echo $NGROK_BACKEND_PID > "$LOG_DIR/ngrok-backend.pid"
    
    # Start ngrok for frontend
    echo "Starting ngrok tunnel for frontend (port 3000)..."
    nohup ngrok http 3000 > "$LOG_DIR/ngrok-frontend.log" 2>&1 &
    NGROK_FRONTEND_PID=$!
    echo $NGROK_FRONTEND_PID > "$LOG_DIR/ngrok-frontend.pid"
    
    # Wait for ngrok to start
    sleep 5
    
    # Get ngrok URLs
    NGROK_BACKEND_URL=$(curl -s http://localhost:4040/api/tunnels | grep -o '"public_url":"[^"]*"' | grep -o 'http[^"]*' | head -n 1)
    NGROK_FRONTEND_URL=$(curl -s http://localhost:4041/api/tunnels | grep -o '"public_url":"[^"]*"' | grep -o 'http[^"]*' | head -n 1)
    
    if [ -z "$NGROK_BACKEND_URL" ] || [ -z "$NGROK_FRONTEND_URL" ]; then
        echo "Failed to get ngrok URLs. Please check the logs at $LOG_DIR/ngrok-*.log"
        return 1
    fi
    
    echo "ngrok tunnels created successfully."
    echo "Backend URL: $NGROK_BACKEND_URL"
    echo "Frontend URL: $NGROK_FRONTEND_URL"
    
    return 0
}

# Function to create nginx configuration for local network access
setup_nginx_local() {
    echo "Setting up nginx for local network access..."
    
    # Check if nginx is installed
    if ! command -v nginx &> /dev/null; then
        echo "nginx is not installed. Installing..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y nginx
        elif command -v yum &> /dev/null; then
            sudo yum install -y nginx
        else
            echo "Failed to install nginx. Please install it manually."
            return 1
        fi
    fi
    
    # Create nginx configuration
    echo "Creating nginx configuration..."
    cat > /tmp/steelnet.conf << EOF
server {
    listen 80;
    server_name _;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }

    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF
    
    # Copy nginx configuration
    sudo cp /tmp/steelnet.conf /etc/nginx/conf.d/steelnet.conf
    
    # Test nginx configuration
    echo "Testing nginx configuration..."
    sudo nginx -t
    
    if [ $? -ne 0 ]; then
        echo "nginx configuration test failed. Please check the configuration manually."
        return 1
    fi
    
    # Restart nginx
    echo "Restarting nginx..."
    sudo systemctl restart nginx || sudo service nginx restart
    
    # Get local IP address
    get_local_ip
    
    echo "nginx configured successfully."
    echo "You can access the application at:"
    echo "  Frontend: http://$LOCAL_IP"
    echo "  Backend API: http://$LOCAL_IP/api"
    
    return 0
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --local       Configure for local network access only"
    echo "  --ngrok       Use ngrok for public internet access"
    echo "  --help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --local    Configure for local network access"
    echo "  $0 --ngrok    Use ngrok for public internet access"
}

# Main function
main() {
    # Process command line arguments
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --local)
                # Check if ports are open
                check_ports || exit 1
                
                # Setup nginx for local network access
                setup_nginx_local
                ;;
            --ngrok)
                # Check if ports are open
                check_ports || exit 1
                
                # Setup ngrok for public internet access
                setup_ngrok
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                echo "Use --help for usage information."
                exit 1
                ;;
        esac
        shift
    done
}

# Run the main function with all command line arguments
main "$@"
