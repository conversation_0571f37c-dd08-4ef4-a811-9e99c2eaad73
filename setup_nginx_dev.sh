#!/usr/bin/env bash

# Setup Nginx for Steel Unit Converter Development Environment
# This script installs and configures Nginx for development use with URL access

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
LOG_DIR="$SCRIPT_DIR/logs"
SSL_DIR="$SCRIPT_DIR/ssl"

# Create logs and SSL directories if they don't exist
mkdir -p "$LOG_DIR"
mkdir -p "$SSL_DIR"

# Set text colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored messages
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "\n${BLUE}==== $1 ====${NC}"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Function to install Nginx
install_nginx() {
    print_header "Installing Nginx"

    if command_exists apt-get; then
        print_info "Using apt-get to install Nginx..."
        sudo apt-get update && sudo apt-get install -y nginx
        return $?
    elif command_exists yum; then
        print_info "Using yum to install Nginx..."
        sudo yum install -y nginx
        return $?
    elif command_exists dnf; then
        print_info "Using dnf to install Nginx..."
        sudo dnf install -y nginx
        return $?
    elif command_exists brew; then
        print_info "Using brew to install Nginx..."
        brew install nginx
        return $?
    elif command_exists port; then
        print_info "Using MacPorts to install Nginx..."
        sudo port install nginx
        return $?
    else
        print_error "Could not find a package manager to install Nginx."
        print_info "You may need to install Nginx manually. Here are some common methods:"
        print_info "  - Debian/Ubuntu: sudo apt-get install nginx"
        print_info "  - CentOS/RHEL: sudo yum install nginx"
        print_info "  - Fedora: sudo dnf install nginx"
        print_info "  - macOS (Homebrew): brew install nginx"
        print_info "  - macOS (MacPorts): sudo port install nginx"

        # Ask if user wants to try a manual installation
        read -p "Would you like to try a manual installation? (y/n) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_info "Attempting manual installation..."

            # Try to determine the OS
            if [ -f "/etc/os-release" ]; then
                . /etc/os-release
                OS_NAME=$NAME
                print_info "Detected OS: $OS_NAME"

                case "$OS_NAME" in
                    *Ubuntu*|*Debian*)
                        print_info "Trying apt-get install..."
                        sudo apt-get update && sudo apt-get install -y nginx
                        return $?
                        ;;
                    *CentOS*|*Red\ Hat*|*RHEL*)
                        print_info "Trying yum install..."
                        sudo yum install -y nginx
                        return $?
                        ;;
                    *Fedora*)
                        print_info "Trying dnf install..."
                        sudo dnf install -y nginx
                        return $?
                        ;;
                    *)
                        print_error "Unsupported OS: $OS_NAME"
                        return 1
                        ;;
                esac
            elif [ "$(uname)" == "Darwin" ]; then
                print_info "Detected macOS"
                if command_exists curl; then
                    print_info "Trying to install Homebrew..."
                    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
                    if command_exists brew; then
                        print_info "Installing nginx with Homebrew..."
                        brew install nginx
                        return $?
                    else
                        print_error "Failed to install Homebrew"
                        return 1
                    fi
                else
                    print_error "curl not found, cannot install Homebrew"
                    return 1
                fi
            else
                print_error "Could not determine OS"
                return 1
            fi
        else
            print_info "Manual installation skipped."
            return 1
        fi
    fi
}

# Function to generate SSL certificates
generate_ssl_certs() {
    print_header "Generating SSL Certificates"

    # Get public IP address
    PUBLIC_IP=$(curl -s ifconfig.me || curl -s icanhazip.com || curl -s ipecho.net/plain || echo "localhost")
    print_info "Detected public IP: $PUBLIC_IP"

    # Check if certificates already exist
    if [ -f "$SSL_DIR/cert.pem" ] && [ -f "$SSL_DIR/key.pem" ]; then
        print_info "SSL certificates already exist."

        # Check if the certificate includes the current public IP
        if openssl x509 -in "$SSL_DIR/cert.pem" -text | grep -q "$PUBLIC_IP"; then
            print_success "Certificate already includes the current public IP."
            return 0
        else
            print_warning "Certificate does not include the current public IP."
            read -p "Do you want to regenerate the certificate with the current IP? (y/n) " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_info "Using existing certificate."
                return 0
            fi
        fi
    fi

    print_info "Generating self-signed SSL certificate..."
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout "$SSL_DIR/key.pem" \
        -out "$SSL_DIR/cert.pem" \
        -subj "/CN=steelnet.ai" \
        -addext "subjectAltName=DNS:steelnet.ai,DNS:localhost,IP:$PUBLIC_IP"

    if [ $? -eq 0 ]; then
        print_success "SSL certificates generated successfully."
        return 0
    else
        print_error "Failed to generate SSL certificates."
        return 1
    fi
}

# Function to create Nginx configuration
create_nginx_config() {
    print_header "Creating Nginx Configuration"

    # Get public IP address if not already set
    if [ -z "$PUBLIC_IP" ]; then
        PUBLIC_IP=$(curl -s ifconfig.me || curl -s icanhazip.com || curl -s ipecho.net/plain || echo "localhost")
        print_info "Detected public IP: $PUBLIC_IP"
    fi

    # Determine Nginx configuration directory and style
    NGINX_CONF_DIR=""
    NGINX_CONF_STYLE=""

    # Check for Debian/Ubuntu style (sites-available + sites-enabled)
    if [ -d "/etc/nginx/sites-available" ] && [ -d "/etc/nginx/sites-enabled" ]; then
        NGINX_CONF_DIR="/etc/nginx/sites-available"
        NGINX_CONF_STYLE="debian"
        print_info "Detected Debian/Ubuntu style Nginx configuration"
    # Check for CentOS/RHEL style (conf.d)
    elif [ -d "/etc/nginx/conf.d" ]; then
        NGINX_CONF_DIR="/etc/nginx/conf.d"
        NGINX_CONF_STYLE="centos"
        print_info "Detected CentOS/RHEL style Nginx configuration"
    # Check for macOS/FreeBSD style with Homebrew
    elif [ -d "/opt/homebrew/etc/nginx/conf.d" ]; then
        NGINX_CONF_DIR="/opt/homebrew/etc/nginx/conf.d"
        NGINX_CONF_STYLE="macos"
        print_info "Detected macOS Homebrew style Nginx configuration"
    elif [ -d "/opt/homebrew/etc/nginx/servers" ]; then
        NGINX_CONF_DIR="/opt/homebrew/etc/nginx/servers"
        NGINX_CONF_STYLE="macos"
        print_info "Detected macOS Homebrew style Nginx configuration"
    # Check for macOS/FreeBSD style with /usr/local
    elif [ -d "/usr/local/etc/nginx/conf.d" ]; then
        NGINX_CONF_DIR="/usr/local/etc/nginx/conf.d"
        NGINX_CONF_STYLE="macos"
        print_info "Detected macOS/FreeBSD style Nginx configuration"
    elif [ -d "/usr/local/etc/nginx/servers" ]; then
        NGINX_CONF_DIR="/usr/local/etc/nginx/servers"
        NGINX_CONF_STYLE="macos"
        print_info "Detected macOS/FreeBSD style Nginx configuration"
    # Fallback to a directory we can create
    else
        print_warning "Could not find standard Nginx configuration directory."

        # Try to find nginx executable to determine installation path
        NGINX_PATH=$(which nginx 2>/dev/null)
        if [ -n "$NGINX_PATH" ]; then
            NGINX_PREFIX=$(dirname $(dirname $NGINX_PATH))
            print_info "Nginx found at: $NGINX_PATH, prefix: $NGINX_PREFIX"

            # Try to create conf.d directory
            if [ -d "$NGINX_PREFIX/etc/nginx" ]; then
                NGINX_CONF_DIR="$NGINX_PREFIX/etc/nginx/conf.d"
                NGINX_CONF_STYLE="custom"
                print_info "Using custom Nginx configuration directory: $NGINX_CONF_DIR"
                mkdir -p "$NGINX_CONF_DIR"
            elif [ -d "/etc/nginx" ]; then
                NGINX_CONF_DIR="/etc/nginx/conf.d"
                NGINX_CONF_STYLE="custom"
                print_info "Using default Nginx configuration directory: $NGINX_CONF_DIR"
                mkdir -p "$NGINX_CONF_DIR"
            else
                print_error "Could not determine Nginx configuration directory."
                return 1
            fi
        else
            print_error "Nginx executable not found. Is Nginx installed correctly?"
            return 1
        fi
    fi

    print_info "Using Nginx configuration directory: $NGINX_CONF_DIR"

    # Create Nginx configuration file
    print_info "Creating Nginx configuration file..."
    cat > /tmp/steelnet-dev.conf << EOF
# Steel Unit Converter Development Nginx Configuration

# HTTP server - redirect to HTTPS
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name _;

    # Redirect all HTTP requests to HTTPS
    return 301 https://\$host\$request_uri;
}

# HTTPS server for steelnet.ai
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name steelnet.ai;

    # SSL configuration
    ssl_certificate $SSL_DIR/cert.pem;
    ssl_certificate_key $SSL_DIR/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';

    # SSL optimizations
    ssl_session_cache shared:SSL:2m;
    ssl_session_timeout 10m;

    # Frontend proxy for development server
    location / {
        proxy_pass http://localhost:5173;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;

        # WebSocket support for hot module replacement
        proxy_read_timeout 86400;
    }

    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000/api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /auth {
        proxy_pass http://localhost:8000/auth;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /conversion {
        proxy_pass http://localhost:8000/conversion;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /health {
        proxy_pass http://localhost:8000/health;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}

# HTTPS server for IP address
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name $PUBLIC_IP;

    # SSL configuration
    ssl_certificate $SSL_DIR/cert.pem;
    ssl_certificate_key $SSL_DIR/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';

    # SSL optimizations
    ssl_session_cache shared:SSL:2m;
    ssl_session_timeout 10m;

    # Frontend proxy for development server
    location / {
        proxy_pass http://localhost:5173;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;

        # WebSocket support for hot module replacement
        proxy_read_timeout 86400;
    }

    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000/api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /auth {
        proxy_pass http://localhost:8000/auth;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /conversion {
        proxy_pass http://localhost:8000/conversion;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /health {
        proxy_pass http://localhost:8000/health;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

    # Copy configuration file to Nginx directory
    print_info "Copying configuration file to Nginx directory..."

    # Use sudo if available, otherwise try without it
    SUDO_CMD=""
    if command_exists sudo; then
        SUDO_CMD="sudo"
    fi

    case "$NGINX_CONF_STYLE" in
        debian)
            # Debian/Ubuntu style
            $SUDO_CMD mkdir -p "$NGINX_CONF_DIR" "/etc/nginx/sites-enabled"
            $SUDO_CMD cp /tmp/steelnet-dev.conf "$NGINX_CONF_DIR/steelnet-dev"

            # Create symlink in sites-enabled if it doesn't exist
            if [ ! -f "/etc/nginx/sites-enabled/steelnet-dev" ]; then
                print_info "Creating symlink in sites-enabled..."
                $SUDO_CMD ln -sf "$NGINX_CONF_DIR/steelnet-dev" "/etc/nginx/sites-enabled/steelnet-dev"
            fi
            ;;
        centos|macos)
            # CentOS/RHEL/macOS style
            $SUDO_CMD mkdir -p "$NGINX_CONF_DIR"
            $SUDO_CMD cp /tmp/steelnet-dev.conf "$NGINX_CONF_DIR/steelnet-dev.conf"
            ;;
        custom)
            # Custom style - try both approaches
            $SUDO_CMD mkdir -p "$NGINX_CONF_DIR"
            $SUDO_CMD cp /tmp/steelnet-dev.conf "$NGINX_CONF_DIR/steelnet-dev.conf"

            # If sites-enabled exists, also create a symlink there
            if [ -d "/etc/nginx/sites-enabled" ]; then
                print_info "Creating symlink in sites-enabled..."
                $SUDO_CMD ln -sf "$NGINX_CONF_DIR/steelnet-dev.conf" "/etc/nginx/sites-enabled/steelnet-dev"
            fi
            ;;
        *)
            # Fallback
            print_warning "Unknown Nginx configuration style. Trying default approach..."
            $SUDO_CMD mkdir -p "$NGINX_CONF_DIR"
            $SUDO_CMD cp /tmp/steelnet-dev.conf "$NGINX_CONF_DIR/steelnet-dev.conf"
            ;;
    esac

    return $?
}

# Function to optimize Nginx configuration
optimize_nginx_config() {
    print_header "Optimizing Nginx Configuration"

    # Check if we can modify the main nginx.conf
    NGINX_MAIN_CONF=""
    if [ -f "/etc/nginx/nginx.conf" ]; then
        NGINX_MAIN_CONF="/etc/nginx/nginx.conf"
    elif [ -f "/usr/local/etc/nginx/nginx.conf" ]; then
        NGINX_MAIN_CONF="/usr/local/etc/nginx/nginx.conf"
    elif [ -f "/opt/homebrew/etc/nginx/nginx.conf" ]; then
        NGINX_MAIN_CONF="/opt/homebrew/etc/nginx/nginx.conf"
    else
        print_warning "Could not find main Nginx configuration file. Skipping optimization."
        return 0
    fi

    print_info "Found main Nginx configuration at: $NGINX_MAIN_CONF"

    # Backup original configuration
    print_info "Backing up original configuration..."
    sudo cp "$NGINX_MAIN_CONF" "$NGINX_MAIN_CONF.bak.$(date +%Y%m%d%H%M%S)"

    # Check if we're on a low memory system
    if command_exists free; then
        TOTAL_MEMORY_MB=$(free -m 2>/dev/null | awk '/^Mem:/{print $2}')
    elif command_exists sysctl; then
        TOTAL_MEMORY_MB=$(sysctl -n hw.memsize 2>/dev/null | awk '{print int($1/1024/1024)}')
    else
        # Default to 4GB if we can't detect
        TOTAL_MEMORY_MB=4096
    fi

    # Make sure we have a valid number
    if [[ ! "$TOTAL_MEMORY_MB" =~ ^[0-9]+$ ]]; then
        TOTAL_MEMORY_MB=4096
    fi

    print_info "Total system memory: $TOTAL_MEMORY_MB MB"

    if [ "$TOTAL_MEMORY_MB" -lt 2048 ]; then
        print_info "Low memory system detected. Applying memory-optimized configuration."
        WORKER_PROCESSES=1
        WORKER_CONNECTIONS=512
    else
        print_info "Standard memory system detected. Applying standard configuration."
        WORKER_PROCESSES=2
        WORKER_CONNECTIONS=1024
    fi

    # Detect the correct nginx user
    NGINX_USER="nginx"

    # Check if we're on macOS with Homebrew
    if command_exists brew && [ -f "/opt/homebrew/bin/nginx" ]; then
        # Get the user from nginx -V output if possible
        NGINX_USER_FROM_CONFIG=$(nginx -V 2>&1 | grep "configure arguments:" | sed -n 's/.*--user=\([^ ]*\).*/\1/p')
        if [ -n "$NGINX_USER_FROM_CONFIG" ]; then
            NGINX_USER="$NGINX_USER_FROM_CONFIG"
        else
            # Default for macOS Homebrew
            NGINX_USER="nobody"
        fi
    # Standard Linux user detection
    elif grep -q "^www-data:" /etc/passwd; then
        NGINX_USER="www-data"  # Debian/Ubuntu default
    elif grep -q "^nginx:" /etc/passwd; then
        NGINX_USER="nginx"     # CentOS/RHEL default
    elif grep -q "^_www:" /etc/passwd; then
        NGINX_USER="www"       # macOS default
    elif grep -q "^http:" /etc/passwd; then
        NGINX_USER="http"      # Arch Linux default
    elif grep -q "^nobody:" /etc/passwd; then
        NGINX_USER="nobody"    # Fallback option
    fi

    print_info "Using nginx user: $NGINX_USER"

    # Detect the correct pid file location
    NGINX_PID_FILE="/var/run/nginx.pid"

    # Check if we're using Homebrew on macOS
    if command_exists brew && [ -f "/opt/homebrew/bin/nginx" ]; then
        # Try to get the pid file from nginx -V output
        NGINX_PREFIX=$(nginx -V 2>&1 | grep "configure arguments:" | sed -n 's/.*--prefix=\([^ ]*\).*/\1/p')
        if [ -n "$NGINX_PREFIX" ]; then
            if [ -d "$NGINX_PREFIX/logs" ]; then
                NGINX_PID_FILE="$NGINX_PREFIX/logs/nginx.pid"
            elif [ -d "$NGINX_PREFIX/var/run" ]; then
                NGINX_PID_FILE="$NGINX_PREFIX/var/run/nginx.pid"
            fi
        else
            # Default Homebrew locations
            if [ -d "/opt/homebrew/var/run" ]; then
                NGINX_PID_FILE="/opt/homebrew/var/run/nginx.pid"
            elif [ -d "/usr/local/var/run" ]; then
                NGINX_PID_FILE="/usr/local/var/run/nginx.pid"
            fi
        fi
    # Standard Linux locations
    elif [ -d "/run" ]; then
        NGINX_PID_FILE="/run/nginx.pid"  # Modern Linux distributions
    elif [ -d "/var/run" ]; then
        NGINX_PID_FILE="/var/run/nginx.pid"  # Traditional location
    fi

    print_info "Using nginx pid file: $NGINX_PID_FILE"

    # Detect the correct mime.types file location
    NGINX_MIME_TYPES="/etc/nginx/mime.types"
    if [ -f "/etc/nginx/mime.types" ]; then
        NGINX_MIME_TYPES="/etc/nginx/mime.types"  # Most Linux distributions
    elif [ -f "/opt/homebrew/etc/nginx/mime.types" ]; then
        NGINX_MIME_TYPES="/opt/homebrew/etc/nginx/mime.types"  # macOS Homebrew
    elif [ -f "/usr/local/etc/nginx/mime.types" ]; then
        NGINX_MIME_TYPES="/usr/local/etc/nginx/mime.types"  # macOS /usr/local
    fi

    print_info "Using nginx mime.types file: $NGINX_MIME_TYPES"

    # Create optimized nginx.conf
    print_info "Creating optimized nginx.conf..."
    cat > /tmp/nginx.conf << EOF
user $NGINX_USER;
worker_processes $WORKER_PROCESSES;
worker_rlimit_nofile 8192;
pid $NGINX_PID_FILE;

events {
    worker_connections $WORKER_CONNECTIONS;
    multi_accept on;
}

http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # MIME types
    include $NGINX_MIME_TYPES;
    default_type application/octet-stream;

    # Logging settings
    # Use file-based logging for better compatibility
    error_log logs/error.log;
    access_log logs/access.log;

    # Gzip settings
    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Virtual Host Configs
    include /etc/nginx/conf.d/*.conf;
    include /usr/local/etc/nginx/conf.d/*.conf;
    include /opt/homebrew/etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
    include /usr/local/etc/nginx/sites-enabled/*;
    include /opt/homebrew/etc/nginx/sites-enabled/*;
}
EOF

    # Check if we should update the main configuration
    read -p "Do you want to update the main Nginx configuration? (y/n) " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Updating main Nginx configuration..."
        sudo cp /tmp/nginx.conf "$NGINX_MAIN_CONF"
    else
        print_info "Skipping main Nginx configuration update."
    fi

    return 0
}

# Function to test and reload Nginx
test_and_reload_nginx() {
    print_header "Testing and Reloading Nginx"

    # Use sudo if available, otherwise try without it
    SUDO_CMD=""
    if command_exists sudo; then
        SUDO_CMD="sudo"
    fi

    print_info "Testing Nginx configuration..."
    $SUDO_CMD nginx -t

    if [ $? -eq 0 ]; then
        print_success "Nginx configuration is valid."

        # Check if nginx is running before trying to reload
        if pgrep -x "nginx" > /dev/null; then
            print_info "Nginx is running. Reloading configuration..."
            if command_exists systemctl && systemctl list-units --type=service | grep -q nginx; then
                $SUDO_CMD systemctl reload nginx
            elif command_exists service && service --status-all 2>&1 | grep -q nginx; then
                $SUDO_CMD service nginx reload
            else
                $SUDO_CMD nginx -s reload
            fi

            if [ $? -eq 0 ]; then
                print_success "Nginx reloaded successfully."
                return 0
            else
                print_error "Failed to reload Nginx."
                print_info "Attempting to start Nginx instead..."
            fi
        else
            print_warning "Nginx is not running. Attempting to start..."
        fi

        # Try to start nginx
        if command_exists systemctl && systemctl list-units --type=service | grep -q nginx; then
            print_info "Using systemd to start Nginx..."

            # Check if there are any conflicting configurations
            print_info "Checking for conflicting configurations..."
            CONFLICTING_CONFIGS=$($SUDO_CMD find /etc/nginx/conf.d /etc/nginx/sites-enabled -type f -name "*.conf" 2>/dev/null | grep -v "steelnet-dev")

            if [ -n "$CONFLICTING_CONFIGS" ]; then
                print_warning "Found potentially conflicting nginx configurations:"
                echo "$CONFLICTING_CONFIGS"
                print_info "Temporarily disabling conflicting configurations..."

                for config in $CONFLICTING_CONFIGS; do
                    if [ -f "$config" ]; then
                        print_info "Backing up and disabling: $config"
                        $SUDO_CMD mv "$config" "${config}.bak"
                    fi
                done
            fi

            # Restart nginx service
            print_info "Starting nginx service..."
            $SUDO_CMD systemctl restart nginx

            # Check if it started successfully
            if systemctl is-active --quiet nginx; then
                print_success "Nginx service started successfully."
            else
                print_warning "Nginx service failed to start. Checking logs..."
                $SUDO_CMD journalctl -xeu nginx.service --no-pager | tail -n 20

                # Try direct start as fallback
                print_info "Trying direct start as fallback..."
                $SUDO_CMD nginx
            fi
        elif command_exists service && service --status-all 2>&1 | grep -q nginx; then
            print_info "Using service to start Nginx..."
            $SUDO_CMD service nginx restart
        elif command_exists brew && [ -f "/opt/homebrew/bin/nginx" ]; then
            # macOS with Homebrew
            print_info "Using Homebrew to start Nginx..."
            # Try to start nginx directly first (faster and more reliable)
            print_info "Attempting to start nginx directly first..."
            $SUDO_CMD /opt/homebrew/bin/nginx

            if [ $? -ne 0 ]; then
                print_info "Direct start failed, trying brew services..."
                brew services start nginx || true
            fi
        else
            # Direct start
            print_info "Starting Nginx directly..."
            $SUDO_CMD nginx
        fi

        # Check if nginx started successfully
        sleep 2
        if pgrep -x "nginx" > /dev/null; then
            print_success "Nginx started successfully."
            return 0
        else
            print_error "Failed to start Nginx."
            print_info "You may need to start Nginx manually with: sudo nginx"
            return 1
        fi
    else
        print_error "Nginx configuration is invalid."

        # Try to provide more helpful information
        print_info "Checking for common issues..."

        # Check if the nginx user exists
        if ! grep -q "^$NGINX_USER:" /etc/passwd; then
            print_warning "The configured nginx user '$NGINX_USER' does not exist."
            print_info "You may need to create this user or modify the configuration to use a different user."
            print_info "Try running the script again with a different user."
        fi

        # Check if the pid directory exists
        PID_DIR=$(dirname "$NGINX_PID_FILE")
        if [ ! -d "$PID_DIR" ]; then
            print_warning "The PID directory '$PID_DIR' does not exist."
            print_info "You may need to create this directory or modify the configuration to use a different directory."
        fi

        # Check if the mime.types file exists
        if [ ! -f "$NGINX_MIME_TYPES" ]; then
            print_warning "The mime.types file '$NGINX_MIME_TYPES' does not exist."
            print_info "You may need to install nginx properly or modify the configuration to use a different file."
        fi

        return 1
    fi
}

# Function to check if Nginx is running
check_nginx_running() {
    print_header "Checking Nginx Status"

    # First try to use systemctl if available
    if command_exists systemctl && systemctl list-units --type=service | grep -q nginx; then
        $SUDO_CMD systemctl status nginx || true

        if systemctl is-active --quiet nginx; then
            print_success "Nginx service is running."
            return 0
        else
            print_warning "Nginx service is not running. Checking for processes..."
        fi
    # Then try service command
    elif command_exists service && service --status-all 2>&1 | grep -q nginx; then
        $SUDO_CMD service nginx status || true

        if service nginx status >/dev/null 2>&1; then
            print_success "Nginx service is running."
            return 0
        else
            print_warning "Nginx service is not running. Checking for processes..."
        fi
    fi

    # Finally, check for nginx processes directly
    if pgrep -x "nginx" > /dev/null; then
        print_info "Nginx processes found:"
        ps aux | grep nginx | grep -v grep
        print_success "Nginx is running."
        return 0
    else
        print_error "Nginx is not running."
        return 1
    fi
}

# Function to find nginx binary
find_nginx_binary() {
    # Check standard locations
    NGINX_LOCATIONS=(
        "/usr/sbin/nginx"
        "/usr/local/sbin/nginx"
        "/usr/bin/nginx"
        "/usr/local/bin/nginx"
        "/opt/nginx/sbin/nginx"
        "/opt/local/sbin/nginx"
        "/opt/homebrew/bin/nginx"
    )

    for location in "${NGINX_LOCATIONS[@]}"; do
        if [ -x "$location" ]; then
            print_success "Found nginx binary at: $location"
            return 0
        fi
    done

    # Try to find using which
    NGINX_PATH=$(which nginx 2>/dev/null)
    if [ -n "$NGINX_PATH" ]; then
        print_success "Found nginx binary at: $NGINX_PATH"
        return 0
    fi

    # Try to find using find command
    if command_exists find; then
        print_info "Searching for nginx binary using find command..."
        NGINX_PATH=$(find /usr -name nginx -type f -executable 2>/dev/null | head -n 1)
        if [ -n "$NGINX_PATH" ]; then
            print_success "Found nginx binary at: $NGINX_PATH"
            return 0
        fi
    fi

    print_error "Could not find nginx binary."
    return 1
}

# Main function
main() {
    print_header "Setting up Nginx for Steel Unit Converter Development"

    # Check if Nginx is installed
    if ! command_exists nginx; then
        print_warning "Nginx is not installed or not in PATH."

        # Try to find nginx binary in common locations
        if find_nginx_binary; then
            print_info "Nginx found but not in PATH. Proceeding with setup."
            # Add nginx to PATH temporarily
            if [ -n "$NGINX_PATH" ]; then
                export PATH="$(dirname "$NGINX_PATH"):$PATH"
                print_info "Added $(dirname "$NGINX_PATH") to PATH."
            fi
        else
            # Ask to install nginx
            read -p "Would you like to install Nginx? (y/n) " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                install_nginx
                if [ $? -ne 0 ]; then
                    print_error "Failed to install Nginx."

                    # Ask if user wants to continue without nginx
                    read -p "Would you like to continue setup without installing Nginx? (y/n) " -n 1 -r
                    echo
                    if [[ $REPLY =~ ^[Yy]$ ]]; then
                        print_warning "Continuing without Nginx. Some features may not work."
                    else
                        print_info "Setup aborted."
                        exit 1
                    fi
                fi
            else
                print_info "Nginx installation skipped."

                # Ask if user wants to continue without nginx
                read -p "Would you like to continue setup without installing Nginx? (y/n) " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    print_warning "Continuing without Nginx. Some features may not work."
                else
                    print_info "Setup aborted."
                    exit 0
                fi
            fi
        fi
    else
        print_success "Nginx is already installed."
        NGINX_VERSION=$(nginx -v 2>&1)
        print_info "Nginx version: $NGINX_VERSION"
    fi

    # Generate SSL certificates
    generate_ssl_certs
    if [ $? -ne 0 ]; then
        print_error "Failed to generate SSL certificates."
        exit 1
    fi

    # Create logs directory for nginx
    if command_exists brew && [ -f "/opt/homebrew/bin/nginx" ]; then
        NGINX_PREFIX=$(nginx -V 2>&1 | grep "configure arguments:" | sed -n 's/.*--prefix=\([^ ]*\).*/\1/p')
        if [ -n "$NGINX_PREFIX" ]; then
            print_info "Creating logs directory at $NGINX_PREFIX/logs"
            mkdir -p "$NGINX_PREFIX/logs"
            chmod 755 "$NGINX_PREFIX/logs"
        fi

        # Also check Cellar directory
        if [ -d "/opt/homebrew/Cellar/nginx" ]; then
            NGINX_CELLAR=$(ls -d /opt/homebrew/Cellar/nginx/* | sort -V | tail -n 1)
            if [ -n "$NGINX_CELLAR" ]; then
                print_info "Creating logs directory at $NGINX_CELLAR/logs"
                mkdir -p "$NGINX_CELLAR/logs"
                chmod 755 "$NGINX_CELLAR/logs"
            fi
        fi
    fi

    # Create Nginx configuration
    create_nginx_config
    if [ $? -ne 0 ]; then
        print_error "Failed to create Nginx configuration."
        exit 1
    fi

    # Optimize Nginx configuration
    optimize_nginx_config

    # Test and reload Nginx
    test_and_reload_nginx
    if [ $? -ne 0 ]; then
        print_error "Failed to reload Nginx."
        exit 1
    fi

    # Check if Nginx is running
    check_nginx_running

    # Get public IP address if not already set
    if [ -z "$PUBLIC_IP" ]; then
        PUBLIC_IP=$(curl -s ifconfig.me || curl -s icanhazip.com || curl -s ipecho.net/plain || echo "localhost")
    fi

    print_header "Setup Complete"
    echo "Nginx has been configured for development with URL access."
    echo ""
    echo "You can now access your application at:"
    echo "  Main URL: https://steelnet.ai"
    echo "  Alternative URL: https://$PUBLIC_IP"
    echo ""
    echo "Note: You may need to add 'steelnet.ai' to your hosts file pointing to $PUBLIC_IP"
    echo "      or ensure DNS is properly configured to point to your server."
    echo ""
    echo "To start the application with URL access, run:"
    echo "  ./start_dev_linux_with_url_access.sh --url-access"

    return 0
}

# Run the main function
main
