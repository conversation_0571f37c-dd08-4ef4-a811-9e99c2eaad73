# Cloud Deployment Script for Steel Unit Converter (PowerShell version)
# This script provides utilities to deploy the application to various cloud providers

# Default configuration
$ENV_FILE = ".env"
$DEPLOY_TARGET = "aliyun"  # Default target is Aliyun
$ACTION = "deploy"         # Default action
$DOCKER_REGISTRY = ""      # Docker registry URL (if using container registry)

# Color functions for output
function Write-ColorOutput {
    param (
        [Parameter(Mandatory=$true)]
        [string]$Message,
        
        [Parameter(Mandatory=$false)]
        [string]$ForegroundColor = "White"
    )
    
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Message
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Display help message
function Show-Help {
    Write-ColorOutput "Steel Unit Converter - Cloud Deployment Script" "Cyan"
    Write-Output ""
    Write-Output "Usage: .\cloud-deploy.ps1 [OPTIONS]"
    Write-Output ""
    Write-Output "Options:"
    Write-Output "  -Target <string>    Deployment target (aliyun, aws, azure, gcp)"
    Write-Output "  -Action <string>    Action to perform (deploy, update, destroy)"
    Write-Output "  -EnvFile <string>   Environment file (default: .env)"
    Write-Output "  -Registry <string>  Docker registry URL"
    Write-Output "  -Help               Show this help message"
    Write-Output ""
    Write-Output "Examples:"
    Write-Output "  .\cloud-deploy.ps1 -Target aliyun"
    Write-Output "  .\cloud-deploy.ps1 -Target aws -Action update"
    Write-Output "  .\cloud-deploy.ps1 -Target azure -EnvFile .env.production"
    Write-Output ""
}

# Parse command line parameters
param (
    [Parameter(Mandatory=$false)]
    [string]$Target = $DEPLOY_TARGET,
    
    [Parameter(Mandatory=$false)]
    [string]$Action = $ACTION,
    
    [Parameter(Mandatory=$false)]
    [string]$EnvFile = $ENV_FILE,
    
    [Parameter(Mandatory=$false)]
    [string]$Registry = $DOCKER_REGISTRY,
    
    [Parameter(Mandatory=$false)]
    [switch]$Help
)

# Show help if requested
if ($Help) {
    Show-Help
    exit 0
}

$DEPLOY_TARGET = $Target
$ACTION = $Action
$ENV_FILE = $EnvFile
$DOCKER_REGISTRY = $Registry

# Load environment variables
if (Test-Path $ENV_FILE) {
    Write-ColorOutput "Loading environment from $ENV_FILE" "Green"
    $envContent = Get-Content $ENV_FILE
    foreach ($line in $envContent) {
        if ($line -and !$line.StartsWith('#')) {
            $key, $value = $line.Split('=', 2)
            if ($key -and $value) {
                $key = $key.Trim()
                $value = $value.Trim()
                [Environment]::SetEnvironmentVariable($key, $value, [System.EnvironmentVariableTarget]::Process)
            }
        }
    }
} else {
    Write-ColorOutput "Warning: Environment file $ENV_FILE not found. Using default values." "Yellow"
}

# Check required tools
function Check-Tools {
    Write-ColorOutput "Checking required tools..." "Cyan"
    
    # Check Docker
    try {
        $dockerVersion = docker --version
        if (!$?) { throw "Docker check failed" }
    } catch {
        Write-ColorOutput "Error: Docker is not installed or not in PATH. Please install Docker." "Red"
        exit 1
    }
    
    # Check Docker Compose
    try {
        $dockerComposeVersion = docker-compose --version
        if (!$?) { throw "Docker Compose check failed" }
    } catch {
        Write-ColorOutput "Error: Docker Compose is not installed or not in PATH. Please install Docker Compose." "Red"
        exit 1
    }
    
    # Check for cloud provider-specific tools
    switch ($DEPLOY_TARGET) {
        "aws" {
            try {
                $awsVersion = aws --version
                if (!$?) { throw "AWS CLI check failed" }
            } catch {
                Write-ColorOutput "Error: AWS CLI is not installed or not in PATH. Please install AWS CLI." "Red"
                exit 1
            }
        }
        "azure" {
            try {
                $azVersion = az --version
                if (!$?) { throw "Azure CLI check failed" }
            } catch {
                Write-ColorOutput "Error: Azure CLI is not installed or not in PATH. Please install Azure CLI." "Red"
                exit 1
            }
        }
        "gcp" {
            try {
                $gcloudVersion = gcloud --version
                if (!$?) { throw "Google Cloud SDK check failed" }
            } catch {
                Write-ColorOutput "Error: Google Cloud SDK is not installed or not in PATH. Please install Google Cloud SDK." "Red"
                exit 1
            }
        }
        "aliyun" {
            try {
                $aliyunVersion = aliyun version
                if (!$?) { throw "Aliyun CLI check failed" }
            } catch {
                Write-ColorOutput "Warning: Aliyun CLI is not installed or not in PATH. Some functionality may be limited." "Yellow"
            }
        }
    }
    
    Write-ColorOutput "All required tools are available." "Green"
}

# Build Docker images
function Build-Images {
    Write-ColorOutput "Building Docker images..." "Cyan"
    
    # Add version tag based on current date and git commit if available
    $versionTag = Get-Date -Format "yyyyMMdd"
    try {
        $gitShortHash = git rev-parse --short HEAD
        if ($?) {
            $versionTag = "$versionTag-$gitShortHash"
        }
    } catch {
        # Git not available or not a git repository, continue with date-only version
    }
    
    Write-ColorOutput "Using version tag: $versionTag" "Green"
    
    # Build images with version tag
    docker-compose build --build-arg VERSION="$versionTag"
    
    # Tag images for registry if specified
    if ($DOCKER_REGISTRY) {
        Write-ColorOutput "Tagging images for registry: $DOCKER_REGISTRY" "Cyan"
        docker tag steel-converter-frontend:latest "$($DOCKER_REGISTRY)/steel-converter-frontend:$versionTag"
        docker tag steel-converter-backend:latest "$($DOCKER_REGISTRY)/steel-converter-backend:$versionTag"
        docker tag steel-converter-frontend:latest "$($DOCKER_REGISTRY)/steel-converter-frontend:latest"
        docker tag steel-converter-backend:latest "$($DOCKER_REGISTRY)/steel-converter-backend:latest"
    }
    
    Write-ColorOutput "Docker images built successfully." "Green"
}

# Push Docker images to registry
function Push-Images {
    if ($DOCKER_REGISTRY) {
        Write-ColorOutput "Pushing images to registry: $DOCKER_REGISTRY" "Cyan"
        
        docker push "$($DOCKER_REGISTRY)/steel-converter-frontend:latest"
        docker push "$($DOCKER_REGISTRY)/steel-converter-backend:latest"
        docker push "$($DOCKER_REGISTRY)/steel-converter-frontend:$versionTag"
        docker push "$($DOCKER_REGISTRY)/steel-converter-backend:$versionTag"
        
        Write-ColorOutput "Images pushed to registry successfully." "Green"
    } else {
        Write-ColorOutput "No registry specified. Skipping image push." "Yellow"
    }
}

# Deploy to Aliyun ECS
function Deploy-ToAliyun {
    Write-ColorOutput "Deploying to Aliyun ECS..." "Cyan"
    
    $aliyunEcsIp = [Environment]::GetEnvironmentVariable("ALIYUN_ECS_IP")
    if (!$aliyunEcsIp) {
        Write-ColorOutput "Error: ALIYUN_ECS_IP environment variable not set. Please specify the ECS instance IP." "Red"
        exit 1
    }
    
    $aliyunEcsUser = [Environment]::GetEnvironmentVariable("ALIYUN_ECS_USER")
    if (!$aliyunEcsUser) {
        $aliyunEcsUser = "root"
        Write-ColorOutput "ALIYUN_ECS_USER not set. Using default: $aliyunEcsUser" "Yellow"
    }
    
    # Prepare deployment directory
    Write-ColorOutput "Preparing deployment package..." "Cyan"
    
    $deployDir = "deploy-package"
    New-Item -ItemType Directory -Force -Path $deployDir | Out-Null
    
    Copy-Item docker-compose.yml -Destination "$deployDir\"
    Copy-Item $ENV_FILE -Destination "$deployDir\.env"
    
    # SSH key option
    $sshKeyOption = ""
    $aliyunSshKey = [Environment]::GetEnvironmentVariable("ALIYUN_SSH_KEY")
    if ($aliyunSshKey) {
        $sshKeyOption = "-i $aliyunSshKey"
    }
    
    # Create deployment script
    $deployScript = @"
#!/bin/bash
set -e

# Create application directory
mkdir -p /app/steel-converter
cp docker-compose.yml /app/steel-converter/
cp .env /app/steel-converter/

# Navigate to application directory
cd /app/steel-converter

# Pull latest images and deploy
docker-compose pull
docker-compose down
docker-compose up -d

echo "Deployment completed successfully!"
"@
    
    Set-Content -Path "$deployDir\deploy.sh" -Value $deployScript
    
    # Check if we have ssh and scp available
    $hasSsh = $false
    try {
        $sshVersion = ssh -V
        $hasSsh = $?
    } catch {
        $hasSsh = $false
    }
    
    if ($hasSsh) {
        # Compress the deployment package
        Compress-Archive -Path "$deployDir\*" -DestinationPath "deploy.zip" -Force
        
        # Copy package to server and execute deployment script
        Write-ColorOutput "Copying deployment package to $aliyunEcsIp..." "Cyan"
        
        # Since PowerShell's SSH support might be limited, using command-line ssh
        scp $sshKeyOption deploy.zip "$($aliyunEcsUser)@$($aliyunEcsIp):/tmp/"
        
        Write-ColorOutput "Executing deployment script..." "Cyan"
        ssh $sshKeyOption "$($aliyunEcsUser)@$($aliyunEcsIp)" @"
mkdir -p /tmp/deploy
unzip -o /tmp/deploy.zip -d /tmp/deploy
cd /tmp/deploy
chmod +x deploy.sh
./deploy.sh
rm -rf /tmp/deploy
rm /tmp/deploy.zip
"@
        
        # Clean up local files
        Remove-Item -Path $deployDir -Recurse -Force
        Remove-Item -Path deploy.zip -Force
        
        Write-ColorOutput "Deployment to Aliyun ECS completed successfully!" "Green"
    } else {
        Write-ColorOutput "SSH tools not found. Please install OpenSSH client or use manual deployment." "Yellow"
        Write-ColorOutput "Manual deployment package created at: $deployDir" "Cyan"
    }
}

# Deploy to AWS
function Deploy-ToAWS {
    Write-ColorOutput "Deploying to AWS..." "Cyan"
    # Implement AWS-specific deployment logic here
    Write-ColorOutput "AWS deployment is not fully implemented yet." "Yellow"
}

# Deploy to Azure
function Deploy-ToAzure {
    Write-ColorOutput "Deploying to Azure..." "Cyan"
    # Implement Azure-specific deployment logic here
    Write-ColorOutput "Azure deployment is not fully implemented yet." "Yellow"
}

# Deploy to GCP
function Deploy-ToGCP {
    Write-ColorOutput "Deploying to Google Cloud Platform..." "Cyan"
    # Implement GCP-specific deployment logic here
    Write-ColorOutput "GCP deployment is not fully implemented yet." "Yellow"
}

# Main execution logic
Write-ColorOutput "Starting deployment process..." "Cyan"
Write-ColorOutput "Target platform: $DEPLOY_TARGET" "Green"
Write-ColorOutput "Action: $ACTION" "Green"

Check-Tools

switch ($ACTION) {
    "deploy" {
        Build-Images
        Push-Images
        
        switch ($DEPLOY_TARGET) {
            "aliyun" { Deploy-ToAliyun }
            "aws" { Deploy-ToAWS }
            "azure" { Deploy-ToAzure }
            "gcp" { Deploy-ToGCP }
            default {
                Write-ColorOutput "Error: Unknown deployment target: $DEPLOY_TARGET" "Red"
                exit 1
            }
        }
    }
    "update" {
        Write-ColorOutput "Updating existing deployment..." "Cyan"
        Build-Images
        Push-Images
        # Add update logic based on target platform
        Write-ColorOutput "Update completed." "Green"
    }
    "destroy" {
        Write-ColorOutput "Warning: This will destroy the deployment. Are you sure? (y/n)" "Red"
        $confirmation = Read-Host
        if ($confirmation -eq "y" -or $confirmation -eq "Y") {
            Write-ColorOutput "Destroying deployment..." "Cyan"
            # Add destroy logic based on target platform
            Write-ColorOutput "Destroy completed." "Green"
        } else {
            Write-ColorOutput "Destroy operation cancelled." "Cyan"
        }
    }
    default {
        Write-ColorOutput "Error: Unknown action: $ACTION" "Red"
        exit 1
    }
}

Write-ColorOutput "Deployment process completed successfully!" "Green" 