#!/usr/bin/env python3
"""
Database Initialization Script for Steel Unit Converter

This script initializes the database schema for the Steel Unit Converter application.
It ensures that all required tables and columns exist in the database.
"""

import os
import sys
import logging
import argparse
import importlib.util
import subprocess
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('database-init')

def load_env_file(env_file_path):
    """Load environment variables from .env file."""
    logger.info(f"Loading environment variables from {env_file_path}")
    env_vars = {}

    try:
        with open(env_file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                key, value = line.split('=', 1)
                env_vars[key.strip()] = value.strip()

        # Set environment variables
        for key, value in env_vars.items():
            os.environ[key] = value

        logger.info(f"Loaded {len(env_vars)} environment variables")
        return env_vars
    except Exception as e:
        logger.error(f"Failed to load environment variables: {e}")
        return {}

def get_database_type(env_vars):
    """Determine the database type from environment variables."""
    # Always use MySQL RDS
    return 'mysql'

def install_required_packages(database_type):
    """Install required packages for the database type."""
    logger.info(f"Installing required packages for {database_type}")

    packages = []

    if database_type == 'mysql':
        packages = ['pymysql', 'aiomysql', 'sqlalchemy']
    elif database_type == 'postgresql':
        packages = ['psycopg2-binary', 'asyncpg', 'sqlalchemy']
    elif database_type == 'sqlite':
        packages = ['aiosqlite', 'sqlalchemy']

    for package in packages:
        try:
            logger.info(f"Installing {package}")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            logger.info(f"Installed {package} successfully")
        except Exception as e:
            logger.error(f"Failed to install {package}: {e}")
            return False

    return True

def create_mysql_database_if_not_exists(host, port, user, password, database):
    """Create the MySQL database if it doesn't exist."""
    logger.info(f"Creating MySQL database {database} if it doesn't exist")

    try:
        import pymysql

        # Connect to MySQL without specifying a database
        connection = pymysql.connect(
            host=host,
            port=int(port),
            user=user,
            password=password
        )

        # Create a cursor
        cursor = connection.cursor()

        # Create the database if it doesn't exist
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{database}`")

        # Close the connection
        connection.close()

        logger.info(f"MySQL database {database} created or already exists")
        return True
    except Exception as e:
        logger.error(f"Failed to create MySQL database: {e}")
        return False

def create_postgresql_database_if_not_exists(host, port, user, password, database):
    """Create the PostgreSQL database if it doesn't exist."""
    logger.info(f"Creating PostgreSQL database {database} if it doesn't exist")

    try:
        import psycopg2

        # Connect to PostgreSQL without specifying a database
        connection = psycopg2.connect(
            host=host,
            port=int(port),
            user=user,
            password=password,
            database='postgres'  # Connect to the default postgres database
        )

        # Set autocommit to True
        connection.autocommit = True

        # Create a cursor
        cursor = connection.cursor()

        # Check if the database exists
        cursor.execute(f"SELECT 1 FROM pg_database WHERE datname = '{database}'")
        exists = cursor.fetchone()

        # Create the database if it doesn't exist
        if not exists:
            cursor.execute(f"CREATE DATABASE {database}")

        # Close the connection
        connection.close()

        logger.info(f"PostgreSQL database {database} created or already exists")
        return True
    except Exception as e:
        logger.error(f"Failed to create PostgreSQL database: {e}")
        return False

def create_database_if_not_exists(database_type, env_vars):
    """Create the database if it doesn't exist."""
    # Only support MySQL now
    return create_mysql_database_if_not_exists(
        env_vars['RDS_HOSTNAME'],
        env_vars.get('RDS_PORT', '3306'),
        env_vars['RDS_USERNAME'],
        env_vars['RDS_PASSWORD'],
        env_vars['RDS_DB_NAME']
    )

def create_mysql_tables(host, port, user, password, database):
    """Create the required tables in the MySQL database."""
    logger.info(f"Creating tables in MySQL database {database}")

    try:
        import pymysql

        # Connect to the MySQL database
        connection = pymysql.connect(
            host=host,
            port=int(port),
            user=user,
            password=password,
            database=database
        )

        # Create a cursor
        cursor = connection.cursor()

        # Check if tables exist and drop them if they do (in reverse order of dependencies)
        logger.info("Checking for existing tables...")

        # Get list of tables
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        logger.info(f"Existing tables: {tables}")

        # Drop tables with foreign keys first
        if 'chat_likes' in tables:
            logger.info("Dropping chat_likes table...")
            cursor.execute("DROP TABLE chat_likes")

        if 'chat_histories' in tables:
            logger.info("Dropping chat_histories table...")
            cursor.execute("DROP TABLE chat_histories")

        if 'chat_history' in tables:
            logger.info("Dropping chat_history table...")
            cursor.execute("DROP TABLE chat_history")

        if 'conversions' in tables:
            logger.info("Dropping conversions table...")
            cursor.execute("DROP TABLE conversions")

        if 'verification_codes' in tables:
            logger.info("Dropping verification_codes table...")
            cursor.execute("DROP TABLE verification_codes")

        if 'alembic_version' in tables:
            logger.info("Dropping alembic_version table...")
            cursor.execute("DROP TABLE alembic_version")

        if 'users' in tables:
            logger.info("Dropping users table...")
            cursor.execute("DROP TABLE users")

        # Create the users table
        logger.info("Creating users table...")
        cursor.execute("""
        CREATE TABLE users (
            id VARCHAR(36) PRIMARY KEY,
            username VARCHAR(255),
            email VARCHAR(255) UNIQUE,
            hashed_password TEXT,
            company_name VARCHAR(255),
            country VARCHAR(255),
            user_type VARCHAR(50),
            is_active BOOLEAN DEFAULT TRUE,
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login TIMESTAMP NULL,
            conversion_count BIGINT DEFAULT 0,
            preferences JSON,
            profile_image_url TEXT
        )
        """)

        # Create the conversions table
        logger.info("Creating conversions table...")
        cursor.execute("""
        CREATE TABLE conversions (
            id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36),
            input_value DOUBLE,
            input_unit VARCHAR(50),
            output_value DOUBLE,
            output_unit VARCHAR(50),
            conversion_type VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """)

        # Create the chat_history table
        logger.info("Creating chat_history table...")
        cursor.execute("""
        CREATE TABLE chat_history (
            id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36),
            session_id VARCHAR(255),
            message TEXT,
            role VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """)

        # Create the verification_codes table
        logger.info("Creating verification_codes table...")
        cursor.execute("""
        CREATE TABLE verification_codes (
            id VARCHAR(36) PRIMARY KEY,
            email VARCHAR(255),
            code VARCHAR(10),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            is_used BOOLEAN DEFAULT FALSE,
            username VARCHAR(50),
            password_hash VARCHAR(100),
            company_name VARCHAR(100),
            country VARCHAR(100),
            user_id VARCHAR(36),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        )
        """)

        # Create the chat_likes table
        logger.info("Creating chat_likes table...")
        cursor.execute("""
        CREATE TABLE chat_likes (
            id VARCHAR(36) PRIMARY KEY,
            user_id VARCHAR(36),
            chat_id VARCHAR(36),
            is_liked BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (chat_id) REFERENCES chat_history(id)
        )
        """)

        # Create the chat_histories table
        logger.info("Creating chat_histories table...")
        cursor.execute("""
        CREATE TABLE chat_histories (
            id VARCHAR(36) NOT NULL,
            user_id VARCHAR(36) NOT NULL,
            session_id VARCHAR(50) NOT NULL,
            title VARCHAR(200),
            message_count INTEGER NOT NULL,
            last_message_at DATETIME NOT NULL,
            created_at DATETIME NOT NULL,
            is_archived BOOL NOT NULL,
            oss_object_key VARCHAR(255),
            content JSON,
            meta_data JSON,
            PRIMARY KEY (id),
            FOREIGN KEY(user_id) REFERENCES users (id) ON DELETE CASCADE
        )
        """)

        # Commit the changes
        connection.commit()

        # Close the connection
        connection.close()

        logger.info("MySQL tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to create MySQL tables: {e}")
        return False

def create_postgresql_tables(host, port, user, password, database):
    """Create the required tables in the PostgreSQL database."""
    logger.info(f"Creating tables in PostgreSQL database {database}")

    try:
        import psycopg2

        # Connect to the PostgreSQL database
        connection = psycopg2.connect(
            host=host,
            port=int(port),
            user=user,
            password=password,
            database=database
        )

        # Create a cursor
        cursor = connection.cursor()

        # Create the users table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(255),
            email VARCHAR(255) UNIQUE,
            hashed_password TEXT,
            company_name VARCHAR(255),
            country VARCHAR(255),
            user_type VARCHAR(50),
            is_active BOOLEAN DEFAULT TRUE,
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            conversion_count INTEGER DEFAULT 0,
            preferences JSONB,
            profile_image_url TEXT
        )
        """)

        # Create the conversions table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS conversions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id),
            input_value FLOAT,
            input_unit VARCHAR(50),
            output_value FLOAT,
            output_unit VARCHAR(50),
            conversion_type VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)

        # Create the chat_history table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS chat_history (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id),
            session_id VARCHAR(255),
            message TEXT,
            role VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)

        # Commit the changes
        connection.commit()

        # Close the connection
        connection.close()

        logger.info("PostgreSQL tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to create PostgreSQL tables: {e}")
        return False

def create_sqlite_tables(database_path):
    """Create the required tables in the SQLite database."""
    logger.info(f"Creating tables in SQLite database {database_path}")

    try:
        import sqlite3

        # Connect to the SQLite database
        connection = sqlite3.connect(database_path)

        # Create a cursor
        cursor = connection.cursor()

        # Create the users table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            email TEXT UNIQUE,
            hashed_password TEXT,
            company_name TEXT,
            country TEXT,
            user_type TEXT,
            is_active INTEGER DEFAULT 1,
            is_admin INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            conversion_count INTEGER DEFAULT 0,
            preferences TEXT,
            profile_image_url TEXT
        )
        """)

        # Create the conversions table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS conversions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            input_value REAL,
            input_unit TEXT,
            output_value REAL,
            output_unit TEXT,
            conversion_type TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """)

        # Create the chat_history table if it doesn't exist
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS chat_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            session_id TEXT,
            message TEXT,
            role TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
        """)

        # Commit the changes
        connection.commit()

        # Close the connection
        connection.close()

        logger.info("SQLite tables created successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to create SQLite tables: {e}")
        return False

def create_tables(database_type, env_vars):
    """Create the required tables in the database."""
    # Only support MySQL now
    return create_mysql_tables(
        env_vars['RDS_HOSTNAME'],
        env_vars.get('RDS_PORT', '3306'),
        env_vars['RDS_USERNAME'],
        env_vars['RDS_PASSWORD'],
        env_vars['RDS_DB_NAME']
    )

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Database Initialization Script for Steel Unit Converter')
    parser.add_argument('--env-file', default='.env', help='Path to the environment file (default: .env)')
    args = parser.parse_args()

    logger.info("Starting database initialization script")

    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Try to load environment variables from different locations
    env_vars = {}

    # First try the specified env file path
    env_file_path = os.path.join(current_dir, args.env_file)
    if os.path.exists(env_file_path):
        logger.info(f"Found .env file at {env_file_path}")
        env_vars = load_env_file(env_file_path)

    # If RDS_HOSTNAME is not found, try the backend directory
    if 'RDS_HOSTNAME' not in env_vars:
        backend_env_path = os.path.join(current_dir, 'backend', args.env_file)
        if os.path.exists(backend_env_path):
            logger.info(f"Trying backend .env file at {backend_env_path}")
            env_vars = load_env_file(backend_env_path)

    # If RDS_HOSTNAME is still not found, try the parent directory
    if 'RDS_HOSTNAME' not in env_vars:
        parent_env_path = os.path.join(os.path.dirname(current_dir), args.env_file)
        if os.path.exists(parent_env_path):
            logger.info(f"Trying parent directory .env file at {parent_env_path}")
            env_vars = load_env_file(parent_env_path)

    # Check if we have the required environment variables
    if 'RDS_HOSTNAME' not in env_vars:
        logger.error("RDS_HOSTNAME not found in any .env file. Please make sure it's defined.")
        logger.error("Tried locations:")
        logger.error(f"  - {env_file_path}")
        logger.error(f"  - {os.path.join(current_dir, 'backend', args.env_file)}")
        logger.error(f"  - {os.path.join(os.path.dirname(current_dir), args.env_file)}")
        return 1

    # Determine the database type
    database_type = get_database_type(env_vars)
    logger.info(f"Using database type: {database_type}")

    # Install required packages
    if not install_required_packages(database_type):
        logger.error("Failed to install required packages")
        return 1

    # Create the database if it doesn't exist
    if not create_database_if_not_exists(database_type, env_vars):
        logger.error("Failed to create database")
        return 1

    # Create the required tables
    if not create_tables(database_type, env_vars):
        logger.error("Failed to create tables")
        return 1

    logger.info("Database initialization completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())

