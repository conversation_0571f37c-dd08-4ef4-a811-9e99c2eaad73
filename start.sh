#!/usr/bin/env bash

# Default to development mode
MODE=${1:-dev}

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"

# Check if python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check if node is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js 14 or higher."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "npm is not installed. Please install npm."
    exit 1
fi

start_backend() {
    local mode=$1
    
    echo "Starting backend in $mode mode..."
    
    # Change directory to backend
    cd "$BACKEND_DIR" || exit
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo "Creating virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Install dependencies if requirements.txt exists
    if [ -f "requirements.txt" ]; then
        echo "Installing backend dependencies..."
        pip install -r requirements.txt
    fi
    
    # Set environment variables
    if [ "$mode" = "prod" ]; then
        export ENV="production"
        export DEBUG="False"
    else
        export ENV="development"
        export DEBUG="True"
    fi
    
    # Start backend server
    if [ "$mode" = "prod" ]; then
        echo "Starting backend in production mode..."
        uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
    else
        echo "Starting backend in development mode..."
        uvicorn main:app --reload --host 0.0.0.0 --port 8000
    fi
}

start_frontend() {
    local mode=$1
    
    echo "Starting frontend in $mode mode..."
    
    # Change directory to frontend
    cd "$FRONTEND_DIR" || exit
    
    # Install dependencies if needed
    if [ -f "package.json" ]; then
        echo "Installing frontend dependencies..."
        npm install
    fi
    
    # Start frontend
    if [ "$mode" = "prod" ]; then
        echo "Building and serving frontend in production mode..."
        npm run build
        npm run preview
    else
        echo "Starting frontend in development mode..."
        npm run dev
    fi
}

# Handle different modes
case "$MODE" in
    backend)
        start_backend "dev"
        ;;
    frontend)
        start_frontend "dev"
        ;;
    prod)
        start_backend "prod" &
        BACKEND_PID=$!
        start_frontend "prod"
        ;;
    backend-prod)
        start_backend "prod"
        ;;
    frontend-prod)
        start_frontend "prod"
        ;;
    *)
        # Default: start both in development mode
        start_backend "dev" &
        BACKEND_PID=$!
        start_frontend "dev"
        ;;
esac

# Keep the script running and handle cleanup
if [ "$MODE" = "dev" ] || [ "$MODE" = "prod" ]; then
    trap 'kill $BACKEND_PID 2>/dev/null; echo "All services stopped."; exit 0' INT TERM
    
    echo "Services started. Press Ctrl+C to stop."
    wait
fi
