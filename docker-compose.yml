version: '3.8'

services:
  backend:
    build: ./backend
    container_name: steel-converter-backend
    restart: always
    environment:
      - ENV=production
      - DATABASE_URL=${RDS_DATABASE_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - SMTP_SERVER=${SMTP_SERVER}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
    mem_limit: 512M
    cpus: 0.5
    depends_on:
      - db
    networks:
      - app-network

  frontend:
    build: ./frontend
    container_name: steel-converter-frontend
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    mem_limit: 256M
    cpus: 0.3
    depends_on:
      - backend
    networks:
      - app-network

  db:
    image: postgres:15-alpine
    container_name: steel-converter-db
    restart: always
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=steel_converter
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  postgres_data:
