#!/bin/bash

# Script to check the setup of the Steel Unit Converter application
# This script checks nginx, ports, firewall, and application status

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
LOG_DIR="$SCRIPT_DIR/logs"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Public IP address
PUBLIC_IP="************"

# Function to print section header
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Function to print success message
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print warning message
print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Function to print error message
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to check if a command exists
check_command() {
    if command -v $1 &> /dev/null; then
        print_success "$1 is installed"
        return 0
    else
        print_error "$1 is not installed"
        return 1
    fi
}

# Function to check if a service is running
check_service() {
    if systemctl is-active --quiet $1 || service $1 status > /dev/null 2>&1; then
        print_success "$1 service is running"
        return 0
    else
        print_error "$1 service is not running"
        return 1
    fi
}

# Function to check if a port is open
check_port() {
    local port=$1
    local description=$2
    local host=${3:-localhost}
    
    # Try different methods to check if port is open
    if command -v nc &> /dev/null; then
        if nc -z -w 5 $host $port; then
            print_success "Port $port ($description) is open on $host"
            return 0
        fi
    elif command -v telnet &> /dev/null; then
        if echo quit | telnet $host $port 2>/dev/null | grep -q Connected; then
            print_success "Port $port ($description) is open on $host"
            return 0
        fi
    elif command -v nmap &> /dev/null; then
        if nmap -p $port $host | grep -q "open"; then
            print_success "Port $port ($description) is open on $host"
            return 0
        fi
    else
        # Fallback to /dev/tcp if no tools are available (Bash only)
        if timeout 1 bash -c "echo > /dev/tcp/$host/$port" 2>/dev/null; then
            print_success "Port $port ($description) is open on $host"
            return 0
        fi
    fi
    
    print_error "Port $port ($description) is not open on $host"
    return 1
}

# Function to check if a process is running
check_process() {
    local process_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null; then
            print_success "$process_name is running with PID: $pid"
            return 0
        else
            print_error "$process_name is not running (PID file exists but process is dead)"
            return 1
        fi
    else
        # Try to find the process by name
        if pgrep -f "$process_name" > /dev/null; then
            local pid=$(pgrep -f "$process_name" | head -n 1)
            print_warning "$process_name is running with PID: $pid (PID file not found)"
            return 0
        else
            print_error "$process_name is not running"
            return 1
        fi
    fi
}

# Function to check firewall status
check_firewall() {
    print_header "Firewall Status"
    
    # Check if firewall is installed and running
    if command -v ufw &> /dev/null; then
        echo "UFW firewall status:"
        ufw status
        
        # Check if ports 80 and 443 are allowed
        if ufw status | grep -E "80|443" | grep -q "ALLOW"; then
            print_success "Ports 80 and 443 are allowed in UFW"
        else
            print_warning "Ports 80 and 443 may not be allowed in UFW"
            echo "Consider running: sudo ufw allow 80/tcp && sudo ufw allow 443/tcp"
        fi
    elif command -v firewall-cmd &> /dev/null; then
        echo "FirewallD status:"
        firewall-cmd --state
        
        # Check if ports 80 and 443 are allowed
        if firewall-cmd --list-ports | grep -E "80|443" > /dev/null; then
            print_success "Ports 80 and 443 are allowed in FirewallD"
        else
            print_warning "Ports 80 and 443 may not be allowed in FirewallD"
            echo "Consider running: sudo firewall-cmd --permanent --add-port=80/tcp && sudo firewall-cmd --permanent --add-port=443/tcp && sudo firewall-cmd --reload"
        fi
    elif command -v iptables &> /dev/null; then
        echo "IPTables rules:"
        sudo iptables -L | grep -E "80|443" || echo "No explicit rules for ports 80/443"
    else
        print_warning "No firewall management tool found (ufw, firewall-cmd, iptables)"
    fi
}

# Function to check nginx configuration
check_nginx_config() {
    print_header "Nginx Configuration"
    
    # Check if nginx is installed
    if ! check_command "nginx"; then
        print_error "Nginx is not installed. Install it with: sudo apt-get install -y nginx"
        return 1
    fi
    
    # Check if nginx service is running
    if ! check_service "nginx"; then
        print_error "Nginx service is not running. Start it with: sudo systemctl start nginx"
        return 1
    fi
    
    # Check nginx configuration
    echo "Testing nginx configuration..."
    if sudo nginx -t; then
        print_success "Nginx configuration is valid"
    else
        print_error "Nginx configuration is invalid"
        return 1
    fi
    
    # Check if steelnet.conf exists
    if [ -f "/etc/nginx/conf.d/steelnet.conf" ]; then
        print_success "Steelnet nginx configuration file exists"
        
        # Check if the configuration includes the public IP
        if grep -q "$PUBLIC_IP" "/etc/nginx/conf.d/steelnet.conf"; then
            print_success "Public IP is configured in nginx"
        else
            print_warning "Public IP may not be configured in nginx"
            echo "Consider updating the server_name directive in /etc/nginx/conf.d/steelnet.conf"
        fi
        
        # Check if the configuration includes proper proxy settings
        if grep -q "proxy_pass http://localhost:3000" "/etc/nginx/conf.d/steelnet.conf" && \
           grep -q "proxy_pass http://localhost:8000" "/etc/nginx/conf.d/steelnet.conf"; then
            print_success "Proxy settings are configured correctly"
        else
            print_warning "Proxy settings may not be configured correctly"
        fi
    else
        print_error "Steelnet nginx configuration file does not exist"
        echo "Run: ./start-prod.sh --url-access to create the configuration"
        return 1
    fi
    
    # Check if SSL certificates exist
    if [ -f "$SCRIPT_DIR/ssl/cert.pem" ] && [ -f "$SCRIPT_DIR/ssl/key.pem" ]; then
        print_success "SSL certificates exist"
    else
        print_warning "SSL certificates do not exist"
        echo "Run: ./start-prod.sh --url-access to generate SSL certificates"
    fi
    
    return 0
}

# Function to check application status
check_application_status() {
    print_header "Application Status"
    
    # Check if backend is running
    check_process "gunicorn" "$LOG_DIR/backend.pid"
    
    # Check if frontend is running
    check_process "serve" "$LOG_DIR/frontend.pid" || check_process "vite" "$LOG_DIR/frontend.pid"
    
    # Check if backend port is open
    check_port 8000 "Backend"
    
    # Check if frontend port is open
    check_port 3000 "Frontend"
    
    # Check if nginx ports are open
    check_port 80 "HTTP"
    check_port 443 "HTTPS"
    
    # Try to access the application
    echo -e "\nTesting application access:"
    
    # Test local access
    echo "Testing local access..."
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 | grep -q "200"; then
        print_success "Frontend is accessible locally"
    else
        print_error "Frontend is not accessible locally"
    fi
    
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8000 | grep -q "200"; then
        print_success "Backend is accessible locally"
    else
        print_error "Backend is not accessible locally"
    fi
    
    # Test public access
    echo "Testing public access..."
    if curl -s -k -o /dev/null -w "%{http_code}" https://$PUBLIC_IP | grep -q "200"; then
        print_success "Application is accessible via public IP (HTTPS)"
    else
        print_error "Application is not accessible via public IP (HTTPS)"
    fi
    
    if curl -s -o /dev/null -w "%{http_code}" http://$PUBLIC_IP | grep -q "301\|200"; then
        print_success "Application is accessible via public IP (HTTP)"
    else
        print_error "Application is not accessible via public IP (HTTP)"
    fi
}

# Function to check network connectivity
check_network() {
    print_header "Network Connectivity"
    
    # Check if we can reach the internet
    if ping -c 1 google.com > /dev/null 2>&1; then
        print_success "Internet connectivity is working"
    else
        print_error "Internet connectivity is not working"
    fi
    
    # Check if we can reach our public IP
    if ping -c 1 $PUBLIC_IP > /dev/null 2>&1; then
        print_success "Public IP is reachable"
    else
        print_warning "Public IP is not reachable via ping (this may be normal if ICMP is blocked)"
    fi
    
    # Check if we can reach our local IP
    LOCAL_IP=""
    if command -v ip &> /dev/null; then
        LOCAL_IP=$(ip addr show | grep -E "inet .* global" | grep -v docker | awk '{print $2}' | cut -d/ -f1 | head -n 1)
    elif command -v ifconfig &> /dev/null; then
        LOCAL_IP=$(ifconfig | grep -E "inet .* broadcast" | awk '{print $2}' | head -n 1)
    fi
    
    if [ -n "$LOCAL_IP" ]; then
        if ping -c 1 $LOCAL_IP > /dev/null 2>&1; then
            print_success "Local IP ($LOCAL_IP) is reachable"
        else
            print_error "Local IP ($LOCAL_IP) is not reachable"
        fi
    else
        print_warning "Could not determine local IP"
    fi
    
    # Check cloud provider network settings
    echo "Checking cloud provider network settings..."
    if [ -f /sys/hypervisor/uuid ] && grep -q "ec2" /sys/hypervisor/uuid; then
        # AWS EC2
        echo "Running on AWS EC2"
        echo "Checking security group settings..."
        if command -v aws &> /dev/null; then
            INSTANCE_ID=$(curl -s http://***************/latest/meta-data/instance-id)
            aws ec2 describe-instance-attribute --instance-id $INSTANCE_ID --attribute groupSet
        else
            print_warning "AWS CLI not installed, cannot check security group settings"
        fi
    elif [ -f /sys/class/dmi/id/product_name ] && grep -q "Alibaba Cloud" /sys/class/dmi/id/product_name; then
        # Alibaba Cloud
        echo "Running on Alibaba Cloud"
        print_warning "Please check security group settings in the Alibaba Cloud console"
        echo "Ensure ports 80 and 443 are allowed in the security group"
    else
        echo "Cloud provider not detected or not supported for automatic checks"
    fi
}

# Function to check system resources
check_resources() {
    print_header "System Resources"
    
    # Check CPU usage
    echo "CPU usage:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4 "% used"}'
    
    # Check memory usage
    echo -e "\nMemory usage:"
    free -h
    
    # Check disk usage
    echo -e "\nDisk usage:"
    df -h | grep -v "tmpfs" | grep -v "udev"
    
    # Check for memory-intensive processes
    echo -e "\nTop memory-consuming processes:"
    ps -eo pid,ppid,cmd,%mem,%cpu --sort=-%mem | head -n 6
}

# Function to fix common issues
fix_common_issues() {
    print_header "Fixing Common Issues"
    
    # Check if nginx is installed
    if ! command -v nginx &> /dev/null; then
        echo "Nginx is not installed. Would you like to install it? (y/n)"
        read -r INSTALL_NGINX
        if [[ "$INSTALL_NGINX" =~ ^[Yy]$ ]]; then
            echo "Installing nginx..."
            if command -v apt-get &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y nginx
            elif command -v yum &> /dev/null; then
                sudo yum install -y nginx
            else
                print_error "Could not install nginx. Please install it manually."
            fi
        fi
    fi
    
    # Check if nginx is running
    if ! systemctl is-active --quiet nginx && ! service nginx status > /dev/null 2>&1; then
        echo "Nginx is not running. Would you like to start it? (y/n)"
        read -r START_NGINX
        if [[ "$START_NGINX" =~ ^[Yy]$ ]]; then
            echo "Starting nginx..."
            sudo systemctl start nginx || sudo service nginx start
        fi
    fi
    
    # Check if firewall is blocking ports
    if command -v ufw &> /dev/null && ufw status | grep -q "active"; then
        if ! ufw status | grep -E "80|443" | grep -q "ALLOW"; then
            echo "Firewall may be blocking ports 80 and 443. Would you like to allow them? (y/n)"
            read -r ALLOW_PORTS
            if [[ "$ALLOW_PORTS" =~ ^[Yy]$ ]]; then
                echo "Allowing ports 80 and 443..."
                sudo ufw allow 80/tcp
                sudo ufw allow 443/tcp
            fi
        fi
    fi
    
    # Check if application is running
    if [ ! -f "$LOG_DIR/backend.pid" ] || ! ps -p $(cat "$LOG_DIR/backend.pid" 2>/dev/null) > /dev/null; then
        echo "Application is not running. Would you like to start it? (y/n)"
        read -r START_APP
        if [[ "$START_APP" =~ ^[Yy]$ ]]; then
            echo "Starting application..."
            ./start-prod.sh --restart --url-access
        fi
    fi
    
    # Check if SSL certificates exist
    if [ ! -f "$SCRIPT_DIR/ssl/cert.pem" ] || [ ! -f "$SCRIPT_DIR/ssl/key.pem" ]; then
        echo "SSL certificates do not exist. Would you like to generate them? (y/n)"
        read -r GEN_SSL
        if [[ "$GEN_SSL" =~ ^[Yy]$ ]]; then
            echo "Generating SSL certificates..."
            mkdir -p "$SCRIPT_DIR/ssl"
            openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
                -keyout "$SCRIPT_DIR/ssl/key.pem" -out "$SCRIPT_DIR/ssl/cert.pem" \
                -subj "/C=CN/ST=State/L=City/O=Organization/CN=steelnet.ai"
        fi
    fi
}

# Main function
main() {
    print_header "Steel Unit Converter Setup Check"
    echo "This script will check the setup of the Steel Unit Converter application"
    echo "Public IP: $PUBLIC_IP"
    echo "Script directory: $SCRIPT_DIR"
    echo "Log directory: $LOG_DIR"
    
    # Check if running as root
    if [ "$EUID" -eq 0 ]; then
        print_warning "Running as root. Some checks may not work correctly."
    fi
    
    # Check required commands
    print_header "Required Commands"
    check_command "curl"
    check_command "grep"
    check_command "awk"
    check_command "sed"
    
    # Check nginx
    check_nginx_config
    
    # Check application status
    check_application_status
    
    # Check network connectivity
    check_network
    
    # Check firewall
    check_firewall
    
    # Check system resources
    check_resources
    
    # Ask if user wants to fix common issues
    echo -e "\nWould you like to attempt to fix common issues? (y/n)"
    read -r FIX_ISSUES
    if [[ "$FIX_ISSUES" =~ ^[Yy]$ ]]; then
        fix_common_issues
    fi
    
    print_header "Summary"
    echo "Check complete. If you're still having issues, please check the logs:"
    echo "  Backend logs: $LOG_DIR/backend.log"
    echo "  Frontend logs: $LOG_DIR/frontend.log"
    echo "  Nginx logs: /var/log/nginx/error.log and /var/log/nginx/access.log"
    
    echo -e "\nYou can also try restarting the application with:"
    echo "  ./start-prod.sh --restart --url-access"
    
    echo -e "\nIf the issue persists, check if your cloud provider's security group allows traffic on ports 80 and 443."
}

# Run the main function
main
