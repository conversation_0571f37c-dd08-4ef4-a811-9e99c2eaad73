#!/usr/bin/env python3
"""
Test script for Steel Unit Converter streaming functionality
"""

import requests
import json
import time
from typing import Dict, Any

def test_streaming_endpoint():
    """Test the streaming endpoint with steel data"""
    
    # Test data from testCase.txt
    test_data = {
        "text": """S/S 430 BA NO PI
.015(+/-.0015) X 2.343"(+/-.005) X COIL 7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL 8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL 12,550#""",
        "unit_system": "metric",
        "function": "table"
    }
    
    print("🔧 Testing Steel Unit Converter Streaming Functionality")
    print("=" * 60)
    
    # Test 1: Health Check
    print("\n1️⃣ Testing Backend Health...")
    try:
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"✅ Backend is healthy: {health_data['status']}")
            print(f"📊 Database: {health_data['database']['type']}")
        else:
            print(f"❌ Health check failed: {health_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test 2: Streaming Endpoint
    print("\n2️⃣ Testing Streaming Endpoint...")
    try:
        url = "http://localhost:8000/api/llm/stream"
        print(f"🌐 Sending request to: {url}")
        print(f"📝 Data: {json.dumps(test_data, indent=2)}")
        
        response = requests.post(
            url,
            json=test_data,
            headers={
                "Content-Type": "application/json",
                "Accept": "text/plain"
            },
            stream=True,
            timeout=30
        )
        
        print(f"📡 Response status: {response.status_code}")
        print(f"📋 Response headers: {dict(response.headers)}")
        
        if response.status_code != 200:
            print(f"❌ Streaming request failed: {response.status_code}")
            print(f"📄 Response text: {response.text}")
            return False
        
        # Process streaming response
        print("\n📺 Processing streaming response...")
        content_chunks = []
        chunk_count = 0
        
        for line in response.iter_lines(decode_unicode=True):
            if line:
                chunk_count += 1
                print(f"📦 Chunk {chunk_count}: {line[:100]}{'...' if len(line) > 100 else ''}")
                
                # Parse SSE format
                if line.startswith("data: "):
                    data_part = line[6:]  # Remove "data: " prefix
                    if data_part.strip() == "[DONE]":
                        print("✅ Stream completed with [DONE] marker")
                        break
                    try:
                        chunk_data = json.loads(data_part)
                        if chunk_data.get("type") == "content":
                            content_chunks.append(chunk_data.get("content", ""))
                        elif chunk_data.get("type") == "completion":
                            print(f"🎯 Completion data received: {chunk_data}")
                    except json.JSONDecodeError as e:
                        print(f"⚠️ JSON decode error for chunk: {e}")
                        print(f"📄 Raw chunk: {data_part}")
        
        full_content = "".join(content_chunks)
        print(f"\n📊 Streaming Summary:")
        print(f"   • Total chunks: {chunk_count}")
        print(f"   • Content length: {len(full_content)} chars")
        print(f"   • Contains table tags: {'<converted_content>' in full_content}")
        
        # Test 3: Table Detection
        print("\n3️⃣ Testing Table Detection...")
        if "<converted_content>" in full_content:
            print("✅ Converted content tags found")
            
            # Extract table content
            import re
            match = re.search(r'<converted_content>(.*?)</converted_content>', full_content, re.DOTALL)
            if match:
                table_content = match.group(1).strip()
                print(f"📋 Table content extracted ({len(table_content)} chars)")
                print(f"📄 Table preview: {table_content[:200]}{'...' if len(table_content) > 200 else ''}")
                
                # Check for markdown table format
                if "|" in table_content and "-" in table_content:
                    print("✅ Markdown table format detected")
                    lines = table_content.split('\n')
                    header_line = next((line for line in lines if '|' in line), None)
                    if header_line:
                        headers = [h.strip() for h in header_line.split('|') if h.strip()]
                        print(f"📊 Table headers: {headers}")
                else:
                    print("⚠️ Table format not recognized as markdown")
            else:
                print("❌ Failed to extract table content")
        else:
            print("⚠️ No converted content tags found in response")
        
        print("\n🎉 Streaming test completed successfully!")
        return True
        
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the backend server running?")
        return False
    except Exception as e:
        print(f"❌ Streaming test error: {e}")
        return False

def test_regular_endpoint():
    """Test the regular (non-streaming) endpoint"""
    print("\n4️⃣ Testing Regular LLM Endpoint...")
    
    test_data = {
        "text": "Convert 1 inch to mm",
        "unit_system": "metric",
        "function": "conversion"
    }
    
    try:
        url = "http://localhost:8000/api/llm"
        response = requests.post(url, json=test_data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Regular endpoint working")
            print(f"📊 Response keys: {list(result.keys())}")
            return True
        else:
            print(f"❌ Regular endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Regular endpoint error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Steel Unit Converter Tests")
    print("=" * 60)
    
    # Test streaming functionality
    streaming_success = test_streaming_endpoint()
    
    # Test regular functionality
    regular_success = test_regular_endpoint()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY:")
    print(f"   • Streaming endpoint: {'✅ PASS' if streaming_success else '❌ FAIL'}")
    print(f"   • Regular endpoint: {'✅ PASS' if regular_success else '❌ FAIL'}")
    
    if streaming_success and regular_success:
        print("\n🎉 All tests passed! The application is fully functional.")
    else:
        print("\n⚠️ Some tests failed. Please check the backend logs.")
    
    return streaming_success and regular_success

if __name__ == "__main__":
    main() 