/**
 * Frontend-only test to debug table parsing corruption
 * Run this in browser console to test the parsing logic
 */

// Simulate perfect backend data (from our backend test)
const perfectTableData = `Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.343in(+/-0.005in) x COIL|7190#|0.38mm(+/-0.04mm) x 59.51mm(+/-0.13mm) x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 2.406in(+/-0.005in) x COIL|8061#|0.38mm(+/-0.04mm) x 61.11mm(+/-0.13mm) x COIL|3656.7 kg|BA Finish
005|S/S 430 BA NO PI|0.015in(+/-0.0015in) x 47.438in(+/-0.03125in) x COIL|57655#|0.38mm(+/-0.04mm) x 1204.93mm(+/-0.79mm) x COIL|26152.1 kg|BA Finish
008|S/S 430 #2B NO PI|0.015in(+/-0.0015in) x 16.938in(+/-0.005in) x COIL|5000#|0.38mm(+/-0.04mm) x 430.23mm(+/-0.13mm) x COIL|2268.0 kg|2B Finish`;

console.log('🧪 FRONTEND PARSER CORRUPTION TEST');
console.log('=' * 50);

// Test 1: Basic parsing
console.log('📋 TEST 1: Basic line parsing');
const lines = perfectTableData.split('\n');
lines.forEach((line, i) => {
    if (line.trim()) {
        const cells = line.split('|').map(c => c.trim());
        console.log(`Line ${i}: ${cells.length} cells | ${line.substring(0, 50)}...`);
        if (i === 0) {
            console.log(`  Headers: ${JSON.stringify(cells)}`);
        } else {
            console.log(`  Row cells: ${JSON.stringify(cells.map(c => c.substring(0, 15) + (c.length > 15 ? '...' : '')))}`);
        }
    }
});

// Test 2: Simulate row object creation (like frontend does)
console.log('\n📊 TEST 2: Row object creation');
const headerLine = lines[0];
const headers = headerLine.split('|').map(h => h.trim());
console.log(`Headers: ${JSON.stringify(headers)}`);

const dataLines = lines.slice(1);
const rows = dataLines.map((line, lineIndex) => {
    const cells = line.split('|').map(c => c.trim());
    const row = {};
    
    // This is how the frontend creates row objects
    headers.forEach((header, index) => {
        row[header] = cells[index] || '';
    });
    
    console.log(`Row ${lineIndex + 1}:`);
    console.log(`  Cell count: ${cells.length}, Header count: ${headers.length}`);
    console.log(`  Row object keys: ${Object.keys(row)}`);
    console.log(`  Row object values preview: ${Object.values(row).map(v => v.substring(0, 15) + (v.length > 15 ? '...' : ''))}`);
    
    // Check for corruption patterns
    const hasCorruption = Object.values(row).some(val => 
        typeof val === 'string' && (
            val.includes('NOin') || 
            val.includes('.000in25in') || 
            val.includes('xmm(+/-0.04mm) xmm') ||
            val.includes('S/S 430') && val.includes('kg') // Check for mixed data
        )
    );
    
    if (hasCorruption) {
        console.log(`  🚨 CORRUPTION DETECTED in row ${lineIndex + 1}`);
        Object.entries(row).forEach(([key, value]) => {
            if (typeof value === 'string' && (
                value.includes('NOin') || 
                value.includes('.000in25in') ||
                (value.includes('S/S 430') && value.includes('kg'))
            )) {
                console.log(`    Corrupted field [${key}]: ${value}`);
            }
        });
    } else {
        console.log(`  ✅ Row ${lineIndex + 1} is clean`);
    }
    
    return row;
});

// Test 3: Simulate HTML table rendering (like React component does)
console.log('\n🖥️ TEST 3: HTML table rendering simulation');
const tableHTML = [
    '<table>',
    '<thead><tr>',
    ...headers.map(h => `<th>${h}</th>`),
    '</tr></thead>',
    '<tbody>',
    ...rows.map(row => {
        const cells = headers.map(header => `<td>${row[header] || ''}</td>`);
        return `<tr>${cells.join('')}</tr>`;
    }),
    '</tbody>',
    '</table>'
].join('');

console.log('Generated HTML table:');
console.log(tableHTML);

// Test 4: Identify the exact corruption source
console.log('\n🔍 TEST 4: Corruption source analysis');
rows.forEach((row, i) => {
    headers.forEach((header, j) => {
        const value = row[header];
        if (value && value.includes('|')) {
            console.log(`🚨 PIPE FOUND IN CELL [${i}][${header}]: "${value}"`);
        }
        if (value && value.length > 100) {
            console.log(`⚠️ LONG CELL [${i}][${header}]: ${value.length} chars - "${value.substring(0, 50)}..."`);
        }
    });
});

console.log('🏁 Frontend parser test completed');