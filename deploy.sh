#!/bin/bash

# Steel Unit Converter Deployment Script for Aliyun
# This script deploys the application in production mode

set -e

# Display banner
echo "=================================================="
echo "  Steel Unit Converter - Aliyun Deployment Script  "
echo "=================================================="
echo

# Check if docker and docker-compose are installed
if ! command -v docker &> /dev/null; then
    echo "Error: docker is not installed. Please install docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "Error: docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

# Make sure we're in the project root directory
cd "$(dirname "$0")"

# Load environment variables
echo "Loading production environment variables..."
set -a
source .env.production
set +a

# Make sure the llm_prompt.txt file is available
if [ ! -f "./backend/llm_prompt.txt" ]; then
    echo "Error: llm_prompt.txt file not found in the backend directory."
    echo "Please make sure this file exists before deploying."
    exit 1
fi

# Check if SSL certificates exist
if [ ! -d "./ssl" ]; then
    echo "Creating SSL directory..."
    mkdir -p ./ssl
fi

if [ ! -f "./ssl/cert.pem" ] || [ ! -f "./ssl/key.pem" ]; then
    echo "Warning: SSL certificates not found in ./ssl directory."
    echo "You need to provide valid SSL certificates for HTTPS."
    echo "For now, creating self-signed certificates for testing..."

    # Generate self-signed certificates for testing
    openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
        -keyout ./ssl/key.pem -out ./ssl/cert.pem \
        -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"

    echo "Self-signed certificates created. Replace with valid certificates before production use."
fi

# Copy the Ubuntu-optimized Dockerfile to use
echo "Using Ubuntu-optimized Dockerfile that installs all dependencies with apt-get..."
cp Dockerfile.ubuntu Dockerfile

# Build and start the containers
echo "Building and starting containers in production mode..."
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d

# Check if containers are running
echo "Checking container status..."
sleep 5
if [ "$(docker ps -q -f name=steel-converter-backend)" ] && [ "$(docker ps -q -f name=steel-converter-frontend)" ]; then
    echo "Deployment successful! Application is now running."
    echo "Frontend is accessible at: https://steelnet.ai"
    echo "Backend API is accessible at: https://steelnet.ai/api/"
    echo "Health check: https://steelnet.ai/api/health"
else
    echo "Error: Deployment failed. Check logs with 'docker-compose -f docker-compose.prod.yml logs'"
    exit 1
fi

echo
echo "=================================================="
echo "  Deployment Complete  "
echo "=================================================="
