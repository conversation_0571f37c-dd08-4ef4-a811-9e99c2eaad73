#!/bin/bash

# Script to fix nginx configuration issues
# This script creates a proper nginx configuration that shows the application instead of directory listing

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
BACKEND_DIR="$SCRIPT_DIR/backend"
LOG_DIR="$SCRIPT_DIR/logs"

# Public IP address
PUBLIC_IP="************"

# Function to print section header
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Function to print success message
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print warning message
print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Function to print error message
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to detect nginx user
detect_nginx_user() {
    print_header "Detecting Nginx User"
    
    # Try to get nginx user from process list
    NGINX_USER=$(ps aux | grep -E "nginx.*worker" | grep -v grep | awk '{print $1}' | head -n 1)
    
    # If not found, try to get from configuration
    if [ -z "$NGINX_USER" ]; then
        if [ -f "/etc/nginx/nginx.conf" ]; then
            NGINX_USER=$(grep -E "^user" /etc/nginx/nginx.conf | awk '{print $2}' | sed 's/;$//')
        fi
    fi
    
    # If still not found, check common users
    if [ -z "$NGINX_USER" ]; then
        for user in www-data http nginx nobody; do
            if id -u "$user" >/dev/null 2>&1; then
                NGINX_USER="$user"
                break
            fi
        done
    fi
    
    # If still not found, use the current user
    if [ -z "$NGINX_USER" ]; then
        NGINX_USER=$(whoami)
        print_warning "Could not detect nginx user, using current user: $NGINX_USER"
    else
        print_success "Detected nginx user: $NGINX_USER"
    fi
    
    echo "NGINX_USER=$NGINX_USER"
    return 0
}

# Function to create SSL certificates
create_ssl_certificates() {
    print_header "Creating SSL Certificates"
    
    # Create SSL directory if it doesn't exist
    if [ ! -d "$SCRIPT_DIR/ssl" ]; then
        echo "Creating SSL directory..."
        mkdir -p "$SCRIPT_DIR/ssl"
    fi
    
    # Generate self-signed certificates if they don't exist
    if [ ! -f "$SCRIPT_DIR/ssl/cert.pem" ] || [ ! -f "$SCRIPT_DIR/ssl/key.pem" ]; then
        echo "Generating self-signed SSL certificates..."
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$SCRIPT_DIR/ssl/key.pem" -out "$SCRIPT_DIR/ssl/cert.pem" \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=steelnet.ai"
        
        if [ $? -eq 0 ]; then
            print_success "SSL certificates generated successfully"
            
            # Set proper permissions
            chmod 644 "$SCRIPT_DIR/ssl/cert.pem"
            chmod 600 "$SCRIPT_DIR/ssl/key.pem"
        else
            print_error "Failed to generate SSL certificates"
            return 1
        fi
    else
        print_success "SSL certificates already exist"
        
        # Set proper permissions
        chmod 644 "$SCRIPT_DIR/ssl/cert.pem"
        chmod 600 "$SCRIPT_DIR/ssl/key.pem"
    fi
    
    return 0
}

# Function to create proper nginx main configuration
create_nginx_main_config() {
    print_header "Creating Nginx Main Configuration"
    
    # Detect nginx user
    detect_nginx_user
    
    # Backup original nginx.conf
    if [ -f "/etc/nginx/nginx.conf" ]; then
        echo "Backing up original nginx.conf..."
        sudo cp "/etc/nginx/nginx.conf" "/etc/nginx/nginx.conf.bak.$(date +%Y%m%d%H%M%S)"
    fi
    
    # Create optimized nginx.conf
    echo "Creating optimized nginx.conf..."
    cat > /tmp/nginx.conf << EOF
# Run nginx as the detected user
user $NGINX_USER;

# Set number of worker processes based on available cores
worker_processes auto;

# Maximum number of open files per worker process
worker_rlimit_nofile 1024;

# Path to nginx PID file
pid /run/nginx.pid;

# Include dynamic modules
include /etc/nginx/modules-enabled/*.conf;

# Events block configuration
events {
    # Maximum number of connections per worker
    worker_connections 768;
    
    # Accept multiple connections at once
    multi_accept on;
}

# HTTP block configuration
http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # SSL settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384';
    
    # Logging settings
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    
    # Gzip settings
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Virtual Host Configs
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOF
    
    # Copy nginx.conf to nginx directory
    echo "Copying nginx.conf to /etc/nginx/nginx.conf..."
    sudo cp /tmp/nginx.conf "/etc/nginx/nginx.conf"
    
    # Test nginx configuration
    echo "Testing nginx configuration..."
    if sudo nginx -t; then
        print_success "Nginx main configuration is valid"
        return 0
    else
        print_error "Nginx main configuration is invalid"
        
        # Restore backup
        echo "Restoring backup..."
        sudo cp "/etc/nginx/nginx.conf.bak.$(date +%Y%m%d%H%M%S)" "/etc/nginx/nginx.conf"
        
        return 1
    fi
}

# Function to create proper nginx site configuration
create_nginx_site_config() {
    print_header "Creating Nginx Site Configuration"
    
    # Create a proper nginx configuration file
    echo "Creating nginx configuration file..."
    cat > /tmp/steelnet.conf << EOF
server {
    listen 80;
    listen [::]:80;
    server_name $PUBLIC_IP steelnet.ai;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://\$host\$request_uri;
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name $PUBLIC_IP steelnet.ai;
    
    # SSL configuration
    ssl_certificate $SCRIPT_DIR/ssl/cert.pem;
    ssl_certificate_key $SCRIPT_DIR/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    
    # SSL optimizations
    ssl_session_cache shared:SSL:2m;
    ssl_session_timeout 10m;
    
    # Root directory for static files
    root $FRONTEND_DIR/dist;
    index index.html;
    
    # Serve favicon.ico directly
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires max;
    }
    
    # Serve static files directly
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        access_log off;
        expires max;
        add_header Cache-Control "public, no-transform";
    }
    
    # Frontend application - important for SPA routing
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
    
    # Copy nginx configuration to nginx directory
    echo "Copying nginx configuration to /etc/nginx/conf.d/steelnet.conf..."
    sudo cp /tmp/steelnet.conf "/etc/nginx/conf.d/steelnet.conf"
    
    # Test nginx configuration
    echo "Testing nginx configuration..."
    if sudo nginx -t; then
        print_success "Nginx site configuration is valid"
        return 0
    else
        print_error "Nginx site configuration is invalid"
        return 1
    fi
}

# Function to check and fix frontend dist directory
check_frontend_dist() {
    print_header "Checking Frontend Dist Directory"
    
    # Check if frontend dist directory exists
    if [ ! -d "$FRONTEND_DIR/dist" ]; then
        print_error "Frontend dist directory not found at $FRONTEND_DIR/dist"
        
        # Ask if user wants to build the frontend
        echo -e "\nWould you like to build the frontend? (y/n)"
        read -r BUILD_FRONTEND
        
        if [[ "$BUILD_FRONTEND" =~ ^[Yy]$ ]]; then
            echo "Building frontend..."
            cd "$FRONTEND_DIR" || exit
            npm run build
            
            if [ -d "$FRONTEND_DIR/dist" ]; then
                print_success "Frontend built successfully"
            else
                print_error "Failed to build frontend"
                return 1
            fi
        else
            return 1
        fi
    else
        print_success "Frontend dist directory exists"
    fi
    
    # Check if index.html exists
    if [ ! -f "$FRONTEND_DIR/dist/index.html" ]; then
        print_error "index.html not found at $FRONTEND_DIR/dist/index.html"
        return 1
    else
        print_success "index.html exists"
    fi
    
    # Detect nginx user
    detect_nginx_user
    
    # Set proper permissions for frontend dist directory
    echo "Setting proper permissions for frontend dist directory..."
    sudo find "$FRONTEND_DIR/dist" -type d -exec chmod 755 {} \;
    sudo find "$FRONTEND_DIR/dist" -type f -exec chmod 644 {} \;
    
    # Create favicon.ico if it doesn't exist
    if [ ! -f "$FRONTEND_DIR/dist/favicon.ico" ]; then
        echo "Creating favicon.ico..."
        touch "$FRONTEND_DIR/dist/favicon.ico"
        chmod 644 "$FRONTEND_DIR/dist/favicon.ico"
    fi
    
    # Set ownership if running as root
    if [ "$(id -u)" -eq 0 ]; then
        sudo chown -R "$NGINX_USER:$NGINX_USER" "$FRONTEND_DIR/dist"
    fi
    
    print_success "Frontend dist directory permissions set"
    return 0
}

# Function to restart nginx
restart_nginx() {
    print_header "Restarting Nginx"
    
    echo "Restarting nginx..."
    sudo systemctl restart nginx || sudo service nginx restart || sudo /etc/init.d/nginx restart
    
    # Check if nginx restarted successfully
    if pgrep -x "nginx" > /dev/null; then
        print_success "Nginx restarted successfully"
        return 0
    else
        print_error "Failed to restart nginx"
        
        # Try to start nginx
        echo "Trying to start nginx..."
        sudo systemctl start nginx || sudo service nginx start || sudo /etc/init.d/nginx start
        
        if pgrep -x "nginx" > /dev/null; then
            print_success "Nginx started successfully"
            return 0
        else
            print_error "Failed to start nginx"
            return 1
        fi
    fi
}

# Main function
main() {
    print_header "Fixing Nginx Configuration"
    echo "This script will fix nginx configuration to show the application instead of directory listing"
    echo "Script directory: $SCRIPT_DIR"
    
    # Create SSL certificates
    create_ssl_certificates
    
    # Create proper nginx main configuration
    create_nginx_main_config
    
    # Create proper nginx site configuration
    create_nginx_site_config
    
    # Check and fix frontend dist directory
    check_frontend_dist
    
    # Restart nginx
    restart_nginx
    
    print_header "Summary"
    echo "Nginx configuration has been fixed."
    echo "You should now be able to access your application at:"
    echo "  HTTPS: https://$PUBLIC_IP"
    echo "  HTTPS: https://steelnet.ai"
    
    echo -e "\nIf you're still having issues, please check the logs:"
    echo "  nginx error log: sudo tail -f /var/log/nginx/error.log"
    echo "  nginx access log: sudo tail -f /var/log/nginx/access.log"
}

# Run the main function
main
