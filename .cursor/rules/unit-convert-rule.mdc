---
description: 
globs: 
alwaysApply: true
---
the project goal is at @goal.txt 
# General
- Use start-prod.sh to run the application and mcp for testing.
- Use MCP for testing to ensure the application is fully functional.
- Use RDS MySQL for database connection and nginx in production for the application.
- Create a Linux development script (start_dev_linux_with_url_access) for faster iteration on the production server, similar to start_dev.bat but for Linux environment.
- Create a setup_nginx_dev.sh script to ensure nginx is correctly configured when using start_dev_linux_with_url_access.sh.

# Production
- The production server has a 2GB memory limit.
- The production server cannot execute the build process, so builds should be done locally and then deployed to production.
- The start-prod.sh script hits memory allocation errors in production due to the memory limit.
- Configure backend routes to use the server's IP address instead of localhost for production environments. When deploying to production servers, all localhost URLs must be replaced with the server's public IP or domain name to ensure proper WebSocket connections.

# Development
- The application has CORS issues when accessing backend API from different origins (like ************* accessing localhost:8000) that need to be fixed in the start_dev_linux_with_url_access.sh script.
- Add 'steelnet.ai' to server.allowedHosts in vite.config.js to allow access from this domain.
- The application needs HTTPS configuration for steelnet.ai domain to prevent mixed content errors when accessing via HTTPS.
- The steelnet.ai domain is accessible from PC but not from mobile phones, indicating a potential cross-device compatibility issue.

# Features
- For the tabulation feature, output tables in HTML format (<table><thead><tr>...</tr></thead><tbody>...</tbody></table>) to make it easy for users to copy to Excel or other applications.
- For the tabulation feature, only include the 'Copy for Excel' button and remove the original copy button.

use browser-control MCP server to test and check the result
apply color theory in choosing colors