<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Streaming Table Formats</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .format-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .format-box {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .format-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            padding: 5px;
            background: #e9ecef;
            border-radius: 3px;
        }
        .test-input {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .test-output {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .copy-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            margin: 3px;
            font-size: 12px;
        }
        .copy-btn:hover {
            background: #0056b3;
        }
        .copy-status {
            color: #28a745;
            font-weight: bold;
            margin-left: 10px;
            font-size: 12px;
        }
        .streaming-demo {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 15px 0;
            font-size: 12px;
        }
        .metric-box {
            background: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
        }
        .metric-label {
            font-size: 11px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>Streaming Table Format Comparison</h1>
    <p>Testing different table formats for streaming efficiency and parsing performance.</p>

    <div class="test-section">
        <h2>Format Comparison</h2>
        <div class="format-comparison">
            <div class="format-box">
                <div class="format-title">1. Compact Streaming Format (NEW)</div>
                <div class="test-input">
<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015" x 2.343" x COIL|7190#|0.38mm x 59.51mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015" x 2.406" x COIL|8061#|0.38mm x 61.11mm x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015" x 16.50" x COIL|12550#|0.38mm x 419.1mm x COIL|5692.7 kg|BA Finish
</table_stream>
                </div>
                <div class="metrics">
                    <div class="metric-box">
                        <div class="metric-value" id="compact-size">-</div>
                        <div class="metric-label">Bytes</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value" id="compact-lines">-</div>
                        <div class="metric-label">Lines</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value" id="compact-parse">-</div>
                        <div class="metric-label">Parse Time</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value" id="compact-stream">✓</div>
                        <div class="metric-label">Streamable</div>
                    </div>
                </div>
            </div>

            <div class="format-box">
                <div class="format-title">2. JSON Structured Format</div>
                <div class="test-input">
<table_data>
{
  "headers": ["Item Code", "Description", "Size (Original)", "Customer QTY", "Size (Converted)", "Converted QTY", "Remarks"],
  "rows": [
    {
      "Item Code": "001",
      "Description": "S/S 430 BA NO PI",
      "Size (Original)": "0.015\" x 2.343\" x COIL",
      "Customer QTY": "7190#",
      "Size (Converted)": "0.38mm x 59.51mm x COIL",
      "Converted QTY": "3261.2 kg",
      "Remarks": "BA Finish"
    }
  ]
}
</table_data>
                </div>
                <div class="metrics">
                    <div class="metric-box">
                        <div class="metric-value" id="json-size">-</div>
                        <div class="metric-label">Bytes</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value" id="json-lines">-</div>
                        <div class="metric-label">Lines</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value" id="json-parse">-</div>
                        <div class="metric-label">Parse Time</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value" id="json-stream">✗</div>
                        <div class="metric-label">Streamable</div>
                    </div>
                </div>
            </div>

            <div class="format-box">
                <div class="format-title">3. Markdown Table Format</div>
                <div class="test-input">
| Item Code | Description | Size (Original) | Customer QTY | Size (Converted) | Converted QTY | Remarks |
|-----------|-------------|-----------------|--------------|------------------|---------------|---------|
| 001 | S/S 430 BA NO PI | 0.015" x 2.343" x COIL | 7190# | 0.38mm x 59.51mm x COIL | 3261.2 kg | BA Finish |
| 002 | S/S 430 BA NO PI | 0.015" x 2.406" x COIL | 8061# | 0.38mm x 61.11mm x COIL | 3656.7 kg | BA Finish |
| 003 | S/S 430 BA NO PI | 0.015" x 16.50" x COIL | 12550# | 0.38mm x 419.1mm x COIL | 5692.7 kg | BA Finish |
                </div>
                <div class="metrics">
                    <div class="metric-box">
                        <div class="metric-value" id="markdown-size">-</div>
                        <div class="metric-label">Bytes</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value" id="markdown-lines">-</div>
                        <div class="metric-label">Lines</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value" id="markdown-parse">-</div>
                        <div class="metric-label">Parse Time</div>
                    </div>
                    <div class="metric-box">
                        <div class="metric-value" id="markdown-stream">~</div>
                        <div class="metric-label">Streamable</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Real Test Case: Steel Conversion Data</h2>
        <p>Using your actual test case data with the new compact streaming format:</p>
        
        <div class="test-input" id="real-test-input">
<converted_content>
<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015(+/-0.0015) x 2.343"(+/-0.005) x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015(+/-0.0015) x 2.406"(+/-0.005) x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015(+/-0.0015) x 16.50"(+/-0.005) x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish
004|S/S 430 BA NO PI|0.015(+/-0.0015) x 19.68"(+/-0.03125) x COIL|8835#|0.38(+/-0.04)mm x 499.87(+/-0.79)mm x COIL|4007.9 kg|BA Finish
005|S/S 430 BA NO PI|0.015(+/-0.0015) x 47.438"(+/-0.03125) x COIL|57655#|0.38(+/-0.04)mm x 1204.93(+/-0.79)mm x COIL|26154.4 kg|BA Finish
006|S/S 430 BA NO PI|0.015(+/-0.0015) x 47.000"(+/-0.03125) x COIL|118001#|0.38(+/-0.04)mm x 1193.8(+/-0.79)mm x COIL|53524.5 kg|BA Finish
007|S/S 430 BA NO PI|0.015(+/-0.0015) x 35.438"(+/-0.03125) x COIL|62515#|0.38(+/-0.04)mm x 900.13(+/-0.79)mm x COIL|28360.3 kg|BA Finish
008|S/S 430 #2B NO PI|0.015(+/-0.0015) x 16.938"(+/-0.005) x COIL|5000#|0.38(+/-0.04)mm x 430.23(+/-0.13)mm x COIL|2268.0 kg|2B Finish
009|S/S 430 #2B NO PI|0.016(+/-0.0015) x 19.6875"(+/-0.005) x COIL|725321#|0.41(+/-0.04)mm x 500.06(+/-0.13)mm x COIL|329020.5 kg|2B Finish
010|S/S 430 #2B NO PI|0.016(+/-0.0015) x 35.500"(+/-0.03125) x COIL|122083#|0.41(+/-0.04)mm x 901.7(+/-0.79)mm x COIL|55384.6 kg|2B Finish
011|S/S 430 #2B NO PI|0.016(+/-0.0015) x 36.000"(+/-0.03125) x COIL|234265#|0.41(+/-0.04)mm x 914.4(+/-0.79)mm x COIL|106252.1 kg|2B Finish
012|S/S 430 #2B NO PI|0.016(+/-0.0015) x 48.000"(+/-0.03125) x COIL|201347#|0.41(+/-0.04)mm x 1219.2(+/-0.79)mm x COIL|91310.3 kg|2B Finish
013|S/S 430 #2B NO PI|0.018(+/-0.0015) x 36.000"(+/-0.03125) x COIL|33841#|0.46(+/-0.04)mm x 914.4(+/-0.79)mm x COIL|15346.4 kg|2B Finish
</table_stream>

All conversions completed successfully. Tolerances have been converted maintaining precision.
</converted_content>
        </div>
        
        <div class="test-output" id="real-test-output">
            <!-- Output will be generated here -->
        </div>
        
        <button class="copy-btn" onclick="copyTable('real-test')">Copy for Excel</button>
        <button class="copy-btn" onclick="copyTSV('real-test')">Copy as TSV</button>
        <button class="copy-btn" onclick="simulateStreaming()">Simulate Streaming</button>
        <span id="real-test-status" class="copy-status"></span>
    </div>

    <div class="test-section">
        <h2>Streaming Simulation</h2>
        <div class="streaming-demo">
            <h4>Progressive Table Building:</h4>
            <div id="streaming-output">Click "Simulate Streaming" to see progressive table building...</div>
        </div>
    </div>

    <script>
        // Import table parsing functions (simplified for testing)
        function parseConvertedContent(content) {
            const convertedContentMatch = content.match(/<converted_content>([\s\S]*?)<\/converted_content>/);
            
            if (convertedContentMatch) {
                const convertedText = convertedContentMatch[1].trim();
                const cleanContent = content.replace(/<converted_content>[\s\S]*?<\/converted_content>/, '').trim();
                
                // Check for compact streaming table format
                const tableStreamMatch = convertedText.match(/<table_stream>([\s\S]*?)<\/table_stream>/);
                if (tableStreamMatch) {
                    const tableContent = tableStreamMatch[1].trim();
                    const remainingContent = convertedText.replace(/<table_stream>[\s\S]*?<\/table_stream>/, '').trim();
                    
                    // Parse the compact table format
                    const lines = tableContent.split('\n').filter(line => line.trim());
                    if (lines.length >= 2) {
                        const headers = lines[0].split('|').map(h => h.trim());
                        const rows = lines.slice(1).map(line => {
                            const cells = line.split('|').map(c => c.trim());
                            const row = {};
                            headers.forEach((header, index) => {
                                row[header] = cells[index] || '';
                            });
                            return row;
                        });
                        
                        return {
                            hasTable: true,
                            tableContent: '',
                            cleanContent: cleanContent || remainingContent,
                            tableData: { headers, rows }
                        };
                    }
                }
            }
            
            return { hasTable: false, tableContent: '', cleanContent: content };
        }

        function renderTable(testId) {
            const inputElement = document.getElementById(`${testId}-input`);
            const outputElement = document.getElementById(`${testId}-output`);
            const content = inputElement.textContent;
            
            const { hasTable, cleanContent, tableData } = parseConvertedContent(content);
            
            let html = '';
            
            if (cleanContent) {
                html += `<h4>Text Content:</h4><p>${cleanContent}</p>`;
            }
            
            if (hasTable && tableData) {
                html += '<h4>Parsed Table:</h4>';
                html += '<table>';
                html += '<thead><tr>';
                tableData.headers.forEach(header => {
                    html += `<th>${header}</th>`;
                });
                html += '</tr></thead>';
                html += '<tbody>';
                tableData.rows.forEach(row => {
                    html += '<tr>';
                    tableData.headers.forEach(header => {
                        html += `<td>${row[header] || ''}</td>`;
                    });
                    html += '</tr>';
                });
                html += '</tbody></table>';
                
                // Store parsed table for copying
                window[`${testId}ParsedTable`] = tableData;
            }
            
            outputElement.innerHTML = html;
        }

        function copyTable(testId) {
            const parsedTable = window[`${testId}ParsedTable`];
            if (!parsedTable) return;
            
            // Create HTML table for Excel
            let html = '<table border="1" cellpadding="4" cellspacing="0" style="border-collapse: collapse;">';
            html += '<thead><tr style="background-color: #f0f0f0; font-weight: bold;">';
            parsedTable.headers.forEach(header => {
                html += `<th style="border: 1px solid #ccc; padding: 8px;">${header}</th>`;
            });
            html += '</tr></thead><tbody>';
            parsedTable.rows.forEach((row, rowIndex) => {
                const rowStyle = rowIndex % 2 === 0 ? 'background-color: #ffffff;' : 'background-color: #f9f9f9;';
                html += `<tr style="${rowStyle}">`;
                parsedTable.headers.forEach(header => {
                    html += `<td style="border: 1px solid #ccc; padding: 8px;">${row[header] || ''}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody></table>';
            
            // Copy to clipboard
            try {
                const clipboardItem = new ClipboardItem({
                    'text/html': new Blob([html], { type: 'text/html' })
                });
                navigator.clipboard.write([clipboardItem]).then(() => {
                    document.getElementById(`${testId}-status`).textContent = 'Copied as HTML!';
                    setTimeout(() => {
                        document.getElementById(`${testId}-status`).textContent = '';
                    }, 2000);
                });
            } catch (error) {
                console.error('Copy failed:', error);
                document.getElementById(`${testId}-status`).textContent = 'Copy failed';
            }
        }

        function copyTSV(testId) {
            const parsedTable = window[`${testId}ParsedTable`];
            if (!parsedTable) return;
            
            // Create TSV format
            let tsv = parsedTable.headers.join('\t') + '\n';
            parsedTable.rows.forEach(row => {
                const rowData = parsedTable.headers.map(header => row[header] || '');
                tsv += rowData.join('\t') + '\n';
            });
            
            navigator.clipboard.writeText(tsv).then(() => {
                document.getElementById(`${testId}-status`).textContent = 'Copied as TSV!';
                setTimeout(() => {
                    document.getElementById(`${testId}-status`).textContent = '';
                }, 2000);
            });
        }

        function simulateStreaming() {
            const streamingOutput = document.getElementById('streaming-output');
            const fullContent = document.getElementById('real-test-input').textContent;
            
            // Extract table content
            const tableMatch = fullContent.match(/<table_stream>([\s\S]*?)<\/table_stream>/);
            if (!tableMatch) return;
            
            const tableContent = tableMatch[1].trim();
            const lines = tableContent.split('\n');
            
            streamingOutput.innerHTML = '<h4>Streaming in progress...</h4>';
            
            let currentContent = '<table_stream>\n';
            let lineIndex = 0;
            
            const streamInterval = setInterval(() => {
                if (lineIndex < lines.length) {
                    currentContent += lines[lineIndex] + '\n';
                    lineIndex++;
                    
                    // Try to parse current content
                    const tempContent = currentContent + '</table_stream>';
                    const { hasTable, tableData } = parseConvertedContent(`<converted_content>${tempContent}</converted_content>`);
                    
                    let html = `<h4>Streaming Progress: ${lineIndex}/${lines.length} lines</h4>`;
                    
                    if (hasTable && tableData) {
                        html += '<table>';
                        html += '<thead><tr>';
                        tableData.headers.forEach(header => {
                            html += `<th>${header}</th>`;
                        });
                        html += '</tr></thead>';
                        html += '<tbody>';
                        tableData.rows.forEach(row => {
                            html += '<tr>';
                            tableData.headers.forEach(header => {
                                html += `<td>${row[header] || ''}</td>`;
                            });
                            html += '</tr>';
                        });
                        html += '</tbody></table>';
                    } else {
                        html += '<p>Waiting for table data...</p>';
                    }
                    
                    streamingOutput.innerHTML = html;
                } else {
                    clearInterval(streamInterval);
                    streamingOutput.innerHTML += '<p style="color: green; font-weight: bold;">✓ Streaming completed!</p>';
                }
            }, 200); // Add a line every 200ms
        }

        function calculateMetrics() {
            // Calculate format metrics
            const formats = [
                { id: 'compact', selector: '.format-box:nth-child(1) .test-input' },
                { id: 'json', selector: '.format-box:nth-child(2) .test-input' },
                { id: 'markdown', selector: '.format-box:nth-child(3) .test-input' }
            ];
            
            formats.forEach(format => {
                const element = document.querySelector(format.selector);
                const content = element.textContent;
                const size = new Blob([content]).size;
                const lines = content.split('\n').length;
                
                document.getElementById(`${format.id}-size`).textContent = size;
                document.getElementById(`${format.id}-lines`).textContent = lines;
                
                // Simulate parse time
                const start = performance.now();
                parseConvertedContent(content);
                const parseTime = (performance.now() - start).toFixed(2);
                document.getElementById(`${format.id}-parse`).textContent = parseTime + 'ms';
            });
        }

        // Initialize on page load
        window.onload = function() {
            renderTable('real-test');
            calculateMetrics();
        };
    </script>
</body>
</html>
