const { chromium } = require('playwright');

/**
 * Comprehensive MCP Test for Steel Unit Converter User Interaction Features
 * Tests all implemented functionality systematically
 */

async function runMCPTests() {
    console.log('🚀 Starting MCP Comprehensive Testing');
    console.log('=====================================');
    
    const browser = await chromium.launch({ 
        headless: false, // Show browser for visual verification
        slowMo: 1000 // Slow down actions for better observation
    });
    
    const context = await browser.newContext();
    const page = await context.newPage();
    
    // Test results tracking
    const testResults = {
        passed: 0,
        failed: 0,
        details: []
    };
    
    function logTest(testName, passed, details = '') {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${status}: ${testName}`);
        if (details) console.log(`   ${details}`);
        
        testResults.details.push({ testName, passed, details });
        if (passed) testResults.passed++;
        else testResults.failed++;
    }
    
    try {
        // Phase 1: Access the Application
        console.log('\n📋 Phase 1: Accessing Application');
        console.log('--------------------------------');
        
        await page.goto('http://localhost:5173');
        await page.waitForLoadState('networkidle');
        
        // Check if the page loaded correctly
        const title = await page.title();
        logTest('Application loads correctly', title.includes('Steel'), `Title: ${title}`);
        
        // Check for chat interface
        const chatExists = await page.locator('[data-testid="chat-interface"], .chat-container, input[placeholder*="message"], input[placeholder*="输入"]').count() > 0;
        logTest('Chat interface is present', chatExists);
        
        // Phase 2: Test User Message Features
        console.log('\n📋 Phase 2: Testing User Message Features');
        console.log('----------------------------------------');
        
        // Find the input field
        const inputSelector = 'input[type="text"], textarea, input[placeholder*="message"], input[placeholder*="输入"]';
        await page.waitForSelector(inputSelector, { timeout: 10000 });
        
        // Send a test message
        const testMessage = "Convert 1 inch to mm";
        await page.fill(inputSelector, testMessage);
        
        // Find and click send button
        const sendButton = page.locator('button[type="submit"], button:has-text("发送"), button:has-text("Send"), button:has([data-testid="send-icon"])').first();
        await sendButton.click();
        
        logTest('User message sent successfully', true, testMessage);
        
        // Wait for user message to appear
        await page.waitForTimeout(2000);
        
        // Check for user message with action buttons
        const userMessageExists = await page.locator('.MuiBox-root:has-text("Convert 1 inch to mm")').count() > 0;
        logTest('User message appears in chat', userMessageExists);
        
        // Wait for user message action buttons to render
        await page.waitForTimeout(3000);

        // Check for copy button on user message using test ID
        const userCopyButton = page.locator('[data-testid="user-copy-button"]');
        const userCopyExists = await userCopyButton.count() > 0;
        logTest('User message has copy button', userCopyExists);

        if (userCopyExists) {
            await userCopyButton.click();
            await page.waitForTimeout(1000);
            logTest('User message copy button works', true, 'Copy button clicked');
        }

        // Check for like button on user message using test ID
        const userLikeButton = page.locator('[data-testid="user-like-button"]');
        const userLikeExists = await userLikeButton.count() > 0;
        logTest('User message has like button', userLikeExists);

        if (userLikeExists) {
            await userLikeButton.click();
            await page.waitForTimeout(1000);
            logTest('User message like button works', true, 'Like button clicked');
        }

        // Check for report error button using test ID
        const userReportButton = page.locator('[data-testid="user-report-button"]');
        const userReportExists = await userReportButton.count() > 0;
        logTest('User message has report error button', userReportExists);

        // Check for feedback button using test ID
        const userFeedbackButton = page.locator('[data-testid="user-feedback-button"]');
        const userFeedbackExists = await userFeedbackButton.count() > 0;
        logTest('User message has feedback button', userFeedbackExists);
        
        // Phase 3: Test AI Response Features
        console.log('\n📋 Phase 3: Testing AI Response Features');
        console.log('---------------------------------------');
        
        // Wait for AI response (longer timeout for API call)
        console.log('Waiting for AI response...');
        await page.waitForTimeout(10000);
        
        // Check for AI response
        const aiResponseExists = await page.locator('.MuiBox-root:has([data-testid="assistant-avatar"]), .MuiBox-root:has-text("25.4")').count() > 0;
        logTest('AI response appears', aiResponseExists);
        
        if (aiResponseExists) {
            // Check for AI response action buttons using test IDs
            const aiCopyButton = page.locator('[data-testid="ai-copy-button"]');
            const aiCopyExists = await aiCopyButton.count() > 0;
            logTest('AI response has copy button', aiCopyExists);

            const aiLikeButton = page.locator('[data-testid="ai-like-button"]');
            const aiLikeExists = await aiLikeButton.count() > 0;
            logTest('AI response has like button', aiLikeExists);

            // Test AI response copy functionality
            if (aiCopyExists) {
                await aiCopyButton.click();
                await page.waitForTimeout(1000);
                logTest('AI response copy button works', true, 'AI copy button clicked');
            }

            // Test AI response like functionality
            if (aiLikeExists) {
                await aiLikeButton.click();
                await page.waitForTimeout(1000);
                logTest('AI response like button works', true, 'AI like button clicked');
            }
        } else {
            logTest('AI response copy functionality', false, 'No AI response to test');
            logTest('AI response like functionality', false, 'No AI response to test');
        }
        
        // Phase 4: Test Chat History Persistence
        console.log('\n📋 Phase 4: Testing Chat History Persistence');
        console.log('--------------------------------------------');
        
        // Count messages before refresh
        const messagesBefore = await page.locator('.MuiBox-root:has-text("Convert 1 inch to mm"), .MuiBox-root:has-text("25.4")').count();
        console.log(`Messages before refresh: ${messagesBefore}`);
        
        // Refresh the page
        await page.reload();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Count messages after refresh
        const messagesAfter = await page.locator('.MuiBox-root:has-text("Convert 1 inch to mm"), .MuiBox-root:has-text("25.4")').count();
        console.log(`Messages after refresh: ${messagesAfter}`);
        
        logTest('Chat history persists after refresh', messagesAfter > 0, `${messagesAfter} messages restored`);
        
        // Check if like status persists by looking for filled thumbs up icons
        const likedButtonsAfter = await page.locator('[data-testid="user-like-button"][color="primary"], [data-testid="ai-like-button"][color="primary"]').count();
        logTest('Like status persists after refresh', likedButtonsAfter > 0, `${likedButtonsAfter} liked messages`);
        
        // Phase 5: Test Table Functionality
        console.log('\n📋 Phase 5: Testing Table Functionality');
        console.log('--------------------------------------');
        
        // Send table test data
        const tableTestData = `S/S 430 BA NO PI
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#`;
        
        await page.fill(inputSelector, tableTestData);
        await sendButton.click();
        
        logTest('Table test data sent', true, 'Steel specifications data sent');
        
        // Wait for table response
        console.log('Waiting for table response...');
        await page.waitForTimeout(15000);
        
        // Check for table elements
        const tableExists = await page.locator('table, .MuiTable-root, [data-testid="table"]').count() > 0;
        logTest('Table generated in response', tableExists);
        
        // Check for "Copy for Excel" button
        const excelCopyButton = await page.locator('button:has-text("Copy for Excel"), button:has-text("复制到Excel")').count() > 0;
        logTest('Copy for Excel button present', excelCopyButton);
        
        if (excelCopyButton) {
            await page.locator('button:has-text("Copy for Excel"), button:has-text("复制到Excel")').first().click();
            await page.waitForTimeout(1000);
            logTest('Copy for Excel functionality works', true, 'Excel copy button clicked');
        }
        
    } catch (error) {
        console.error('❌ Test execution error:', error.message);
        logTest('Test execution', false, error.message);
    }
    
    // Final Results
    console.log('\n📊 Test Results Summary');
    console.log('======================');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
    
    console.log('\n📋 Detailed Results:');
    testResults.details.forEach(result => {
        const status = result.passed ? '✅' : '❌';
        console.log(`${status} ${result.testName}`);
        if (result.details) console.log(`   ${result.details}`);
    });
    
    // Keep browser open for manual verification
    console.log('\n🔍 Browser will remain open for manual verification...');
    console.log('Press Ctrl+C to close when done.');
    
    // Wait indefinitely to keep browser open
    await new Promise(() => {});
    
    await browser.close();
}

// Run the tests
runMCPTests().catch(console.error);
