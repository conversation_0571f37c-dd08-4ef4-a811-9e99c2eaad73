#!/bin/bash

# Script to fix the favicon.ico 502 Bad Gateway issue
# This script creates a favicon.ico file in the frontend/dist directory

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
DIST_DIR="$FRONTEND_DIR/dist"

# Function to print section header
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Function to print success message
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print warning message
print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Function to print error message
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to create a simple favicon.ico file
create_favicon() {
    print_header "Creating favicon.ico file"
    
    # Check if the dist directory exists
    if [ ! -d "$DIST_DIR" ]; then
        print_error "Frontend dist directory not found at $DIST_DIR"
        
        # Ask if user wants to create the directory
        echo -e "\nWould you like to create the dist directory? (y/n)"
        read -r CREATE_DIR
        
        if [[ "$CREATE_DIR" =~ ^[Yy]$ ]]; then
            echo "Creating dist directory..."
            mkdir -p "$DIST_DIR"
        else
            return 1
        fi
    fi
    
    # Check if curl is installed
    if ! command -v curl &> /dev/null; then
        print_warning "curl is not installed. Installing..."
        if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y curl
        elif command -v yum &> /dev/null; then
            sudo yum install -y curl
        else
            print_error "Failed to install curl. Please install it manually."
            return 1
        fi
    fi
    
    # Download a simple favicon.ico file
    echo "Downloading a simple favicon.ico file..."
    curl -s -o "$DIST_DIR/favicon.ico" https://www.google.com/favicon.ico
    
    # Check if the download was successful
    if [ -f "$DIST_DIR/favicon.ico" ]; then
        print_success "favicon.ico file created successfully at $DIST_DIR/favicon.ico"
        
        # Restart the frontend server
        echo -e "\nWould you like to restart the frontend server? (y/n)"
        read -r RESTART_FRONTEND
        
        if [[ "$RESTART_FRONTEND" =~ ^[Yy]$ ]]; then
            echo "Restarting frontend server..."
            "$SCRIPT_DIR/start-prod.sh" --restart --frontend
        fi
        
        return 0
    else
        print_error "Failed to create favicon.ico file"
        
        # Try to create an empty favicon.ico file
        echo "Creating an empty favicon.ico file..."
        touch "$DIST_DIR/favicon.ico"
        
        if [ -f "$DIST_DIR/favicon.ico" ]; then
            print_success "Empty favicon.ico file created successfully at $DIST_DIR/favicon.ico"
            
            # Restart the frontend server
            echo -e "\nWould you like to restart the frontend server? (y/n)"
            read -r RESTART_FRONTEND
            
            if [[ "$RESTART_FRONTEND" =~ ^[Yy]$ ]]; then
                echo "Restarting frontend server..."
                "$SCRIPT_DIR/start-prod.sh" --restart --frontend
            fi
            
            return 0
        else
            print_error "Failed to create empty favicon.ico file"
            return 1
        fi
    fi
}

# Function to check if nginx is configured to serve favicon.ico
check_nginx_config() {
    print_header "Checking nginx configuration for favicon.ico"
    
    # Check if nginx is installed
    if ! command -v nginx &> /dev/null; then
        print_warning "nginx is not installed. Skipping nginx configuration check."
        return 1
    fi
    
    # Check if steelnet.conf exists
    if [ ! -f "/etc/nginx/conf.d/steelnet.conf" ]; then
        print_warning "steelnet.conf not found. Skipping nginx configuration check."
        return 1
    fi
    
    # Check if the configuration includes a location for favicon.ico
    if grep -q "location[[:space:]]\+/favicon.ico" "/etc/nginx/conf.d/steelnet.conf"; then
        print_success "nginx is already configured to serve favicon.ico"
        return 0
    else
        print_warning "nginx is not configured to serve favicon.ico"
        
        # Ask if user wants to update the nginx configuration
        echo -e "\nWould you like to update the nginx configuration to serve favicon.ico? (y/n)"
        read -r UPDATE_CONFIG
        
        if [[ "$UPDATE_CONFIG" =~ ^[Yy]$ ]]; then
            echo "Updating nginx configuration..."
            
            # Create a temporary file with the updated configuration
            sed '/location \/ {/i \
    # Serve favicon.ico directly\
    location = /favicon.ico {\
        alias '"$DIST_DIR"'/favicon.ico;\
        access_log off;\
        log_not_found off;\
        expires max;\
    }\
' "/etc/nginx/conf.d/steelnet.conf" > "/tmp/steelnet.conf"
            
            # Copy the updated configuration to the nginx directory
            sudo cp "/tmp/steelnet.conf" "/etc/nginx/conf.d/steelnet.conf"
            
            # Test nginx configuration
            echo "Testing nginx configuration..."
            if sudo nginx -t; then
                print_success "nginx configuration is valid"
                
                # Reload nginx
                echo "Reloading nginx..."
                sudo systemctl reload nginx || sudo service nginx reload
                
                print_success "nginx configuration updated successfully"
                return 0
            else
                print_error "nginx configuration is invalid"
                
                # Restore the original configuration
                sudo cp "/etc/nginx/conf.d/steelnet.conf.bak" "/etc/nginx/conf.d/steelnet.conf"
                
                return 1
            fi
        fi
    fi
}

# Main function
main() {
    print_header "Fixing favicon.ico 502 Bad Gateway issue"
    echo "This script will create a favicon.ico file in the frontend/dist directory"
    echo "Script directory: $SCRIPT_DIR"
    echo "Frontend directory: $FRONTEND_DIR"
    echo "Dist directory: $DIST_DIR"
    
    # Create favicon.ico file
    create_favicon
    
    # Check nginx configuration
    check_nginx_config
    
    print_header "Summary"
    echo "The favicon.ico issue should now be fixed."
    echo "If you're still having issues, please check the logs:"
    echo "  Frontend logs: $SCRIPT_DIR/logs/frontend.log"
    echo "  nginx error log: /var/log/nginx/error.log"
    echo "  nginx access log: /var/log/nginx/access.log"
    
    echo -e "\nYou can also try restarting the application with:"
    echo "  ./start-prod.sh --restart"
}

# Run the main function
main
