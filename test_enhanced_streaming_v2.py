#!/usr/bin/env python3
"""
Test the enhanced streaming table implementation with stricter validation
"""
import requests
import json
import time

def test_enhanced_streaming():
    """Test the enhanced streaming implementation with better prompt"""
    
    # Use a larger dataset to test robustness
    test_data = """S/S 430  BA  NO PI
 
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#
.015(+/-.0015) X 19.68"(+/-.03125) X COIL                                         8,835#
.015(+/-.0015) X 47.438"(+/-.03125) X COIL                                      57,655#
.015(+/-.0015) X 47.000"(+/-.03125) X COIL                                      118,001#
.015(+/-.0015) X 35.438"(+/-.03125) X COIL                                      62,515#
 
S/S 430 #2B NO PI
 
.015(+/-.0015) X 16.938"(+/-.005) X COIL                                           5,000#
.016(+/-.0015) X 19.6875"(+/-.005) X COIL                                         725,321#
.016(+/-.0015) X 35.500"(+/-.03125) X COIL                                      122,083#
.016(+/-.0015) X 36.000"(+/-.03125) X COIL                                      234,265#
.016(+/-.0015) X 48.000"(+/-.03125) X COIL                                      201,347#
.018(+/-.0015) X 36.000"(+/-.03125) X COIL                                      33,841#"""

    url = "http://localhost:8000/api/llm/stream"
    payload = {
        "text": test_data,
        "function": "table",
        "unit_system": "metric"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    print("🧪 Testing Enhanced Streaming Table Implementation v2")
    print("=" * 70)
    print(f"📍 URL: {url}")
    lines_count = len(test_data.strip().split('\n'))
    print(f"📋 Test Data: {lines_count} lines of steel data")
    print("=" * 70)
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True)
        
        if response.status_code == 200:
            print("✅ Stream started successfully!")
            accumulated_data = ""
            chunk_count = 0
            header_detected = False
            table_start_detected = False
            
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    
                    if decoded_line.startswith('data: '):
                        try:
                            data_str = decoded_line[6:]
                            if data_str == '[DONE]':
                                print("\\n🏁 Stream completed successfully!")
                                break
                            
                            data = json.loads(data_str)
                            if 'content' in data:
                                chunk_content = data['content']
                                accumulated_data += chunk_content
                                chunk_count += 1
                                
                                # Check for table stream start
                                if '<table_stream>' in accumulated_data and not table_start_detected:
                                    print(f"\\n📊 Table stream started at chunk {chunk_count}")
                                    table_start_detected = True
                                
                                # Check for complete header line with enhanced detection
                                header_patterns = [
                                    'Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks',
                                    'Item Code' and 'Description' and 'Remarks'
                                ]
                                
                                if not header_detected and any(pattern in accumulated_data for pattern in header_patterns if isinstance(pattern, str)):
                                    print(f"✅ Table header detected at chunk {chunk_count}!")
                                    print("🎯 Expected format: Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks")
                                    header_detected = True
                                
                                # Show progress every 25 chunks
                                if chunk_count % 25 == 0:
                                    print(f"📈 Progress: {chunk_count} chunks, {len(accumulated_data)} chars")
                                
                        except json.JSONDecodeError:
                            pass
            
            print("\\n" + "=" * 70)
            print("📊 ENHANCED STREAMING ANALYSIS:")
            print("=" * 70)
            
            # Parse the final result with enhanced validation
            import re
            match = re.search(r'<table_stream>\s*([\s\S]*?)\s*</table_stream>', accumulated_data)
            if match:
                table_content = match.group(1).strip()
                lines = [line.strip() for line in table_content.split('\n') if line.strip()]
                
                if lines:
                    # Enhanced header validation
                    header_line = lines[0]
                    expected_header = 'Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks'
                    
                    print(f"✅ Table parsed successfully!")
                    print(f"📋 Header line: {header_line}")
                    print(f"🎯 Expected: {expected_header}")
                    print(f"🔍 Header match: {header_line == expected_header}")
                    
                    # Parse headers
                    headers = [h.strip() for h in header_line.split('|')]
                    print(f"📊 Header count: {len(headers)}")
                    print(f"📊 Headers: {headers}")
                    
                    # Parse data rows with enhanced validation
                    data_rows = lines[1:]
                    print(f"📊 Data rows: {len(data_rows)}")
                    
                    # Validate each row
                    valid_rows = 0
                    corrupted_rows = 0
                    
                    for i, row in enumerate(data_rows, 1):
                        cells = [c.strip() for c in row.split('|')]
                        if len(cells) == 7:
                            valid_rows += 1
                            if i <= 3:  # Show first 3 rows
                                print(f"✅ Row {i} ({len(cells)} cells): {cells}")
                        else:
                            corrupted_rows += 1
                            print(f"❌ Row {i} CORRUPTED ({len(cells)} cells): {cells}")
                    
                    print(f"\\n📈 VALIDATION SUMMARY:")
                    print(f"✅ Valid rows: {valid_rows}")
                    print(f"❌ Corrupted rows: {corrupted_rows}")
                    print(f"📊 Success rate: {(valid_rows/(valid_rows+corrupted_rows)*100):.1f}%")
                    
                    if len(headers) == 7 and valid_rows > 0 and corrupted_rows == 0:
                        print("\\n🎉 SUCCESS: Perfect table structure with 7 columns!")
                        return True
                    elif len(headers) == 7 and valid_rows > 0:
                        print(f"\\n⚠️  PARTIAL SUCCESS: {valid_rows} valid rows, {corrupted_rows} corrupted")
                        return False
                    else:
                        print(f"\\n💥 FAILURE: Invalid table structure")
                        return False
                else:
                    print("❌ No table lines found")
                    return False
            else:
                print("❌ No table_stream content found")
                print(f"📄 Raw data preview: {accumulated_data[:500]}...")
                return False
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"💥 Request failed: {e}")
        return False

if __name__ == "__main__":
    success = test_enhanced_streaming()
    
    print("\\n" + "=" * 70)
    if success:
        print("🎉 ENHANCED STREAMING TEST: PERFECT SUCCESS!")
        print("🚀 The streaming table implementation is working correctly!")
        print("✅ All columns are properly aligned and data is intact")
    else:
        print("💥 ENHANCED STREAMING TEST: ISSUES DETECTED")
        print("🔧 Further improvements needed for robust streaming")
    
    print("\\n💡 Next steps:")
    print("1. Open http://localhost:5173 in your browser")
    print("2. Select 'Table' function")
    print("3. Paste the test data and submit")
    print("4. Verify that all 7 columns display correctly")
    print("5. Check browser console for detailed debug logs")