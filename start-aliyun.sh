#!/usr/bin/env bash

# Steel Unit Converter - <PERSON><PERSON> ECS Startup Script
# This script starts the application in production mode on Aliyun ECS

set -e

# Display banner
echo "=================================================="
echo "  Steel Unit Converter - Aliyun ECS Startup Script "
echo "=================================================="
echo

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
LOG_DIR="$SCRIPT_DIR/logs"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Check if required tools are installed
check_requirements() {
    echo "Checking system requirements..."

    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        echo "Error: Docker is not installed. Please install Docker first."
        exit 1
    fi

    # Check if docker-compose is installed
    if ! command -v docker-compose &> /dev/null; then
        echo "Error: Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi

    # Check if the .env.production file exists
    if [ ! -f "$SCRIPT_DIR/.env.production" ]; then
        echo "Error: .env.production file not found. Please create it first."
        exit 1
    fi

    echo "All requirements satisfied."
}

# Check if containers are already running
check_running_containers() {
    echo "Checking for running containers..."

    if [ "$(docker ps -q -f name=steel-converter-backend)" ] || [ "$(docker ps -q -f name=steel-converter-frontend)" ]; then
        echo "Warning: Steel Unit Converter containers are already running."
        read -p "Do you want to stop them and restart? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "Stopping existing containers..."
            docker-compose -f docker-compose.prod.yml down
        else
            echo "Exiting without restarting services."
            exit 0
        fi
    fi
}

# Start the application using Docker Compose
start_application() {
    echo "Starting Steel Unit Converter in production mode..."

    # Load environment variables
    set -a
    source "$SCRIPT_DIR/.env.production"
    set +a

    # Check if SSL certificates exist
    if [ ! -d "$SCRIPT_DIR/ssl" ]; then
        echo "Creating SSL directory..."
        mkdir -p "$SCRIPT_DIR/ssl"
    fi

    if [ ! -f "$SCRIPT_DIR/ssl/cert.pem" ] || [ ! -f "$SCRIPT_DIR/ssl/key.pem" ]; then
        echo "Warning: SSL certificates not found in ./ssl directory."
        echo "You need to provide valid SSL certificates for HTTPS."
        echo "For now, creating self-signed certificates for testing..."

        # Generate self-signed certificates for testing
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$SCRIPT_DIR/ssl/key.pem" -out "$SCRIPT_DIR/ssl/cert.pem" \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=steelnet.ai"

        echo "Self-signed certificates created. Replace with valid certificates for production use."
    fi

    # Copy the Ubuntu-optimized Dockerfile to use
    echo "Using Ubuntu-optimized Dockerfile that installs all dependencies with apt-get..."
    cp "$SCRIPT_DIR/Dockerfile.ubuntu" "$SCRIPT_DIR/Dockerfile"

    # Start the application with Docker Compose
    echo "Starting containers..."
    docker-compose -f "$SCRIPT_DIR/docker-compose.prod.yml" up -d

    # Check if containers are running
    echo "Checking container status..."
    sleep 5
    if [ "$(docker ps -q -f name=steel-converter-backend)" ] && [ "$(docker ps -q -f name=steel-converter-frontend)" ]; then
        echo "Application started successfully!"
        echo "Frontend is accessible at: https://steelnet.ai"
        echo "Backend API is accessible at: https://steelnet.ai/api/"
        echo "Health check: https://steelnet.ai/api/health"
    else
        echo "Error: Failed to start containers. Check logs for details."
        docker-compose -f "$SCRIPT_DIR/docker-compose.prod.yml" logs
        exit 1
    fi
}

# Setup system service for auto-start on boot
setup_system_service() {
    echo "Setting up system service for auto-start on boot..."

    # Create systemd service file
    SERVICE_FILE="/etc/systemd/system/steel-unit-converter.service"

    if [ -f "$SERVICE_FILE" ]; then
        echo "Service file already exists. Skipping creation."
    else
        echo "Creating systemd service file..."

        # Create service file with sudo
        cat << EOF | sudo tee "$SERVICE_FILE" > /dev/null
[Unit]
Description=Steel Unit Converter Application
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$SCRIPT_DIR
ExecStart=/usr/bin/docker-compose -f $SCRIPT_DIR/docker-compose.prod.yml up -d
ExecStop=/usr/bin/docker-compose -f $SCRIPT_DIR/docker-compose.prod.yml down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

        # Reload systemd
        sudo systemctl daemon-reload

        # Enable the service
        sudo systemctl enable steel-unit-converter.service

        echo "System service created and enabled. The application will start automatically on boot."
    fi
}

# Setup log rotation
setup_log_rotation() {
    echo "Setting up log rotation..."

    # Create logrotate configuration file
    LOGROTATE_FILE="/etc/logrotate.d/steel-unit-converter"

    if [ -f "$LOGROTATE_FILE" ]; then
        echo "Log rotation already configured. Skipping."
    else
        echo "Creating logrotate configuration..."

        # Create logrotate config with sudo
        cat << EOF | sudo tee "$LOGROTATE_FILE" > /dev/null
$LOG_DIR/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0640 root root
    sharedscripts
    postrotate
        docker-compose -f $SCRIPT_DIR/docker-compose.prod.yml restart > /dev/null 2>&1 || true
    endscript
}
EOF

        echo "Log rotation configured."
    fi
}

# Setup firewall rules
setup_firewall() {
    echo "Setting up firewall rules..."

    # Check if firewalld is installed and running
    if command -v firewall-cmd &> /dev/null && systemctl is-active --quiet firewalld; then
        echo "Configuring firewalld..."

        # Add HTTP and HTTPS ports
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --permanent --add-service=https

        # Reload firewall
        sudo firewall-cmd --reload

        echo "Firewall configured to allow HTTP and HTTPS traffic."
    # Check if ufw is installed and running
    elif command -v ufw &> /dev/null && ufw status | grep -q "active"; then
        echo "Configuring ufw..."

        # Add HTTP and HTTPS ports
        sudo ufw allow http
        sudo ufw allow https

        echo "Firewall configured to allow HTTP and HTTPS traffic."
    else
        echo "No supported firewall detected. Please manually configure your firewall to allow ports 80 and 443."
    fi
}

# Main function
main() {
    check_requirements
    check_running_containers
    start_application

    # Ask if user wants to set up system service
    read -p "Do you want to set up the application as a system service for auto-start on boot? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_system_service
    fi

    # Ask if user wants to set up log rotation
    read -p "Do you want to set up log rotation? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_log_rotation
    fi

    # Ask if user wants to set up firewall rules
    read -p "Do you want to configure firewall rules? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_firewall
    fi

    echo
    echo "=================================================="
    echo "  Steel Unit Converter is now running!  "
    echo "=================================================="
    echo
    echo "You can manage the application with these commands:"
    echo
    echo "  Start:   docker-compose -f $SCRIPT_DIR/docker-compose.prod.yml up -d"
    echo "  Stop:    docker-compose -f $SCRIPT_DIR/docker-compose.prod.yml down"
    echo "  Restart: docker-compose -f $SCRIPT_DIR/docker-compose.prod.yml restart"
    echo "  Logs:    docker-compose -f $SCRIPT_DIR/docker-compose.prod.yml logs -f"
    echo
    echo "If you set up the system service, you can also use:"
    echo
    echo "  Start:   sudo systemctl start steel-unit-converter"
    echo "  Stop:    sudo systemctl stop steel-unit-converter"
    echo "  Status:  sudo systemctl status steel-unit-converter"
    echo
    echo "Application URLs:"
    echo "  Frontend: https://steelnet.ai"
    echo "  API:      https://steelnet.ai/api"
    echo "  Health:   https://steelnet.ai/api/health"
    echo
}

# Run the main function
main
