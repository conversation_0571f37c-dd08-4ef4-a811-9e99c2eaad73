<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-output {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Table Rendering Fix Test</h1>
    
    <div class="test-section">
        <h2>Test: Simulated Backend Response with &lt;converted_content&gt; Tags</h2>
        <button onclick="testStreamingWithTables()">Test Table Rendering Fix</button>
        <div id="output" class="test-output"></div>
    </div>

    <script>
        // Simulated table utilities from frontend
        function containsMarkdownTable(text) {
            if (!text || typeof text !== 'string') return false;
            const lines = text.trim().split('\n').filter(line => line.trim().length > 0);
            if (lines.length < 2) return false;
            
            // Look for pipe characters and separator lines
            const hasPipes = lines.some(line => line.includes('|'));
            const hasSeparator = lines.some(line => line.match(/^[\|\s]*[-:]+[\|\s-:]*$/));
            
            return hasPipes && (hasSeparator || lines.length >= 3);
        }

        function validateAndCleanTableContent(content) {
            if (!content || typeof content !== 'string') return null;
            try {
                let cleaned = content
                    .replace(/```[\s\S]*?```/g, '')
                    .replace(/`[^`]*`/g, '')
                    .replace(/\*\*([^*]*)\*\*/g, '$1')
                    .replace(/\*([^*]*)\*/g, '$1')
                    .trim();
                return cleaned.length === 0 ? null : cleaned;
            } catch (error) {
                console.error('Error validating table content:', error);
                return null;
            }
        }

        function parseConvertedContent(content) {
            console.log('Parsing converted content:', content);
            const convertedContentMatch = content.match(/<converted_content>([\s\S]*?)<\/converted_content>/);
            
            if (convertedContentMatch) {
                const rawTableContent = convertedContentMatch[1].trim();
                const cleanedTableContent = validateAndCleanTableContent(rawTableContent);
                const cleanContent = content.replace(/<converted_content>[\s\S]*?<\/converted_content>/, '').trim();
                
                console.log('Found converted content:', {
                    rawTableContent,
                    cleanedTableContent,
                    cleanContent,
                    hasTable: cleanedTableContent ? containsMarkdownTable(cleanedTableContent) : containsMarkdownTable(rawTableContent)
                });
                
                return {
                    hasTable: cleanedTableContent ? containsMarkdownTable(cleanedTableContent) : containsMarkdownTable(rawTableContent),
                    tableContent: cleanedTableContent || rawTableContent,
                    cleanContent
                };
            }
            
            return {
                hasTable: false,
                tableContent: '',
                cleanContent: content
            };
        }

        function parseMarkdownTable(markdownTable) {
            try {
                if (!markdownTable || typeof markdownTable !== 'string') {
                    return { headers: [], rows: [], isValid: false, error: 'Empty or invalid table content' };
                }

                const lines = markdownTable.trim().split('\n').filter(line => line.trim().length > 0);
                if (lines.length < 2) {
                    return { headers: [], rows: [], isValid: false, error: 'Insufficient table rows' };
                }

                // Parse headers
                const headerLine = lines[0].trim();
                let headers = [];
                if (headerLine.includes('|')) {
                    const cleanHeader = headerLine.replace(/^\||\|$/g, '');
                    headers = cleanHeader.split('|').map(h => h.trim()).filter(h => h.length > 0);
                } else {
                    headers = headerLine.split(/\s{2,}|\t/).map(h => h.trim()).filter(h => h.length > 0);
                }

                if (headers.length === 0) {
                    return { headers: [], rows: [], isValid: false, error: 'No valid headers found' };
                }

                // Find separator line
                let separatorIndex = -1;
                for (let i = 1; i < Math.min(3, lines.length); i++) {
                    const line = lines[i].trim();
                    if (line.match(/^[\|\s]*[-:]+[\|\s-:]*$/)) {
                        separatorIndex = i;
                        break;
                    }
                }

                let startRowIndex = separatorIndex !== -1 ? separatorIndex + 1 : 1;

                // Parse rows
                const rows = [];
                for (let i = startRowIndex; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line || line.startsWith('<!--') || line.match(/^[\|\s-:]*$/)) continue;

                    let cells = [];
                    if (line.includes('|')) {
                        const cleanLine = line.replace(/^\||\|$/g, '');
                        cells = cleanLine.split('|').map(cell => cell.trim());
                    } else {
                        cells = line.split(/\s{2,}|\t/).map(cell => cell.trim()).filter(cell => cell.length > 0);
                    }

                    if (cells.some(cell => cell.length > 0)) {
                        const row = {};
                        headers.forEach((header, index) => {
                            row[header] = cells[index] || '';
                        });
                        rows.push(row);
                    }
                }

                const isValid = headers.length > 0 && rows.length > 0;
                return { 
                    headers, 
                    rows, 
                    isValid,
                    error: isValid ? undefined : 'No valid table data found'
                };

            } catch (error) {
                console.error('Error parsing markdown table:', error);
                return { 
                    headers: [], 
                    rows: [], 
                    isValid: false, 
                    error: `Parsing error: ${error.message}` 
                };
            }
        }

        function renderTable(parsedTable) {
            if (!parsedTable.isValid || !parsedTable.headers || !parsedTable.rows) {
                return `<div class="error">Table format invalid: ${parsedTable.error || 'Unknown error'}</div>`;
            }

            let html = '<table>';
            
            // Headers
            html += '<thead><tr>';
            parsedTable.headers.forEach(header => {
                html += `<th>${escapeHtml(header)}</th>`;
            });
            html += '</tr></thead>';
            
            // Rows
            html += '<tbody>';
            parsedTable.rows.forEach(row => {
                html += '<tr>';
                parsedTable.headers.forEach(header => {
                    const cellContent = row[header] || '';
                    html += `<td>${escapeHtml(cellContent)}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody>';
            
            html += '</table>';
            return html;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function testStreamingWithTables() {
            const output = document.getElementById('output');
            
            // Simulate the problematic backend response that was causing issues
            const simulatedBackendResponse = `Based on your steel specifications, here are the metric conversions:

<converted_content>
| Product Type | Thickness (mm) | Width (mm) | Weight (kg) |
|--------------|----------------|------------|-------------|
| S/S 430 BA NO PI | 0.381 | 59.51 | 3260.18 |
| S/S 430 BA NO PI | 0.381 | 61.11 | 3656.68 |
| S/S 430 BA NO PI | 0.381 | 419.10 | 5692.77 |
</converted_content>

These conversions are based on standard steel density calculations.`;

            // Test the fixed parsing logic
            const { hasTable, tableContent, cleanContent } = parseConvertedContent(simulatedBackendResponse);
            
            output.innerHTML = `
                <div class="success">✅ Parsing Test Results:</div>
                <div><strong>Has Table:</strong> ${hasTable}</div>
                <div><strong>Clean Content:</strong></div>
                <pre>${cleanContent}</pre>
                <div><strong>Table Content:</strong></div>
                <pre>${tableContent}</pre>
                
                ${hasTable ? `
                    <div class="success">✅ Table detected! Rendering table:</div>
                    ${(() => {
                        const parsedTable = parseMarkdownTable(tableContent);
                        return parsedTable.isValid ? renderTable(parsedTable) : 
                               `<div class="error">Failed to parse table: ${parsedTable.error}</div>`;
                    })()}
                ` : '<div class="error">❌ No table detected</div>'}
            `;
        }
    </script>
</body>
</html> 