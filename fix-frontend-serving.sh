#!/bin/bash

# Script to fix the frontend serving issue
# This script updates the nginx configuration to properly serve the frontend application

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
DIST_DIR="$FRONTEND_DIR/dist"

# Public IP address
PUBLIC_IP="************"

# Function to print section header
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Function to print success message
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print warning message
print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Function to print error message
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to check if the frontend is built
check_frontend_built() {
    print_header "Checking if frontend is built"
    
    if [ -d "$DIST_DIR" ]; then
        if [ -f "$DIST_DIR/index.html" ]; then
            print_success "Frontend is built and index.html exists"
            return 0
        else
            print_warning "Frontend is built but index.html does not exist"
            
            # Check if there are any HTML files in the dist directory
            HTML_FILES=$(find "$DIST_DIR" -name "*.html" | wc -l)
            if [ "$HTML_FILES" -gt 0 ]; then
                print_warning "Found $HTML_FILES HTML files in the dist directory"
                
                # Find the main HTML file
                MAIN_HTML=$(find "$DIST_DIR" -name "*.html" | head -n 1)
                echo "Main HTML file: $MAIN_HTML"
                
                # Create a symlink to index.html
                echo "Creating a symlink to index.html..."
                ln -sf "$MAIN_HTML" "$DIST_DIR/index.html"
                
                if [ -f "$DIST_DIR/index.html" ]; then
                    print_success "Created symlink to index.html"
                    return 0
                else
                    print_error "Failed to create symlink to index.html"
                    return 1
                fi
            else
                print_error "No HTML files found in the dist directory"
                return 1
            fi
        fi
    else
        print_error "Frontend is not built (dist directory does not exist)"
        
        # Ask if user wants to build the frontend
        echo -e "\nWould you like to build the frontend? (y/n)"
        read -r BUILD_FRONTEND
        
        if [[ "$BUILD_FRONTEND" =~ ^[Yy]$ ]]; then
            echo "Building frontend..."
            cd "$FRONTEND_DIR" || exit
            npm run build
            
            if [ -d "$DIST_DIR" ] && [ -f "$DIST_DIR/index.html" ]; then
                print_success "Frontend built successfully"
                return 0
            else
                print_error "Failed to build frontend"
                return 1
            fi
        else
            return 1
        fi
    fi
}

# Function to create a favicon.ico file
create_favicon() {
    print_header "Creating favicon.ico file"
    
    if [ ! -f "$DIST_DIR/favicon.ico" ]; then
        echo "Creating favicon.ico file..."
        
        # Try to download a simple favicon.ico
        if command -v curl &> /dev/null; then
            curl -s -o "$DIST_DIR/favicon.ico" https://www.google.com/favicon.ico
        else
            # Create an empty favicon.ico file
            touch "$DIST_DIR/favicon.ico"
        fi
        
        if [ -f "$DIST_DIR/favicon.ico" ]; then
            print_success "favicon.ico file created successfully"
        else
            print_warning "Failed to create favicon.ico file"
        fi
    else
        print_success "favicon.ico file already exists"
    fi
}

# Function to update nginx configuration to serve static files
update_nginx_config() {
    print_header "Updating nginx configuration to serve static files"
    
    # Check if nginx is installed
    if ! command -v nginx &> /dev/null; then
        print_error "nginx is not installed"
        return 1
    fi
    
    # Check if steelnet.conf exists
    if [ ! -f "/etc/nginx/conf.d/steelnet.conf" ]; then
        print_error "steelnet.conf does not exist"
        
        # Ask if user wants to run fix-nginx-config.sh
        echo -e "\nWould you like to run fix-nginx-config.sh to create the nginx configuration? (y/n)"
        read -r RUN_FIX_NGINX
        
        if [[ "$RUN_FIX_NGINX" =~ ^[Yy]$ ]]; then
            echo "Running fix-nginx-config.sh..."
            "$SCRIPT_DIR/fix-nginx-config.sh"
        else
            return 1
        fi
    fi
    
    # Create a new nginx configuration
    echo "Creating new nginx configuration..."
    cat > /tmp/steelnet.conf << EOF
server {
    listen 80;
    listen [::]:80;
    server_name $PUBLIC_IP steelnet.ai;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://\$host\$request_uri;
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name $PUBLIC_IP steelnet.ai;
    
    # SSL configuration
    ssl_certificate $SCRIPT_DIR/ssl/cert.pem;
    ssl_certificate_key $SCRIPT_DIR/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    
    # SSL optimizations
    ssl_session_cache shared:SSL:2m;
    ssl_session_timeout 10m;
    
    # Root directory for static files
    root $DIST_DIR;
    index index.html;
    
    # Serve favicon.ico directly
    location = /favicon.ico {
        access_log off;
        log_not_found off;
        expires max;
    }
    
    # Serve static files directly
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        access_log off;
        expires max;
        add_header Cache-Control "public, no-transform";
    }
    
    # Frontend application
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
    
    # Copy the new configuration to nginx directory
    echo "Copying new configuration to /etc/nginx/conf.d/steelnet.conf..."
    sudo cp /tmp/steelnet.conf /etc/nginx/conf.d/steelnet.conf
    
    # Test nginx configuration
    echo "Testing nginx configuration..."
    if sudo nginx -t; then
        print_success "nginx configuration is valid"
        
        # Restart nginx
        echo "Restarting nginx..."
        sudo systemctl restart nginx || sudo service nginx restart
        
        # Check if nginx restarted successfully
        if systemctl is-active --quiet nginx || service nginx status > /dev/null 2>&1; then
            print_success "nginx restarted successfully"
            return 0
        else
            print_error "Failed to restart nginx"
            return 1
        fi
    else
        print_error "nginx configuration is invalid"
        return 1
    fi
}

# Function to check if the frontend server is running
check_frontend_server() {
    print_header "Checking if frontend server is running"
    
    # Check if the frontend server is running
    if [ -f "$SCRIPT_DIR/logs/frontend.pid" ]; then
        FRONTEND_PID=$(cat "$SCRIPT_DIR/logs/frontend.pid")
        if ps -p "$FRONTEND_PID" > /dev/null; then
            print_success "Frontend server is running with PID: $FRONTEND_PID"
            
            # Ask if user wants to stop the frontend server
            echo -e "\nWould you like to stop the frontend server? (y/n)"
            echo "Note: With the new nginx configuration, the frontend server is not needed"
            read -r STOP_FRONTEND
            
            if [[ "$STOP_FRONTEND" =~ ^[Yy]$ ]]; then
                echo "Stopping frontend server..."
                kill "$FRONTEND_PID"
                
                # Check if the frontend server was stopped
                if ! ps -p "$FRONTEND_PID" > /dev/null; then
                    print_success "Frontend server stopped successfully"
                    rm "$SCRIPT_DIR/logs/frontend.pid"
                else
                    print_error "Failed to stop frontend server"
                fi
            fi
        else
            print_warning "Frontend server is not running (PID file exists but process is dead)"
            rm "$SCRIPT_DIR/logs/frontend.pid"
        fi
    else
        print_warning "Frontend server is not running (PID file does not exist)"
    fi
}

# Main function
main() {
    print_header "Fixing Frontend Serving Issue"
    echo "This script will update the nginx configuration to properly serve the frontend application"
    echo "Script directory: $SCRIPT_DIR"
    echo "Frontend directory: $FRONTEND_DIR"
    echo "Dist directory: $DIST_DIR"
    
    # Check if the frontend is built
    check_frontend_built
    
    # Create a favicon.ico file
    create_favicon
    
    # Update nginx configuration
    update_nginx_config
    
    # Check if the frontend server is running
    check_frontend_server
    
    print_header "Summary"
    echo "The frontend serving issue should now be fixed."
    echo "You should now be able to access your application at:"
    echo "  HTTPS: https://$PUBLIC_IP"
    echo "  HTTP: http://$PUBLIC_IP (redirects to HTTPS)"
    
    echo -e "\nIf you're still having issues, please check the logs:"
    echo "  nginx error log: sudo tail -f /var/log/nginx/error.log"
    echo "  nginx access log: sudo tail -f /var/log/nginx/access.log"
}

# Run the main function
main
