#!/usr/bin/env python3
"""
Test the enhanced streaming table implementation with partial header simulation
"""
import requests
import json
import time

def test_streaming_with_simulated_chunks():
    """Simulate the streaming chunks to test partial header handling"""
    
    # Simulate the chunks as they come from the backend
    chunks = [
        "<table",
        "_stream",
        ">\\n",
        "Item",
        " Code",
        "|",
        "Description",
        "|",
        "Size",
        " (Original)",
        "|Customer",
        " QTY|",
        "Size (Converted)",
        "|Converted QTY",
        "|Remarks\\n",
        "001|S/S 430 BA",
        " NO PI|0.015(+/-0.0015)",
        " X 2.343\"(+/-0.005)",
        " X COIL|7190#|0.38(+/-0.04)",
        " X 59.52(+/-0.13)",
        " X COIL|3260.53 kg",
        "|Converted\\n",
        "</table_stream>"
    ]
    
    print("Testing streaming with simulated partial chunks...")
    accumulated = ""
    
    for i, chunk in enumerate(chunks):
        accumulated += chunk
        print(f"\\nChunk {i}: '{chunk}'")
        print(f"Accumulated ({len(accumulated)} chars): '{accumulated}'")
        
        # Check for table_stream pattern
        import re
        match = re.search(r'<table_stream>([\\s\\S]*?)(?:</table_stream>|$)', accumulated)
        if match:
            table_content = match.group(1)
            print(f"Found table content: '{table_content}'")
            
            # Parse lines
            lines = table_content.split('\\n')
            print(f"Lines: {lines}")
            
            if lines and lines[0].strip():
                header_line = lines[0].strip()
                if '|' in header_line:
                    headers = header_line.split('|')
                    headers = [h.strip() for h in headers]
                    print(f"Parsed headers ({len(headers)}): {headers}")
                    
                    if len(headers) >= 6:
                        print("✓ Headers appear complete!")
                    else:
                        print(f"⚠ Headers incomplete: {len(headers)}/7")
                        
                    # Check data rows
                    data_lines = [line for line in lines[1:] if line.strip()]
                    print(f"Data lines: {data_lines}")
                    
                    for j, line in enumerate(data_lines):
                        if '|' in line:
                            cells = line.split('|')
                            cells = [c.strip() for c in cells]
                            print(f"Row {j} cells ({len(cells)}): {cells}")
        
        print("-" * 50)

def test_real_api():
    """Test with real API call"""
    test_data = """S/S 430  BA  NO PI
 
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#"""

    url = "http://localhost:8000/api/llm/stream"
    payload = {
        "text": test_data,
        "function": "table",
        "unit_system": "metric"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    print("\\nTesting real API streaming...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True)
        
        if response.status_code == 200:
            print("\\nStreaming response received!")
            accumulated_data = ""
            chunk_count = 0
            
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    
                    if decoded_line.startswith('data: '):
                        try:
                            data_str = decoded_line[6:]
                            if data_str == '[DONE]':
                                print("\\n✓ Stream completed")
                                break
                            
                            data = json.loads(data_str)
                            if 'content' in data:
                                chunk_content = data['content']
                                accumulated_data += chunk_content
                                chunk_count += 1
                                
                                print(f"\\nChunk {chunk_count}: '{chunk_content}'")
                                
                                # Check if we have table content
                                if '<table_stream>' in accumulated_data:
                                    print("Found table_stream start!")
                                    
                                if 'Item Code' in accumulated_data and '|' in accumulated_data:
                                    # Try to parse current state
                                    import re
                                    match = re.search(r'<table_stream>([\\s\\S]*?)(?:</table_stream>|$)', accumulated_data)
                                    if match:
                                        current_table = match.group(1)
                                        lines = current_table.split('\\n')
                                        if lines and lines[0].strip():
                                            header_line = lines[0].strip()
                                            if '|' in header_line:
                                                headers = [h.strip() for h in header_line.split('|')]
                                                print(f"Current headers ({len(headers)}): {headers}")
                                
                        except json.JSONDecodeError:
                            pass
            
            print(f"\\nFinal accumulated data ({len(accumulated_data)} chars):")
            print(accumulated_data)
            
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"Request failed: {e}")

if __name__ == "__main__":
    # Test simulated chunks first
    test_streaming_with_simulated_chunks()
    
    # Then test real API
    test_real_api()