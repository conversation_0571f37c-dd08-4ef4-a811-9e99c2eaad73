#!/usr/bin/env bash

# Steel Unit Converter - Development Startup Script with URL Access for Linux
# This script starts the application in development mode with URL access

# Set script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
LOG_DIR="$SCRIPT_DIR/logs"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Set default values
ACTION="start"
COMPONENT="all"
SHOW_LOGS_AFTER=false
SETUP_URL_ACCESS=false
LOW_MEMORY_MODE=false

# Set sudo command if available
if command -v sudo >/dev/null 2>&1; then
    SUDO_CMD="sudo"
else
    SUDO_CMD=""
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        --restart)
            ACTION="restart"
            shift
            ;;
        --stop)
            ACTION="stop"
            shift
            ;;
        --status)
            ACTION="status"
            shift
            ;;
        --logs)
            ACTION="logs"
            if [[ "$2" == "backend" || "$2" == "frontend" ]]; then
                COMPONENT="$2"
                shift
            fi
            shift
            ;;
        --backend)
            COMPONENT="backend"
            shift
            ;;
        --frontend)
            COMPONENT="frontend"
            shift
            ;;
        --show-logs)
            SHOW_LOGS_AFTER=true
            shift
            ;;
        --url-access)
            # Keep this option for backward compatibility, but it doesn't do anything special now
            # since we're always setting up for URL access
            shift
            ;;
        --low-memory)
            LOW_MEMORY_MODE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --restart       Stop existing processes and start new ones"
            echo "  --stop          Stop all processes and exit"
            echo "  --status        Check if services are running"
            echo "  --logs [COMP]   Show logs (backend, frontend, or all)"
            echo "  --backend       Only operate on backend"
            echo "  --frontend      Only operate on frontend"
            echo "  --show-logs     Show logs after starting"
            echo "  --url-access    Legacy option (kept for compatibility)"
            echo "  --low-memory    Optimize for low memory environments (2GB RAM)"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information."
            exit 1
            ;;
    esac
done

# Function to check if a command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Function to check if Python is installed
check_python() {
    echo "Checking for Python..."
    if command_exists python3; then
        echo "Python 3 is installed."
        # Set global Python command
        PYTHON_CMD="python3"
        # Check Python version
        PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
        echo "Python version: $PYTHON_VERSION"

        # Check for required modules
        echo "Checking for required Python modules..."
        if ! python3 -c "import uvicorn" 2>/dev/null; then
            echo "Warning: uvicorn module not found. Will attempt to install it."
        fi

        if ! python3 -c "import fastapi" 2>/dev/null; then
            echo "Warning: fastapi module not found. Will attempt to install it."
        fi
    elif command_exists python; then
        # Check if python is actually python3
        PYTHON_VERSION=$(python --version 2>&1)
        if [[ $PYTHON_VERSION == *"Python 3"* ]]; then
            echo "Python 3 is installed as 'python'."
            PYTHON_CMD="python"
        else
            echo "Python 3 is not installed. Please install Python 3.8 or higher."
            exit 1
        fi
    else
        echo "Python 3 is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
}

# Function to check for HTTPS support
check_https_support() {
    echo "Checking for HTTPS support..."

    # Check if OpenSSL is installed
    if command_exists openssl; then
        echo "OpenSSL is installed."
        OPENSSL_VERSION=$(openssl version 2>&1)
        echo "OpenSSL version: $OPENSSL_VERSION"
    else
        echo "Warning: OpenSSL is not installed. HTTPS support may be limited."
    fi

    # Check if we're on the production server
    if [[ "$SERVER_IP" == "************" || "$SERVER_IP" == "steelnet.ai" ]]; then
        echo "Production server detected. HTTPS support is limited."
        echo "The backend does not currently support HTTPS connections."
        echo "Frontend HTTPS connections will fall back to HTTP for API calls."
        echo "This may cause mixed content warnings in some browsers."
        HTTPS_SUPPORTED=false
    else
        HTTPS_SUPPORTED=true
    fi
}

# Function to check if Node.js is installed
check_node() {
    echo "Checking for Node.js..."
    if command_exists node; then
        echo "Node.js is installed."
    else
        echo "Node.js is not installed. Please install Node.js 14 or higher."
        exit 1
    fi
}

# Function to kill existing processes
kill_existing() {
    echo "Checking for existing processes..."

    # Kill processes on ports 8000 (backend) and 5173 (frontend dev server)
    if command_exists lsof; then
        echo "Killing process on port 8000..."
        lsof -ti:8000 | xargs kill -9 2>/dev/null || true

        echo "Killing process on port 5173..."
        lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    else
        # Alternative method if lsof is not available
        echo "lsof not found, using alternative method to kill processes..."

        # Find and kill Python/Uvicorn processes
        pkill -f "uvicorn main:app" 2>/dev/null || true

        # Find and kill Node.js processes for frontend
        pkill -f "node.*vite" 2>/dev/null || true
    fi

    echo "All existing processes killed."
}

# Function to start the backend in development mode
start_backend() {
    echo "Starting backend in development mode..."

    # Change directory to backend
    cd "$BACKEND_DIR" || exit

    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo "Creating virtual environment..."
        python3 -m venv venv
    fi

    # Activate virtual environment
    source venv/bin/activate

    # Install dependencies if requirements.txt exists
    if [ -f "requirements.txt" ]; then
        echo "Installing backend dependencies..."
        pip install -r requirements.txt
    fi

    # Set environment variables for development
    export ENV="development"
    export DEBUG="True"

    # Get server IP address for CORS configuration (force IPv4)
    SERVER_IP=$(curl -s -4 ifconfig.me || curl -s -4 icanhazip.com || curl -s -4 ipecho.net/plain || hostname -I | grep -oE '([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1)
    if [ -z "$SERVER_IP" ]; then
        # Fallback to local network IPv4 if external IP detection fails
        SERVER_IP=$(ip -4 addr show 2>/dev/null | grep -oP '(?<=inet\s)\d+(\.\d+){3}' | grep -v '127.0.0.1' | head -1)
        if [ -z "$SERVER_IP" ]; then
            # Last resort fallback
            SERVER_IP="127.0.0.1"
        fi
    fi
    echo "Detected server IP (IPv4): $SERVER_IP"

    # Check if this is a production server
    IS_PRODUCTION=false
    if [[ "$SERVER_IP" == "************" ]]; then
        IS_PRODUCTION=true
        echo "Detected production server (************)"
        # Set low memory mode automatically for production
        LOW_MEMORY_MODE=true
        echo "Setting LOW_MEMORY_MODE=true for production server"
    fi

    # Configure CORS to allow access from the server IP
    export CORS_ORIGINS="*"

    # Determine Python command (use python3 explicitly on production servers)
    PYTHON_CMD="python"
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        echo "Using python3 command"
    fi

    # Fix import issues
    echo "Fixing import issues..."
    $PYTHON_CMD fix_imports.py

    # Run dependency checker to install missing packages
    echo "Checking for missing dependencies..."
    $PYTHON_CMD install_missing_deps.py

    # Start backend server with uvicorn in development mode
    echo "Starting backend with uvicorn (development mode)..."
    echo "Binding backend to IP: $SERVER_IP (and 0.0.0.0 for local access)"

    # Create a custom CORS configuration file for the backend
    echo "Creating CORS configuration for backend..."
    cat > "$BACKEND_DIR/.env.development" << EOF
# Development environment CORS configuration
CORS_ORIGINS=*
EOF

    # Create a custom CORS middleware file to ensure all origins are allowed
    echo "Creating custom CORS middleware for backend..."
    mkdir -p "$BACKEND_DIR/app/middleware"
    cat > "$BACKEND_DIR/app/middleware/cors.py" << EOF
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

def setup_cors(app: FastAPI):
    """Setup CORS middleware for the application."""
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Allow all origins in development
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["*"],
        max_age=86400,  # 24 hours cache for preflight requests
    )
EOF

    # Create a custom CORS middleware initialization file
    echo "Creating CORS middleware initialization..."
    mkdir -p "$BACKEND_DIR/app/middleware"
    cat > "$BACKEND_DIR/app/middleware/__init__.py" << EOF
# Import middleware modules
from .cors import setup_cors

__all__ = ["setup_cors"]
EOF

    # Determine uvicorn command (use python3 -m uvicorn on production servers)
    UVICORN_CMD="uvicorn"
    if command -v python3 &> /dev/null; then
        UVICORN_CMD="python3 -m uvicorn"
        echo "Using python3 -m uvicorn command"
    fi

    if [ "$LOW_MEMORY_MODE" = true ]; then
        # Use lower memory settings for development
        nohup $UVICORN_CMD main:app --reload --host 0.0.0.0 --port 8000 --workers 1 > "$LOG_DIR/backend.log" 2>&1 &
    else
        nohup $UVICORN_CMD main:app --reload --host 0.0.0.0 --port 8000 > "$LOG_DIR/backend.log" 2>&1 &
    fi

    BACKEND_PID=$!
    echo "Backend started with PID: $BACKEND_PID"
    echo "Backend logs available at: $LOG_DIR/backend.log"

    # Save PID to file for later reference
    echo $BACKEND_PID > "$LOG_DIR/backend.pid"

    # Return to script directory
    cd "$SCRIPT_DIR" || exit
}

# Function to start the frontend in development mode
start_frontend() {
    echo "Starting frontend in development mode..."

    # Change directory to frontend
    cd "$FRONTEND_DIR" || exit

    # Install dependencies if needed
    if [ -f "package.json" ]; then
        echo "Installing frontend dependencies..."
        npm install
    fi

    # Set NODE_OPTIONS for memory optimization if needed
    if [ "$LOW_MEMORY_MODE" = true ]; then
        export NODE_OPTIONS="--max-old-space-size=256"
    else
        export NODE_OPTIONS="--max-old-space-size=512"
    fi

    # Get server IP address if not already set (force IPv4)
    if [ -z "$SERVER_IP" ]; then
        SERVER_IP=$(curl -s -4 ifconfig.me || curl -s -4 icanhazip.com || curl -s -4 ipecho.net/plain || hostname -I | grep -oE '([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1)
        if [ -z "$SERVER_IP" ]; then
            # Fallback to local network IPv4 if external IP detection fails
            SERVER_IP=$(ip -4 addr show 2>/dev/null | grep -oP '(?<=inet\s)\d+(\.\d+){3}' | grep -v '127.0.0.1' | head -1)
            if [ -z "$SERVER_IP" ]; then
                # Last resort fallback
                SERVER_IP="127.0.0.1"
            fi
        fi
        echo "Detected server IP (IPv4): $SERVER_IP"
    fi

    # Create a temporary Vite config file with proper proxy settings
    echo "Creating temporary Vite config with proper proxy settings..."
    cat > "$FRONTEND_DIR/vite.config.proxy.js" << EOF
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import fs from 'fs';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    host: '0.0.0.0',
    strictPort: true,
    cors: true,
    // Disable HMR WebSocket to avoid connection issues
    hmr: false,
    // Allow specific hosts
    allowedHosts: ['localhost', '127.0.0.1', 'steelnet.ai', '************'],
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: true,  // Allow secure connections
        ws: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
      '/conversion': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: true,  // Allow secure connections
        ws: true,
      },
      '/auth': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: true,  // Allow secure connections
        ws: true,
      },
      '/health': {
        target: 'http://127.0.0.1:8000',
        changeOrigin: true,
        secure: true,  // Allow secure connections
        ws: true,
      }
    },
  },
});
EOF

    # Create a .env.local file for frontend with correct API URL
    echo "Creating frontend environment configuration..."
    cat > "$FRONTEND_DIR/.env.local" << EOF
# Development environment configuration
VITE_DEV_SERVER_HOST=0.0.0.0
VITE_DEV_SERVER_PORT=5173
VITE_API_BASE_URL=http://localhost:5173
VITE_BACKEND_URL=http://localhost:8000
# Disable HMR to avoid WebSocket issues
VITE_DISABLE_HMR=true
EOF

    # Create a custom client file to override WebSocket connection
    echo "Creating custom Vite client configuration..."
    mkdir -p "$FRONTEND_DIR/node_modules/vite/dist/client"
    cat > "$FRONTEND_DIR/node_modules/vite/dist/client/env.js" << EOF
// Custom Vite client environment override
const env = {
  BASE_URL: "/",
  MODE: "development",
  DEV: true,
  PROD: false,
  SSR: false,
  VITE_HMR_HOST: "localhost",
  VITE_HMR_PORT: 5173,
  VITE_HMR_PROTOCOL: "ws"
};

export { env };
EOF

    # Create a custom index.html with HMR disabled
    if [ -f "$FRONTEND_DIR/index.html" ]; then
        echo "Creating backup of original index.html..."
        cp "$FRONTEND_DIR/index.html" "$FRONTEND_DIR/index.html.bak"

        echo "Adding API configuration to index.html..."
        cat > "$FRONTEND_DIR/index.html" << EOF
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Steel Unit Converter</title>
    <script>
      // Disable HMR to avoid WebSocket issues
      window.__VITE_DISABLE_HMR = true;

      // Set API URLs - use dynamic hostname and protocol detection
      const currentHost = window.location.hostname;
      const currentProtocol = window.location.protocol;

      // For production servers, always use HTTP for backend API calls
      // since the backend doesn't support HTTPS yet
      const apiProtocol = (currentHost === '************' || currentHost === 'steelnet.ai')
        ? 'http:'
        : currentProtocol;

      window.API_BASE_URL = currentProtocol + "//" + currentHost + ":5173";
      window.BACKEND_URL = apiProtocol + "//" + currentHost + ":8000";

      console.log("Using dynamic API configuration:");
      console.log("- Frontend protocol:", currentProtocol);
      console.log("- API protocol:", apiProtocol);
      console.log("- Host:", currentHost);
      console.log("- Frontend URL:", window.API_BASE_URL);
      console.log("- Backend URL:", window.BACKEND_URL);

      // Fix React issues
      window.__vite_plugin_react_preamble_installed__ = true;
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
EOF
    fi

    # Start frontend server in development mode with custom config
    echo "Starting frontend with vite (development mode with external access)..."
    # Add environment variables - use 0.0.0.0 for binding but dynamic hostname for access
    export VITE_DEV_SERVER_HOST="0.0.0.0"
    export VITE_DEV_SERVER_PORT=5173
    # These will be overridden by the dynamic detection in the frontend code
    # Support both HTTP and HTTPS
    export VITE_API_BASE_URL="//localhost:5173"
    export VITE_BACKEND_URL="//localhost:8000"
    # Disable HMR to avoid WebSocket issues
    export VITE_DISABLE_HMR=true

    # Create a custom API configuration file to ensure correct API URLs
    echo "Creating custom API configuration file..."
    mkdir -p "$FRONTEND_DIR/src/config"
    cat > "$FRONTEND_DIR/src/config/api.ts" << EOF
// API configuration for development
// Use dynamic hostname and protocol detection to support both HTTP and HTTPS
const currentHost = window.location.hostname;
const currentProtocol = window.location.protocol;

// For production servers, always use HTTP for backend API calls
// since the backend doesn't support HTTPS yet
const apiProtocol = (currentHost === '************' || currentHost === 'steelnet.ai')
  ? 'http:'
  : currentProtocol;

const API_BASE_URL = \`\${currentProtocol}//\${currentHost}:5173\`;
const BACKEND_URL = \`\${apiProtocol}//\${currentHost}:8000\`;

console.log('Using dynamic API configuration:');
console.log('- Frontend protocol:', currentProtocol);
console.log('- API protocol:', apiProtocol);
console.log('- Host:', currentHost);
console.log('- Frontend URL:', API_BASE_URL);
console.log('- Backend URL:', BACKEND_URL);

export const API_ENDPOINTS = {
  // Use the proxy for all API requests
  LLM: \`\${API_BASE_URL}/api/llm\`,
  LLM_FUNCTIONS: \`\${API_BASE_URL}/api/llm/functions\`,
  CONVERSION: \`\${API_BASE_URL}/conversion\`,
  AUTH: \`\${API_BASE_URL}/auth\`,
  HEALTH: \`\${API_BASE_URL}/health\`,
};

export default API_ENDPOINTS;
EOF

    # Start with custom config to ensure proper proxy
    echo "Starting frontend with disabled HMR for stability..."
    nohup npm run dev -- --config vite.config.proxy.js --host 0.0.0.0 --strictPort --force > "$LOG_DIR/frontend.log" 2>&1 &

    FRONTEND_PID=$!
    echo "Frontend started with PID: $FRONTEND_PID"
    echo "Frontend logs available at: $LOG_DIR/frontend.log"

    # Save PID to file for later reference
    echo $FRONTEND_PID > "$LOG_DIR/frontend.pid"

    # Return to script directory
    cd "$SCRIPT_DIR" || exit
}

# Function to set up nginx for URL access
setup_nginx() {
    echo "Setting up URL access via nginx..."

    # Check if setup_nginx_dev.sh exists
    if [ -f "$SCRIPT_DIR/setup_nginx_dev.sh" ]; then
        echo "Using setup_nginx_dev.sh script for nginx configuration..."

        # Run the setup script with error handling
        bash "$SCRIPT_DIR/setup_nginx_dev.sh"
        SETUP_RESULT=$?

        # Check if nginx is actually running, regardless of script exit code
        if pgrep -x "nginx" > /dev/null; then
            echo "Nginx is running. Configuration successful."
            return 0
        elif [ $SETUP_RESULT -eq 0 ]; then
            echo "Setup script completed successfully, but nginx may not be running."
            echo "You can still access the application directly via:"
            echo "  Backend: http://localhost:8000"
            echo "  Frontend: http://localhost:5173"
            return 0
        else
            echo "Failed to configure nginx. Check the output above for errors."

            # Provide troubleshooting information
            echo ""
            echo "Troubleshooting tips:"
            echo "1. Check if nginx is already running with another configuration"
            echo "2. Check if ports 80 and 443 are already in use"
            echo "3. Check if you have permission to start nginx"
            echo "4. Try running 'sudo systemctl status nginx' for more information"
            echo ""

            # Ask if user wants to continue without nginx
            echo "Do you want to continue without nginx configuration? (y/n)"
            read -r CONTINUE_WITHOUT_NGINX
            if [[ "$CONTINUE_WITHOUT_NGINX" =~ ^[Yy]$ ]]; then
                echo "Continuing without nginx configuration."
                return 0
            else
                echo "Aborting."
                return 1
            fi
        fi
    else
        echo "setup_nginx_dev.sh not found. Please run the following command first:"
        echo "  chmod +x setup_nginx_dev.sh"
        echo "Then run this script again with the --url-access option."
        return 1
    fi
}

# Function to check if services are running
check_services() {
    echo "Checking if services are running..."

    # Check if backend is running
    if [ -f "$LOG_DIR/backend.pid" ]; then
        BACKEND_PID=$(cat "$LOG_DIR/backend.pid")
        if ps -p "$BACKEND_PID" > /dev/null; then
            echo "Backend is running with PID: $BACKEND_PID"
        else
            echo "Backend is not running."
        fi
    else
        echo "Backend is not running."
    fi

    # Check if frontend is running
    if [ -f "$LOG_DIR/frontend.pid" ]; then
        FRONTEND_PID=$(cat "$LOG_DIR/frontend.pid")
        if ps -p "$FRONTEND_PID" > /dev/null; then
            echo "Frontend is running with PID: $FRONTEND_PID"
        else
            echo "Frontend is not running."
        fi
    else
        echo "Frontend is not running."
    fi

    # Check if ports are open
    echo "Checking ports..."

    echo "Port 8000 (Backend):"
    if command_exists lsof; then
        lsof -i:8000 | grep LISTEN || echo "Port 8000 is not open"
    else
        netstat -tuln | grep ":8000" || echo "Port 8000 is not open"
    fi

    echo "Port 5173 (Frontend):"
    if command_exists lsof; then
        lsof -i:5173 | grep LISTEN || echo "Port 5173 is not open"
    else
        netstat -tuln | grep ":5173" || echo "Port 5173 is not open"
    fi
}

# Function to show logs
show_logs() {
    local component=$1

    if [ "$component" = "backend" ] || [ "$component" = "all" ]; then
        echo "Backend logs:"
        tail -f "$LOG_DIR/backend.log" &
        BACKEND_LOG_PID=$!
    fi

    if [ "$component" = "frontend" ] || [ "$component" = "all" ]; then
        echo "Frontend logs:"
        tail -f "$LOG_DIR/frontend.log" &
        FRONTEND_LOG_PID=$!
    fi

    # Wait for user to press Ctrl+C
    echo "Press Ctrl+C to stop viewing logs."
    wait

    # Kill log processes
    if [ -n "$BACKEND_LOG_PID" ]; then
        kill $BACKEND_LOG_PID 2>/dev/null || true
    fi
    if [ -n "$FRONTEND_LOG_PID" ]; then
        kill $FRONTEND_LOG_PID 2>/dev/null || true
    fi
}

# Function to restore original files
restore_original_files() {
    echo "Restoring original files..."

    # Restore original index.html if backup exists
    if [ -f "$FRONTEND_DIR/index.html.bak" ]; then
        echo "Restoring original index.html..."
        mv "$FRONTEND_DIR/index.html.bak" "$FRONTEND_DIR/index.html"
    fi

    # Remove temporary Vite config files
    if [ -f "$FRONTEND_DIR/vite.config.proxy.js" ]; then
        echo "Removing temporary Vite config..."
        rm "$FRONTEND_DIR/vite.config.proxy.js"
    fi

    if [ -f "$FRONTEND_DIR/vite.client.js" ]; then
        echo "Removing temporary client config..."
        rm "$FRONTEND_DIR/vite.client.js"
    fi

    echo "Original files restored."
}

# Function to stop services
stop_services() {
    echo "Stopping services..."

    # Kill backend process
    if [ -f "$LOG_DIR/backend.pid" ]; then
        BACKEND_PID=$(cat "$LOG_DIR/backend.pid")
        if ps -p "$BACKEND_PID" > /dev/null; then
            echo "Stopping backend with PID: $BACKEND_PID"
            kill -9 "$BACKEND_PID" 2>/dev/null || true
        fi
        rm -f "$LOG_DIR/backend.pid"
    fi

    # Kill frontend process
    if [ -f "$LOG_DIR/frontend.pid" ]; then
        FRONTEND_PID=$(cat "$LOG_DIR/frontend.pid")
        if ps -p "$FRONTEND_PID" > /dev/null; then
            echo "Stopping frontend with PID: $FRONTEND_PID"
            kill -9 "$FRONTEND_PID" 2>/dev/null || true
        fi
        rm -f "$LOG_DIR/frontend.pid"
    fi

    # Kill any remaining processes on ports 8000 and 5173
    kill_existing

    # Restore original files
    restore_original_files

    echo "All services stopped."
}

# Main execution
case "$ACTION" in
    start)
        # Check requirements
        check_python
        check_node

        # Kill existing processes
        kill_existing

        # Start services based on component
        if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "backend" ]; then
            start_backend
        fi

        if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "frontend" ]; then
            start_frontend
        fi

        # We no longer need nginx setup as the frontend handles proxying
        # This comment is kept for clarity on what changed

        echo "Services started successfully."

        # Show logs if requested
        if [ "$SHOW_LOGS_AFTER" = true ]; then
            show_logs "$COMPONENT"
        fi
        ;;
    restart)
        # Kill existing processes
        kill_existing

        # Check requirements
        check_python
        check_node

        # Start services based on component
        if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "backend" ]; then
            start_backend
        fi

        if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "frontend" ]; then
            start_frontend
        fi

        # We no longer need nginx setup as the frontend handles proxying
        # This comment is kept for clarity on what changed

        echo "Services restarted successfully."

        # Show logs if requested
        if [ "$SHOW_LOGS_AFTER" = true ]; then
            show_logs "$COMPONENT"
        fi
        ;;
    stop)
        stop_services
        ;;
    status)
        check_services
        ;;
    logs)
        show_logs "$COMPONENT"
        ;;
    *)
        echo "No action specified. Use --help for usage information."
        exit 1
        ;;
esac

# Display summary
echo ""
echo "=================================================="
echo "  Steel Unit Converter - Development Mode (Linux) "
echo "=================================================="
echo ""

# Get server IP address if not already set (force IPv4)
if [ -z "$SERVER_IP" ]; then
    SERVER_IP=$(curl -s -4 ifconfig.me || curl -s -4 icanhazip.com || curl -s -4 ipecho.net/plain || hostname -I | grep -oE '([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1)
    if [ -z "$SERVER_IP" ]; then
        # Fallback to local network IPv4 if external IP detection fails
        SERVER_IP=$(ip -4 addr show 2>/dev/null | grep -oP '(?<=inet\s)\d+(\.\d+){3}' | grep -v '127.0.0.1' | head -1)
        if [ -z "$SERVER_IP" ]; then
            # Last resort fallback
            SERVER_IP="127.0.0.1"
        fi
    fi
fi

# Check if steelnet.ai is in hosts file
echo "Checking if steelnet.ai is in hosts file..."
if grep -q "steelnet.ai" /etc/hosts; then
    echo "steelnet.ai already in hosts file."
else
    echo "steelnet.ai not found in hosts file."
    echo "To add steelnet.ai to your hosts file, run this command manually:"
    echo "  echo \"$SERVER_IP steelnet.ai\" | sudo tee -a /etc/hosts"
    echo ""
    echo "This will allow you to access the application via http://steelnet.ai:5173"
fi

echo "Application URLs:"
echo "  Frontend: http://$SERVER_IP:5173"
echo "  Backend API: http://$SERVER_IP:8000/api"
echo "  Domain access: http://steelnet.ai:5173 (if hosts file is configured)"
echo ""
echo "Local access URLs (RECOMMENDED):"
echo "  Frontend: http://localhost:5173"
echo "  Backend API: http://localhost:8000/api"
echo ""
echo "Note: The frontend server is configured to proxy API requests to the backend."
echo "      This means you can access the API through the frontend URL as well:"
echo "      http://localhost:5173/api"
echo ""
echo "IMPORTANT: To avoid CORS issues, please use the following URLs consistently:"
echo "  - For local development: http://localhost:5173"
echo "  - For remote access: http://$SERVER_IP:5173"
echo "  - For domain access: http://steelnet.ai:5173"
echo ""
echo "HTTPS NOTICE:"
echo "  - The backend server does NOT currently support HTTPS directly"
echo "  - If you access the frontend via HTTPS (https://steelnet.ai), the application will"
echo "    automatically use a proxy to avoid mixed content issues"
echo "  - The proxy will route requests through the frontend server to the backend"
echo "  - This solution allows the application to work in modern browsers that block mixed content"
echo ""
echo "The application now uses dynamic protocol detection to automatically adapt to how you access it."
echo "For production servers (************ and steelnet.ai), backend API calls will always use HTTP"
echo "regardless of how you access the frontend."
echo ""
echo "If you're still experiencing CORS issues:"
echo "  1. Make sure you're accessing the application through the frontend URL"
echo "  2. Don't mix localhost and IP address access in the same session"
echo "  3. Clear your browser cache and cookies"
echo "  4. Try using a private/incognito browser window"
echo "  5. If using steelnet.ai, ensure it's in your hosts file pointing to $SERVER_IP"

echo ""
echo "Management commands:"
echo "  Start/Restart:    $0 --restart"
echo "  Stop:             $0 --stop"
echo "  Status:           $0 --status"
echo "  View Logs:        $0 --logs [backend|frontend|all]"
echo "  Low Memory Mode:  $0 --low-memory"
echo ""
if [ "$LOW_MEMORY_MODE" = true ]; then
    echo "Low memory mode is currently ENABLED"
else
    echo "Low memory mode is currently disabled"
fi
echo ""
