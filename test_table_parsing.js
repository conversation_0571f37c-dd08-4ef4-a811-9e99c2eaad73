// Test the table parsing logic directly
const testData = `<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015(+/-0.0015) X 2.343"(+/-0.005) X COIL|7190#|0.38(+/-0.04) X 59.52(+/-0.13) X COIL|3260.53 kg|Converted
002|S/S 430 BA NO PI|0.015(+/-0.0015) X 2.406"(+/-0.005) X COIL|8061#|0.38(+/-0.04) X 61.11(+/-0.13) X COIL|3656.73 kg|Converted
003|S/S 430 BA NO PI|0.015(+/-0.0015) X 16.50"(+/-0.005) X COIL|12550#|0.38(+/-0.04) X 419.10(+/-0.13) X COIL|5694.83 kg|Converted
</table_stream>`;

console.log('Testing table parsing...');
console.log('Input:', testData);

// Extract table content
const tableStreamMatch = testData.match(/<table_stream>([\s\S]*?)<\/table_stream>/);
if (tableStreamMatch) {
  const tableContent = tableStreamMatch[1].trim();
  console.log('Extracted table content:', tableContent);
  
  // Parse the compact table format
  const lines = tableContent.split('\n').filter(line => line.trim());
  console.log('Lines:', lines);
  
  if (lines.length >= 2) {
    const headers = lines[0].split('|').map(h => h.trim());
    console.log('Headers:', headers);
    console.log('Header count:', headers.length);
    
    const rows = lines.slice(1).map((line, lineIndex) => {
      const cells = line.split('|').map(c => c.trim());
      console.log(`Row ${lineIndex} cells:`, cells);
      console.log(`Row ${lineIndex} cell count:`, cells.length);
      
      const row = {};
      headers.forEach((header, index) => {
        row[header] = cells[index] || '';
      });
      return row;
    });
    
    console.log('Final parsed table:');
    console.log('Headers:', headers);
    console.log('Rows:', rows);
    console.log('First row:', rows[0]);
  }
}