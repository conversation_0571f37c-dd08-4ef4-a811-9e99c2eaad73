<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .warning {
            color: orange;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Frontend Fix Test</h1>
        <p>Testing the fixed frontend table parsing with accumulated content</p>

        <div class="test-section">
            <h3>📊 Test 1: Simulated Streaming with Accumulated Content</h3>
            <div id="test1-log" class="log"></div>
            <div id="test1-result"></div>
        </div>

        <div class="test-section">
            <h3>🔄 Test 2: Progressive Streaming Simulation</h3>
            <div id="test2-log" class="log"></div>
            <div id="test2-result"></div>
        </div>
    </div>

    <script>
        function log(testId, message, type = 'info') {
            const logElement = document.getElementById(`${testId}-log`);
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Simplified StreamingTableParser based on the fixed version
        class StreamingTableParser {
            constructor() {
                this.headers = [];
                this.rows = [];
                this.isHeaderParsed = false;
                this.buffer = '';
                this.expectedHeaders = ['Item Code', 'Description', 'Size (Original)', 'Customer QTY', 'Size (Converted)', 'Converted QTY', 'Remarks'];
            }

            addChunk(accumulatedContent) {
                console.log('🔄 StreamingTableParser.addChunk ENTRY:', {
                    accumulatedLength: accumulatedContent.length,
                    hasTableStreamStart: accumulatedContent.includes('<table_stream>'),
                    hasTableStreamEnd: accumulatedContent.includes('</table_stream>')
                });

                // Use accumulated content directly
                this.buffer = accumulatedContent;

                // Look for table_stream tags
                const streamMatch = this.buffer.match(/<table_stream>([\s\S]*?)(?:<\/table_stream>|$)/);
                if (streamMatch) {
                    const tableContent = streamMatch[1];
                    console.log('Found table content in buffer:', {
                        tableContentLength: tableContent.length,
                        tableContent: tableContent,
                        hasNewlines: tableContent.includes('\n'),
                        lineCount: tableContent.split('\n').length
                    });

                    // Only parse if we have the complete table
                    if (this.buffer.includes('</table_stream>')) {
                        console.log('✅ Complete table found, parsing...');
                        return this.parseStreamingContent(tableContent);
                    } else {
                        console.log('⏳ Incomplete table, waiting for closing tag...');
                        return { headers: this.headers, rows: this.rows, isValid: false, error: 'Waiting for complete table_stream' };
                    }
                }

                console.log('No table_stream match yet, returning invalid table');
                return { headers: this.headers, rows: this.rows, isValid: false, error: 'Waiting for table_stream content' };
            }

            parseStreamingContent(content) {
                console.log('🔄 parseStreamingContent called:', {
                    contentLength: content.length,
                    content: content,
                    hasNewlines: content.includes('\n')
                });

                const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0);
                console.log('📋 Split into lines:', {
                    totalLines: lines.length,
                    lines: lines
                });

                // Parse headers
                if (!this.isHeaderParsed && lines.length > 0) {
                    const headerLine = lines[0];
                    const headers = headerLine.split('|').map(h => h.trim()).filter(h => h.length > 0);
                    
                    if (headers.length === 7) {
                        console.log('✅ Header line is complete with 7 columns, setting headers');
                        this.headers = headers;
                        this.isHeaderParsed = true;
                    } else {
                        console.log(`⏳ Header line incomplete: ${headers.length}/7 columns`);
                        return {
                            headers: this.expectedHeaders,
                            rows: [],
                            isValid: false,
                            error: `Waiting for complete header line (got ${headers.length}/7 columns)`
                        };
                    }
                }

                // Parse data rows
                if (this.isHeaderParsed) {
                    this.rows = [];
                    const dataLines = lines.slice(1); // Skip header line

                    for (let i = 0; i < dataLines.length; i++) {
                        const line = dataLines[i];
                        const cells = line.split('|').map(c => c.trim());

                        if (cells.length === this.headers.length) {
                            const row = {};
                            this.headers.forEach((header, index) => {
                                row[header] = cells[index] || '';
                            });
                            this.rows.push(row);
                            console.log(`✅ Row ${i + 1} parsed successfully: ${cells.length} cells`);
                        } else {
                            console.warn(`⚠️ Row ${i + 1} column mismatch: expected ${this.headers.length}, got ${cells.length}`);
                        }
                    }

                    return {
                        headers: this.headers,
                        rows: this.rows,
                        isValid: true
                    };
                }

                return { headers: this.headers, rows: this.rows, isValid: false, error: 'Headers not parsed yet' };
            }

            reset() {
                this.headers = [];
                this.rows = [];
                this.isHeaderParsed = false;
                this.buffer = '';
            }
        }

        // Test 1: Simulated streaming with accumulated content
        function test1_simulatedStreaming() {
            log('test1', '🚀 Starting Test 1: Simulated Streaming with Accumulated Content');
            
            const parser = new StreamingTableParser();
            
            // Simulate streaming chunks with accumulated content (like the backend sends)
            const streamingChunks = [
                {
                    content: '<table',
                    accumulated_content: '<table'
                },
                {
                    content: '_stream>\\n',
                    accumulated_content: '<table_stream>\\n'
                },
                {
                    content: 'Item Code|Description',
                    accumulated_content: '<table_stream>\\nItem Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks'
                },
                {
                    content: '\\n001|S/S 430 BA NO PI',
                    accumulated_content: '<table_stream>\\nItem Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks\\n001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish'
                },
                {
                    content: '\\n002|S/S 430 BA NO PI',
                    accumulated_content: '<table_stream>\\nItem Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks\\n001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish\\n002|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.406(+/-0.005)in x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish'
                },
                {
                    content: '</table_stream>',
                    accumulated_content: '<table_stream>\\nItem Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks\\n001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish\\n002|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.406(+/-0.005)in x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish\\n</table_stream>'
                }
            ];

            let chunkIndex = 0;

            function processNextChunk() {
                if (chunkIndex >= streamingChunks.length) {
                    log('test1', '✅ All chunks processed', 'success');
                    return;
                }

                const chunk = streamingChunks[chunkIndex];
                const accumulatedContent = chunk.accumulated_content.replace(/\\n/g, '\n');
                
                log('test1', `📦 Processing chunk ${chunkIndex + 1}: "${chunk.content}"`);
                log('test1', `📋 Accumulated content length: ${accumulatedContent.length}`);

                // Use accumulated content (the fix)
                const result = parser.addChunk(accumulatedContent);
                
                log('test1', `📊 Parse result: isValid=${result.isValid}, headers=${result.headers.length}, rows=${result.rows.length}`);
                
                if (result.isValid && result.headers.length > 0 && result.rows.length > 0) {
                    log('test1', '✅ Table successfully parsed!', 'success');
                    
                    // Generate HTML table
                    let html = '<table>';
                    html += '<thead><tr>';
                    result.headers.forEach(header => {
                        html += `<th>${escapeHtml(header)}</th>`;
                    });
                    html += '</tr></thead>';
                    
                    html += '<tbody>';
                    result.rows.forEach(row => {
                        html += '<tr>';
                        result.headers.forEach(header => {
                            const cellContent = row[header] || '';
                            html += `<td>${escapeHtml(cellContent)}</td>`;
                        });
                        html += '</tr>';
                    });
                    html += '</tbody></table>';

                    document.getElementById('test1-result').innerHTML = html;
                    
                    log('test1', `✅ Table rendered with ${result.headers.length} headers and ${result.rows.length} rows`, 'success');
                    return;
                }

                chunkIndex++;
                setTimeout(processNextChunk, 500);
            }

            processNextChunk();
        }

        // Test 2: Progressive streaming simulation
        function test2_progressiveStreaming() {
            log('test2', '🚀 Starting Test 2: Progressive Streaming Simulation');
            
            // This simulates the exact data from our backend streaming test
            const completeAccumulatedContent = `<table_stream>
Item Code|Description|Size (Original)|Customer QTY|Size (Converted)|Converted QTY|Remarks
001|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.343(+/-0.005)in x COIL|7190#|0.38(+/-0.04)mm x 59.51(+/-0.13)mm x COIL|3261.2 kg|BA Finish
002|S/S 430 BA NO PI|0.015(+/-0.0015)in x 2.406(+/-0.005)in x COIL|8061#|0.38(+/-0.04)mm x 61.11(+/-0.13)mm x COIL|3656.7 kg|BA Finish
003|S/S 430 BA NO PI|0.015(+/-0.0015)in x 16.50(+/-0.005)in x COIL|12550#|0.38(+/-0.04)mm x 419.1(+/-0.13)mm x COIL|5692.7 kg|BA Finish
</table_stream>`;

            const parser = new StreamingTableParser();
            const result = parser.addChunk(completeAccumulatedContent);
            
            log('test2', `📊 Parse result: isValid=${result.isValid}, headers=${result.headers.length}, rows=${result.rows.length}`);
            
            if (result.isValid) {
                log('test2', '✅ Table successfully parsed!', 'success');
                
                // Verify data integrity
                result.rows.forEach((row, index) => {
                    const cellCount = Object.keys(row).length;
                    if (cellCount === result.headers.length) {
                        log('test2', `✅ Row ${index + 1}: ${cellCount} cells - data integrity OK`, 'success');
                    } else {
                        log('test2', `❌ Row ${index + 1}: ${cellCount} cells - data integrity FAILED`, 'error');
                    }
                });
                
                // Generate HTML table
                let html = '<table>';
                html += '<thead><tr>';
                result.headers.forEach(header => {
                    html += `<th>${escapeHtml(header)}</th>`;
                });
                html += '</tr></thead>';
                
                html += '<tbody>';
                result.rows.forEach(row => {
                    html += '<tr>';
                    result.headers.forEach(header => {
                        const cellContent = row[header] || '';
                        html += `<td>${escapeHtml(cellContent)}</td>`;
                    });
                    html += '</tr>';
                });
                html += '</tbody></table>';

                document.getElementById('test2-result').innerHTML = html;
                
                log('test2', `✅ Table rendered successfully`, 'success');
            } else {
                log('test2', `❌ Table parsing failed: ${result.error}`, 'error');
            }
        }

        // Run tests
        window.onload = function() {
            test1_simulatedStreaming();
            setTimeout(test2_progressiveStreaming, 3000);
        };
    </script>
</body>
</html>
