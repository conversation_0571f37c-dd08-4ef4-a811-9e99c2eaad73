#!/bin/bash

echo "=== Aliyun SMTP Configuration Fix ==="
echo "Timestamp: $(date)"
echo "======================================"

# Function to print colored output
print_info() {
    echo -e "\e[34m[INFO]\e[0m $1"
}

print_success() {
    echo -e "\e[32m[SUCCESS]\e[0m $1"
}

print_error() {
    echo -e "\e[31m[ERROR]\e[0m $1"
}

print_warning() {
    echo -e "\e[33m[WARNING]\e[0m $1"
}

# Check if we're on <PERSON><PERSON>
if [[ $(hostname) == *"aliyun"* ]] || [[ $(hostname) == *"iZ"* ]]; then
    print_info "Detected Aliyun ECS environment"
else
    print_warning "This script is optimized for Aliyun ECS. Results may vary on other platforms."
fi

# Function to backup existing .env
backup_env() {
    if [ -f .env ]; then
        backup_file=".env.backup.$(date +%Y%m%d_%H%M%S)"
        cp .env "$backup_file"
        print_success "Backed up existing .env to $backup_file"
    fi
}

# Function to update SMTP settings
update_smtp_settings() {
    local smtp_server=$1
    local smtp_port=$2
    local smtp_username=$3
    local smtp_password=$4
    
    print_info "Updating SMTP settings in .env file..."
    
    # Create or update .env file
    if [ -f .env ]; then
        # Update existing settings
        sed -i.bak "s/^SMTP_SERVER=.*/SMTP_SERVER=$smtp_server/" .env
        sed -i.bak "s/^SMTP_PORT=.*/SMTP_PORT=$smtp_port/" .env
        sed -i.bak "s/^SMTP_USERNAME=.*/SMTP_USERNAME=$smtp_username/" .env
        sed -i.bak "s/^SMTP_PASSWORD=.*/SMTP_PASSWORD=$smtp_password/" .env
        
        # Add settings if they don't exist
        grep -q "^SMTP_SERVER=" .env || echo "SMTP_SERVER=$smtp_server" >> .env
        grep -q "^SMTP_PORT=" .env || echo "SMTP_PORT=$smtp_port" >> .env
        grep -q "^SMTP_USERNAME=" .env || echo "SMTP_USERNAME=$smtp_username" >> .env
        grep -q "^SMTP_PASSWORD=" .env || echo "SMTP_PASSWORD=$smtp_password" >> .env
        
        # Add optional settings for better deliverability
        grep -q "^SMTP_SENDER_NAME=" .env || echo "SMTP_SENDER_NAME=SteelNet" >> .env
        grep -q "^SMTP_DOMAIN=" .env || echo "SMTP_DOMAIN=steelnet.ai" >> .env
        grep -q "^SMTP_HOSTNAME=" .env || echo "SMTP_HOSTNAME=unit-converter.steelnet.ai" >> .env
    else
        print_error ".env file not found. Please run this script from the project root directory."
        exit 1
    fi
}

# Function to test SMTP connection
test_smtp_connection() {
    local smtp_server=$1
    local smtp_port=$2
    
    print_info "Testing SMTP connection to $smtp_server:$smtp_port..."
    
    if timeout 10 bash -c "</dev/tcp/$smtp_server/$smtp_port" 2>/dev/null; then
        print_success "✓ Connection to $smtp_server:$smtp_port successful"
        return 0
    else
        print_error "✗ Connection to $smtp_server:$smtp_port failed"
        return 1
    fi
}

echo
echo "=== SMTP Configuration Options ==="
echo "1. Aliyun DirectMail (Recommended for Aliyun ECS)"
echo "2. Gmail with App Password"
echo "3. SendGrid (External service)"
echo "4. QQ Mail (For Chinese users)"
echo "5. Custom SMTP server"
echo "6. Disable email verification (Development only)"
echo

read -p "Choose an option (1-6): " choice

case $choice in
    1)
        print_info "Configuring Aliyun DirectMail..."
        echo
        echo "To use Aliyun DirectMail, you need to:"
        echo "1. Enable DirectMail service in Aliyun console"
        echo "2. Create an SMTP user and get credentials"
        echo "3. Verify your domain"
        echo
        read -p "Enter your DirectMail SMTP username: " dm_username
        read -s -p "Enter your DirectMail SMTP password: " dm_password
        echo
        
        backup_env
        # Aliyun DirectMail uses different endpoints based on region
        echo "Available DirectMail regions:"
        echo "1. cn-hangzhou (Default)"
        echo "2. cn-beijing"
        echo "3. cn-qingdao"
        echo "4. ap-southeast-1 (Singapore)"
        echo "5. ap-southeast-2 (Sydney)"
        read -p "Choose region (1-5, default 1): " region_choice
        
        case $region_choice in
            2) dm_server="smtpdm.ap-southeast-1.aliyuncs.com" ;;
            3) dm_server="smtpdm.ap-southeast-2.aliyuncs.com" ;;
            4) dm_server="smtpdm.ap-southeast-1.aliyuncs.com" ;;
            5) dm_server="smtpdm.ap-southeast-2.aliyuncs.com" ;;
            *) dm_server="smtpdm.aliyun.com" ;;
        esac
        
        update_smtp_settings "$dm_server" "25" "$dm_username" "$dm_password"
        
        # Test connection
        if test_smtp_connection "$dm_server" "25"; then
            print_success "Aliyun DirectMail configuration completed!"
        else
            print_warning "Connection test failed. You may need to:"
            print_warning "1. Enable DirectMail service in Aliyun console"
            print_warning "2. Check your credentials"
            print_warning "3. Verify your domain"
        fi
        ;;
        
    2)
        print_info "Configuring Gmail with App Password..."
        echo
        echo "To use Gmail, you need to:"
        echo "1. Enable 2-factor authentication"
        echo "2. Generate an App Password (not your regular password)"
        echo "3. Use the App Password here"
        echo
        read -p "Enter your Gmail address: " gmail_address
        read -s -p "Enter your Gmail App Password: " gmail_password
        echo
        
        backup_env
        update_smtp_settings "smtp.gmail.com" "587" "$gmail_address" "$gmail_password"
        
        if test_smtp_connection "smtp.gmail.com" "587"; then
            print_success "Gmail configuration completed!"
        else
            print_error "Gmail connection failed. Check your credentials and App Password."
        fi
        ;;
        
    3)
        print_info "Configuring SendGrid..."
        echo
        echo "To use SendGrid:"
        echo "1. Sign up at https://sendgrid.com"
        echo "2. Create an API key"
        echo "3. Use 'apikey' as username and API key as password"
        echo
        read -s -p "Enter your SendGrid API key: " sendgrid_key
        echo
        
        backup_env
        update_smtp_settings "smtp.sendgrid.net" "587" "apikey" "$sendgrid_key"
        
        if test_smtp_connection "smtp.sendgrid.net" "587"; then
            print_success "SendGrid configuration completed!"
        else
            print_error "SendGrid connection failed. Check your API key."
        fi
        ;;
        
    4)
        print_info "Configuring QQ Mail..."
        echo
        echo "To use QQ Mail:"
        echo "1. Enable SMTP service in QQ Mail settings"
        echo "2. Generate an authorization code (not your QQ password)"
        echo
        read -p "Enter your QQ email address: " qq_address
        read -s -p "Enter your QQ authorization code: " qq_password
        echo
        
        backup_env
        update_smtp_settings "smtp.qq.com" "587" "$qq_address" "$qq_password"
        
        if test_smtp_connection "smtp.qq.com" "587"; then
            print_success "QQ Mail configuration completed!"
        else
            print_error "QQ Mail connection failed. Check your credentials."
        fi
        ;;
        
    5)
        print_info "Configuring custom SMTP server..."
        read -p "Enter SMTP server hostname: " custom_server
        read -p "Enter SMTP port (25/465/587): " custom_port
        read -p "Enter SMTP username: " custom_username
        read -s -p "Enter SMTP password: " custom_password
        echo
        
        backup_env
        update_smtp_settings "$custom_server" "$custom_port" "$custom_username" "$custom_password"
        
        if test_smtp_connection "$custom_server" "$custom_port"; then
            print_success "Custom SMTP configuration completed!"
        else
            print_error "Custom SMTP connection failed. Check your settings."
        fi
        ;;
        
    6)
        print_warning "Disabling email verification (Development only)..."
        backup_env
        
        # Comment out SMTP settings to disable email
        sed -i.bak 's/^SMTP_SERVER=/#SMTP_SERVER=/' .env
        sed -i.bak 's/^SMTP_PORT=/#SMTP_PORT=/' .env
        sed -i.bak 's/^SMTP_USERNAME=/#SMTP_USERNAME=/' .env
        sed -i.bak 's/^SMTP_PASSWORD=/#SMTP_PASSWORD=/' .env
        
        print_success "Email verification disabled. Verification codes will be logged to console."
        ;;
        
    *)
        print_error "Invalid choice. Exiting."
        exit 1
        ;;
esac

echo
echo "=== Testing Python SMTP Connection ==="
print_info "Running Python SMTP test..."

# Create and run Python test
python3 debug_smtp.py

echo
echo "=== Final Recommendations ==="
echo "1. If you're using Aliyun DirectMail, make sure:"
echo "   - DirectMail service is enabled"
echo "   - Your domain is verified"
echo "   - SMTP user is created and active"
echo ""
echo "2. For better email deliverability:"
echo "   - Set up SPF records for your domain"
echo "   - Configure DKIM signing"
echo "   - Monitor your sender reputation"
echo ""
echo "3. If emails still fail:"
echo "   - Check Aliyun security groups for port restrictions"
echo "   - Verify your SMTP credentials"
echo "   - Consider using external email services"
echo ""
echo "4. To restart your application:"
echo "   - If using systemd: sudo systemctl restart your-app"
echo "   - If using docker: docker-compose restart"
echo "   - If running manually: restart your Python application"

print_success "SMTP configuration completed!" 