<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Structured Table Format</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-input {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .test-output {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .copy-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .copy-btn:hover {
            background: #0056b3;
        }
        .copy-status {
            color: #28a745;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>Test Structured Table Format</h1>
    
    <div class="test-section">
        <h2>Test 1: Structured Table Data Format</h2>
        <p>Testing the new structured table format with JSON data inside &lt;table_data&gt; tags:</p>
        
        <div class="test-input" id="test1-input">
<converted_content>
Here is your conversion result:

<table_data>
{
  "headers": ["Item Code", "Description", "Size (Original)", "Customer QTY", "Size (Converted)", "Converted QTY", "Remarks"],
  "rows": [
    {
      "Item Code": "001",
      "Description": "304 Stainless Steel Sheet",
      "Size (Original)": "0.125\" x 48\" x 96\"",
      "Customer QTY": "500 lbs",
      "Size (Converted)": "3.18mm x 1219mm x 2438mm",
      "Converted QTY": "226.8 kg",
      "Remarks": "2B Finish"
    },
    {
      "Item Code": "002", 
      "Description": "316L Stainless Steel Plate",
      "Size (Original)": "0.25\" x 24\" x 48\"",
      "Customer QTY": "1000 lbs",
      "Size (Converted)": "6.35mm x 610mm x 1219mm",
      "Converted QTY": "453.6 kg",
      "Remarks": "Hot Rolled"
    }
  ]
}
</table_data>

All conversions completed successfully.
</converted_content>
        </div>
        
        <div class="test-output" id="test1-output">
            <!-- Output will be generated here -->
        </div>
        
        <button class="copy-btn" onclick="copyTable('test1')">Copy for Excel</button>
        <button class="copy-btn" onclick="copyTSV('test1')">Copy as TSV</button>
        <span id="test1-status" class="copy-status"></span>
    </div>

    <div class="test-section">
        <h2>Test 2: Traditional Markdown Table</h2>
        <p>Testing backward compatibility with traditional markdown tables:</p>
        
        <div class="test-input" id="test2-input">
<converted_content>
| Item Code | Description | Size (Original) | Customer QTY | Size (Converted) | Converted QTY | Remarks |
|-----------|-------------|-----------------|--------------|------------------|---------------|---------|
| 001 | 304 Stainless Steel Sheet | 0.125" x 48" x 96" | 500 lbs | 3.18mm x 1219mm x 2438mm | 226.8 kg | 2B Finish |
| 002 | 316L Stainless Steel Plate | 0.25" x 24" x 48" | 1000 lbs | 6.35mm x 610mm x 1219mm | 453.6 kg | Hot Rolled |
</converted_content>
        </div>
        
        <div class="test-output" id="test2-output">
            <!-- Output will be generated here -->
        </div>
        
        <button class="copy-btn" onclick="copyTable('test2')">Copy for Excel</button>
        <button class="copy-btn" onclick="copyTSV('test2')">Copy as TSV</button>
        <span id="test2-status" class="copy-status"></span>
    </div>

    <script>
        // Import the table utility functions (simplified versions for testing)
        function parseConvertedContent(content) {
            const convertedContentMatch = content.match(/<converted_content>([\s\S]*?)<\/converted_content>/);
            
            if (convertedContentMatch) {
                const convertedText = convertedContentMatch[1].trim();
                const cleanContent = content.replace(/<converted_content>[\s\S]*?<\/converted_content>/, '').trim();
                
                // Check for structured table data first
                const tableDataMatch = convertedText.match(/<table_data>([\s\S]*?)<\/table_data>/);
                if (tableDataMatch) {
                    try {
                        const tableData = JSON.parse(tableDataMatch[1].trim());
                        const remainingContent = convertedText.replace(/<table_data>[\s\S]*?<\/table_data>/, '').trim();
                        
                        return {
                            hasTable: true,
                            tableContent: '',
                            cleanContent: cleanContent || remainingContent,
                            tableData: tableData
                        };
                    } catch (error) {
                        console.error('Failed to parse structured table data:', error);
                    }
                }
                
                // Fallback to markdown table detection
                const hasTable = convertedText.includes('|') && convertedText.includes('\n|');
                
                return {
                    hasTable,
                    tableContent: hasTable ? convertedText : '',
                    cleanContent: cleanContent || convertedText
                };
            }
            
            return {
                hasTable: false,
                tableContent: '',
                cleanContent: content
            };
        }

        function parseMarkdownTable(markdownTable) {
            const lines = markdownTable.trim().split('\n').filter(line => line.trim().length > 0);
            
            if (lines.length < 1) {
                return { headers: [], rows: [], isValid: false };
            }
            
            const headerLine = lines[0].trim();
            let headers = [];
            
            if (headerLine.includes('|')) {
                const cleanHeader = headerLine.replace(/^\||\|$/g, '');
                headers = cleanHeader.split('|').map(h => h.trim()).filter(h => h.length > 0);
            }
            
            if (headers.length === 0) {
                return { headers: [], rows: [], isValid: false };
            }
            
            // Find separator line
            let separatorIndex = -1;
            for (let i = 1; i < Math.min(3, lines.length); i++) {
                const line = lines[i].trim();
                if (line.match(/^[\|\s]*[-:]+[\|\s-:]*$/)) {
                    separatorIndex = i;
                    break;
                }
            }
            
            let startRowIndex = separatorIndex !== -1 ? separatorIndex + 1 : 1;
            
            const rows = [];
            for (let i = startRowIndex; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line || line.startsWith('<!--') || line.match(/^[\|\s-:]*$/)) continue;
                
                let cells = [];
                if (line.includes('|')) {
                    const cleanLine = line.replace(/^\||\|$/g, '');
                    cells = cleanLine.split('|').map(cell => cell.trim());
                }
                
                if (cells.some(cell => cell.length > 0)) {
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header] = cells[index] || '';
                    });
                    rows.push(row);
                }
            }
            
            return { headers, rows, isValid: headers.length > 0 && rows.length > 0 };
        }

        function renderTable(testId) {
            const inputElement = document.getElementById(`${testId}-input`);
            const outputElement = document.getElementById(`${testId}-output`);
            const content = inputElement.textContent;
            
            const { hasTable, tableContent, cleanContent, tableData } = parseConvertedContent(content);
            
            let html = '';
            
            if (cleanContent) {
                html += `<h4>Text Content:</h4><p>${cleanContent}</p>`;
            }
            
            if (hasTable) {
                let parsedTable;
                
                if (tableData && tableData.headers && tableData.rows) {
                    parsedTable = {
                        headers: tableData.headers,
                        rows: tableData.rows,
                        isValid: true
                    };
                } else if (tableContent) {
                    parsedTable = parseMarkdownTable(tableContent);
                }
                
                if (parsedTable && parsedTable.isValid) {
                    html += '<h4>Parsed Table:</h4>';
                    html += '<table>';
                    html += '<thead><tr>';
                    parsedTable.headers.forEach(header => {
                        html += `<th>${header}</th>`;
                    });
                    html += '</tr></thead>';
                    html += '<tbody>';
                    parsedTable.rows.forEach(row => {
                        html += '<tr>';
                        parsedTable.headers.forEach(header => {
                            html += `<td>${row[header] || ''}</td>`;
                        });
                        html += '</tr>';
                    });
                    html += '</tbody></table>';
                    
                    // Store parsed table for copying
                    window[`${testId}ParsedTable`] = parsedTable;
                } else {
                    html += '<p style="color: red;">Failed to parse table</p>';
                }
            }
            
            outputElement.innerHTML = html;
        }

        function copyTable(testId) {
            const parsedTable = window[`${testId}ParsedTable`];
            if (!parsedTable) return;
            
            // Create HTML table for Excel
            let html = '<table border="1" cellpadding="4" cellspacing="0" style="border-collapse: collapse;">';
            html += '<thead><tr style="background-color: #f0f0f0; font-weight: bold;">';
            parsedTable.headers.forEach(header => {
                html += `<th style="border: 1px solid #ccc; padding: 8px;">${header}</th>`;
            });
            html += '</tr></thead><tbody>';
            parsedTable.rows.forEach((row, rowIndex) => {
                const rowStyle = rowIndex % 2 === 0 ? 'background-color: #ffffff;' : 'background-color: #f9f9f9;';
                html += `<tr style="${rowStyle}">`;
                parsedTable.headers.forEach(header => {
                    html += `<td style="border: 1px solid #ccc; padding: 8px;">${row[header] || ''}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody></table>';
            
            // Copy to clipboard
            try {
                const clipboardItem = new ClipboardItem({
                    'text/html': new Blob([html], { type: 'text/html' })
                });
                navigator.clipboard.write([clipboardItem]).then(() => {
                    document.getElementById(`${testId}-status`).textContent = 'Copied as HTML!';
                    setTimeout(() => {
                        document.getElementById(`${testId}-status`).textContent = '';
                    }, 2000);
                });
            } catch (error) {
                console.error('Copy failed:', error);
                document.getElementById(`${testId}-status`).textContent = 'Copy failed';
            }
        }

        function copyTSV(testId) {
            const parsedTable = window[`${testId}ParsedTable`];
            if (!parsedTable) return;
            
            // Create TSV format
            let tsv = parsedTable.headers.join('\t') + '\n';
            parsedTable.rows.forEach(row => {
                const rowData = parsedTable.headers.map(header => row[header] || '');
                tsv += rowData.join('\t') + '\n';
            });
            
            navigator.clipboard.writeText(tsv).then(() => {
                document.getElementById(`${testId}-status`).textContent = 'Copied as TSV!';
                setTimeout(() => {
                    document.getElementById(`${testId}-status`).textContent = '';
                }, 2000);
            });
        }

        // Render tables on page load
        window.onload = function() {
            renderTable('test1');
            renderTable('test2');
        };
    </script>
</body>
</html>
