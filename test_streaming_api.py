#!/usr/bin/env python3
import requests
import json
import time
import os

# Disable proxy for this test
os.environ.pop('http_proxy', None)
os.environ.pop('https_proxy', None)
os.environ.pop('all_proxy', None)

def test_streaming_api():
    """Test the streaming API to see what format it returns"""
    
    print("Testing streaming API...")
    
    # Test data from the user's test case
    test_data = """S/S 430  BA  NO PI
 
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#"""

    url = "http://localhost:8000/api/llm/stream"
    payload = {
        "text": test_data,
        "function": "table",
        "unit_system": "metric"
    }
    
    try:
        print(f"Making request to: {url}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(url, json=payload, stream=True, timeout=60)
        
        if response.status_code != 200:
            print(f"Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return
        
        print(f"Response status: {response.status_code}")
        print("Streaming response:")
        print("=" * 50)
        
        full_response = ""
        chunk_count = 0
        
        for line in response.iter_lines():
            if line:
                chunk_count += 1
                decoded_line = line.decode('utf-8')
                full_response += decoded_line
                print(f"Chunk {chunk_count}: {repr(decoded_line)}")
        
        print("=" * 50)
        print("Full response:")
        print(full_response)
        print("=" * 50)
        
        # Check what format the response contains
        if '<table_stream>' in full_response:
            print("✓ Response contains <table_stream> format")
            # Extract table content
            start = full_response.find('<table_stream>')
            end = full_response.find('</table_stream>')
            if start != -1 and end != -1:
                table_content = full_response[start:end+15]
                print("Table content:")
                print(table_content)
                print("\n" + "="*50)
        elif '<table_data>' in full_response:
            print("✓ Response contains <table_data> format")
        elif '|' in full_response and '\n|' in full_response:
            print("✓ Response contains markdown table format")
            # Show first few lines of table
            lines = full_response.split('\n')
            table_lines = [line for line in lines if '|' in line][:5]
            print("Sample table lines:")
            for line in table_lines:
                print(f"  {line}")
        else:
            print("✗ No table format detected")

        # Show last 500 characters to see the end of response
        print(f"\nLast 500 characters of response:")
        print("="*50)
        print(repr(full_response[-500:]))
        print("="*50)
        
        print(f"\nTotal chunks received: {chunk_count}")
        print(f"Total response length: {len(full_response)} characters")
        
    except requests.exceptions.RequestException as e:
        print(f"Request error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    test_streaming_api()
