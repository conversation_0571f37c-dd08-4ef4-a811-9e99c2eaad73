#!/usr/bin/env bash

# Steel Unit Converter - Production Startup Script (Fixed for 2GB memory systems)
# This script starts the application in production mode with memory optimizations

# Set script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
LOG_DIR="$SCRIPT_DIR/logs"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Set default values
ACTION="start"
COMPONENT="all"
SHOW_LOGS_AFTER=false
SETUP_URL_ACCESS=false
LOW_MEMORY_MODE=true  # Always enable low memory mode
MEMORY_PER_WORKER=150
WORKERS=1  # Force single worker
THREADS=4

# Set Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=256"

# Function to check if Python is installed
check_python() {
    echo "Checking for Python..."
    if command -v python3 &> /dev/null; then
        echo "Found Python: $(python3 --version)"
        # Create alias for python -> python3 if needed
        if ! command -v python &> /dev/null; then
            echo "Creating alias for python -> python3"
            alias python=python3
        fi
        return 0
    elif command -v python &> /dev/null; then
        echo "Found Python: $(python --version)"
        return 0
    else
        echo "Python not found. Please install Python 3.8 or higher."
        exit 1
    fi
}

# Function to check if Node.js is installed
check_node() {
    echo "Checking for Node.js..."
    if command -v node &> /dev/null; then
        echo "Found Node.js: $(node --version)"
        return 0
    else
        echo "Node.js not found. Please install Node.js 14 or higher."
        exit 1
    fi
}

# Function to kill existing processes
kill_existing() {
    echo "Checking for existing processes..."

    # Kill processes on ports 8000 and 3000
    if command -v lsof &> /dev/null; then
        echo "Killing process on port 8000..."
        lsof -ti:8000 | xargs kill -9 2>/dev/null || true

        echo "Killing process on port 3000..."
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
    else
        # Alternative method if lsof is not available
        echo "lsof not found, using alternative method to kill processes..."

        # Find and kill Python/Gunicorn processes
        pkill -f "gunicorn main:app" 2>/dev/null || true
        pkill -f "uvicorn main:app" 2>/dev/null || true

        # Find and kill Node.js processes for frontend
        pkill -f "node.*serve -s dist" 2>/dev/null || true
        pkill -f "node.*vite preview" 2>/dev/null || true
    fi

    echo "All existing processes killed."
}

# Function to start the backend
start_backend() {
    echo "Starting backend in production mode..."

    # Change directory to backend
    cd "$BACKEND_DIR" || exit

    # Create and activate virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo "Creating virtual environment..."
        python3 -m venv venv
    fi

    # Activate virtual environment
    source venv/bin/activate

    # Install dependencies
    echo "Installing backend dependencies..."
    pip install -r requirements.txt
    pip install gunicorn
    pip install uvicorn

    # Set environment variables
    export ENV="production"
    export DEBUG="False"

    # Fix import issues
    echo "Fixing import issues..."
    python fix_imports.py

    # Check for missing dependencies
    echo "Checking for missing dependencies..."
    python install_missing_deps.py

    # Start backend with Gunicorn (single worker, 4 threads)
    echo "Starting backend with Gunicorn (1 worker, 4 threads)..."
    nohup gunicorn main:app \
        --worker-class uvicorn.workers.UvicornWorker \
        --workers 1 \
        --threads 4 \
        --bind 0.0.0.0:8000 \
        --max-requests 500 \
        --max-requests-jitter 50 \
        --timeout 120 \
        --keep-alive 5 \
        --log-level warning \
        --worker-tmp-dir /tmp \
        --preload \
        > "$LOG_DIR/backend.log" 2>&1 &

    BACKEND_PID=$!
    echo "Backend started with PID: $BACKEND_PID"
    echo "Backend logs available at: $LOG_DIR/backend.log"

    # Save PID to file for later reference
    echo $BACKEND_PID > "$LOG_DIR/backend.pid"

    # Return to script directory
    cd "$SCRIPT_DIR" || exit
}

# Function to start the frontend
start_frontend() {
    echo "Starting frontend in production mode..."

    # Change directory to frontend
    cd "$FRONTEND_DIR" || exit

    # Install dependencies
    echo "Installing frontend dependencies..."
    npm install --production

    # Install vite globally if not already installed
    if ! command -v vite &> /dev/null; then
        echo "Installing vite globally..."
        npm install -g vite
    fi

    # Create a minimal vite.config.js for low memory build
    echo "Creating minimal vite.config.js for production..."
    cat > vite.config.js << 'EOF'
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  build: {
    outDir: 'dist',
    minify: false,
    sourcemap: false,
    target: 'es2015',
    cssCodeSplit: true,
    assetsInlineLimit: 4096,
    emptyOutDir: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          material: ['@mui/material'],
          icons: ['@mui/icons-material'],
          emotion: ['@emotion/react', '@emotion/styled'],
        }
      }
    }
  }
});
EOF

    # Check if dist directory exists, if not build it
    if [ ! -d "dist" ] || [ ! -f "dist/index.html" ]; then
        echo "Building frontend with minimal settings..."
        # Try different build approaches with increasing memory optimization
        npm run build:minimal || NODE_OPTIONS="--max-old-space-size=128" npm run build -- --mode=production --minify=false || npm run build -- --mode=production || {
            echo "Frontend build failed. Using static files instead."
            mkdir -p dist
            echo "<html><body><h1>Steel Unit Converter</h1><p>Frontend build failed. Please contact support.</p></body></html>" > dist/index.html
        }
    else
        echo "Using existing dist directory."
    fi

    # Serve frontend with a lightweight server
    echo "Starting frontend server..."

    # Install serve locally if not already installed
    if ! npm list | grep -q serve; then
        echo "Installing serve locally..."
        npm install serve
    fi

    # Use npx to run serve from local node_modules
    echo "Using serve for static file serving..."
    nohup npx serve -s dist -l tcp://0.0.0.0:3000 --no-clipboard --single > "$LOG_DIR/frontend.log" 2>&1 &

    FRONTEND_PID=$!
    echo "Frontend server started with PID: $FRONTEND_PID"
    echo "Frontend logs available at: $LOG_DIR/frontend.log"

    # Save PID to file for later reference
    echo $FRONTEND_PID > "$LOG_DIR/frontend.pid"

    # Return to script directory
    cd "$SCRIPT_DIR" || exit
}

# Main function
main() {
    # Check requirements
    check_python
    check_node

    # Kill existing processes
    kill_existing

    # Start services based on component
    if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "backend" ]; then
        start_backend
    fi

    if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "frontend" ]; then
        start_frontend
    fi

    echo "Services started successfully."

    # Show summary
    echo ""
    echo "=================================================="
    echo "  Steel Unit Converter - Production Mode  "
    echo "=================================================="
    echo ""
    echo "Application URLs (direct access):"
    echo "  Backend: http://localhost:8000"
    echo "  Frontend: http://localhost:3000"
    echo "  API: http://localhost:8000/api"
    echo ""
    echo "Low memory mode is ENABLED"
    echo ""
    echo "Management commands:"
    echo "  Stop:             ./start-prod-fixed.sh stop"
    echo "  Status:           ./start-prod-fixed.sh status"
    echo "  View Logs:        ./start-prod-fixed.sh logs"
}

# Function to stop all services
stop_services() {
    kill_existing
    echo "All services stopped."
}

# Function to check service status
check_status() {
    echo "Checking service status..."

    # Check if backend is running
    if command -v lsof &> /dev/null; then
        echo "Backend (port 8000):"
        lsof -i:8000 || echo "Backend is not running."

        echo "Frontend (port 3000):"
        lsof -i:3000 || echo "Frontend is not running."
    else
        echo "lsof not found, cannot check port status."
    fi
}

# Function to show logs
show_logs() {
    if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "backend" ]; then
        echo "Backend logs:"
        tail -n 50 "$LOG_DIR/backend.log"
    fi

    if [ "$COMPONENT" = "all" ] || [ "$COMPONENT" = "frontend" ]; then
        echo "Frontend logs:"
        tail -n 50 "$LOG_DIR/frontend.log"
    fi
}

# Parse command line arguments
if [ $# -gt 0 ]; then
    case "$1" in
        start)
            ACTION="start"
            shift
            ;;
        stop)
            ACTION="stop"
            shift
            ;;
        status)
            ACTION="status"
            shift
            ;;
        logs)
            ACTION="logs"
            shift
            ;;
        *)
            echo "Unknown action: $1"
            echo "Usage: $0 [start|stop|status|logs]"
            exit 1
            ;;
    esac
fi

# Execute action
case "$ACTION" in
    start)
        main
        ;;
    stop)
        stop_services
        ;;
    status)
        check_status
        ;;
    logs)
        show_logs
        ;;
esac

# Make script executable
chmod +x "$0"
