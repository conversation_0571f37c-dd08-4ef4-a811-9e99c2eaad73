version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: backend
    container_name: steel-converter-backend
    restart: always
    environment:
      - ENV=production
      - RDS_HOSTNAME=${RDS_HOSTNAME}
      - RDS_PORT=${RDS_PORT}
      - RDS_DB_NAME=${RDS_DB_NAME}
      - RDS_USERNAME=${RDS_USERNAME}
      - RDS_PASSWORD=${RDS_PASSWORD}
      - DATABASE_URL=${RDS_DATABASE_URL}
      - DB_POOL_SIZE=${DB_POOL_SIZE:-20}
      - DB_MAX_OVERFLOW=${DB_MAX_OVERFLOW:-30}
      - DB_POOL_TIMEOUT=${DB_POOL_TIMEOUT:-30}
      - DB_POOL_RECYCLE=${DB_POOL_RECYCLE:-1800}
      - SQL_ECHO=${SQL_ECHO:-False}
      - SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_ALGORITHM=${JWT_ALGORITHM:-HS256}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-1440}
      - SMTP_SERVER=${SMTP_SERVER}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SMTP_SENDER_NAME=${SMTP_SENDER_NAME}
      - SMTP_DOMAIN=${SMTP_DOMAIN}
      - SMTP_HOSTNAME=${SMTP_HOSTNAME}
      - EMAIL_RATE_LIMIT=${EMAIL_RATE_LIMIT:-10}
      - EMAIL_RATE_LIMIT_PERIOD=${EMAIL_RATE_LIMIT_PERIOD:-3600}
      - CORS_ORIGINS=${CORS_ORIGINS}
      - RATE_LIMIT_PER_MINUTE=${RATE_LIMIT_PER_MINUTE:-60}
      - VOLCANO_ENGINE_API_KEY=${VOLCANO_ENGINE_API_KEY}
      - VOLCANO_API_KEY=${VOLCANO_API_KEY}
      - VOLCANO_ENGINE_ENDPOINT_ID=${VOLCANO_ENGINE_ENDPOINT_ID}
      - VOLCANO_ENDPOINT_ID=${VOLCANO_ENDPOINT_ID}
      - VOLCANO_ENGINE_API_URL=${VOLCANO_ENGINE_API_URL}
      - VOLCANO_ENGINE_TIMEOUT=${VOLCANO_ENGINE_TIMEOUT:-300}
      - VOLCANO_ENGINE_PROMPT_TEMPLATE=${VOLCANO_ENGINE_PROMPT_TEMPLATE:-llm_prompt.txt}
      - LOG_LEVEL=${LOG_LEVEL:-WARNING}
      - SENTRY_DSN=${SENTRY_DSN}
    mem_limit: ${BACKEND_MEMORY_LIMIT:-512m}
    cpus: 0.5
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: frontend
    container_name: steel-converter-frontend
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    mem_limit: ${FRONTEND_MEMORY_LIMIT:-256m}
    cpus: 0.3
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
