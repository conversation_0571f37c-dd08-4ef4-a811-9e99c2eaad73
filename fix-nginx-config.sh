#!/bin/bash

# Script to fix nginx configuration issues
# This script creates a proper nginx configuration file

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where the script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Public IP address
PUBLIC_IP="************"

# Function to print section header
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}\n"
}

# Function to print success message
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print warning message
print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Function to print error message
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Function to create SSL certificates
create_ssl_certificates() {
    print_header "Creating SSL certificates"
    
    # Create SSL directory if it doesn't exist
    if [ ! -d "$SCRIPT_DIR/ssl" ]; then
        echo "Creating SSL directory..."
        mkdir -p "$SCRIPT_DIR/ssl"
    fi
    
    # Generate self-signed certificates if they don't exist
    if [ ! -f "$SCRIPT_DIR/ssl/cert.pem" ] || [ ! -f "$SCRIPT_DIR/ssl/key.pem" ]; then
        echo "Generating self-signed SSL certificates..."
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$SCRIPT_DIR/ssl/key.pem" -out "$SCRIPT_DIR/ssl/cert.pem" \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=steelnet.ai"
        
        if [ $? -eq 0 ]; then
            print_success "SSL certificates generated successfully"
        else
            print_error "Failed to generate SSL certificates"
            return 1
        fi
    else
        print_success "SSL certificates already exist"
    fi
    
    return 0
}

# Function to create proper nginx configuration
create_nginx_config() {
    print_header "Creating proper nginx configuration"
    
    # Create a proper nginx configuration file
    echo "Creating nginx configuration file..."
    cat > /tmp/steelnet.conf << EOF
server {
    listen 80;
    listen [::]:80;
    server_name $PUBLIC_IP steelnet.ai;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://\$host\$request_uri;
}

server {
    listen 443 ssl;
    listen [::]:443 ssl;
    server_name $PUBLIC_IP steelnet.ai;
    
    # SSL configuration
    ssl_certificate $SCRIPT_DIR/ssl/cert.pem;
    ssl_certificate_key $SCRIPT_DIR/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    
    # SSL optimizations
    ssl_session_cache shared:SSL:2m;
    ssl_session_timeout 10m;
    
    # Frontend proxy
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://localhost:3000;
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
    
    # Copy nginx configuration to nginx directory
    echo "Copying nginx configuration to /etc/nginx/conf.d/steelnet.conf..."
    sudo cp /tmp/steelnet.conf /etc/nginx/conf.d/steelnet.conf
    
    # Test nginx configuration
    echo "Testing nginx configuration..."
    if sudo nginx -t; then
        print_success "nginx configuration is valid"
        
        # Restart nginx
        echo "Restarting nginx..."
        sudo systemctl restart nginx || sudo service nginx restart
        
        # Check if nginx restarted successfully
        if systemctl is-active --quiet nginx || service nginx status > /dev/null 2>&1; then
            print_success "nginx restarted successfully"
            return 0
        else
            print_error "Failed to restart nginx"
            return 1
        fi
    else
        print_error "nginx configuration is invalid"
        return 1
    fi
}

# Function to optimize nginx main configuration
optimize_nginx_main_config() {
    print_header "Optimizing nginx main configuration"
    
    # Backup original nginx.conf
    echo "Backing up original nginx.conf..."
    sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.bak
    
    # Create optimized nginx.conf
    echo "Creating optimized nginx.conf..."
    cat > /tmp/nginx.conf << EOF
user nginx;
worker_processes 1;
worker_rlimit_nofile 1024;
pid /var/run/nginx.pid;

events {
    worker_connections 512;
    multi_accept off;
}

http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Buffer size settings (reduced for low memory)
    client_body_buffer_size 8k;
    client_header_buffer_size 1k;
    client_max_body_size 1m;
    large_client_header_buffers 2 1k;
    
    # Gzip settings (save CPU)
    gzip on;
    gzip_min_length 1000;
    gzip_comp_level 2;
    gzip_types text/plain text/css application/javascript application/json;
    
    # Logging settings
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    
    # Virtual Host Configs
    include /etc/nginx/conf.d/*.conf;
    include /etc/nginx/sites-enabled/*;
}
EOF
    
    # Copy nginx.conf to nginx directory
    echo "Copying nginx.conf to /etc/nginx/nginx.conf..."
    sudo cp /tmp/nginx.conf /etc/nginx/nginx.conf
    
    # Test nginx configuration
    echo "Testing nginx configuration..."
    if sudo nginx -t; then
        print_success "nginx main configuration is valid"
        return 0
    else
        print_error "nginx main configuration is invalid"
        
        # Restore backup
        echo "Restoring backup..."
        sudo cp /etc/nginx/nginx.conf.bak /etc/nginx/nginx.conf
        
        return 1
    fi
}

# Function to check if nginx is running
check_nginx_running() {
    print_header "Checking if nginx is running"
    
    if systemctl is-active --quiet nginx || service nginx status > /dev/null 2>&1; then
        print_success "nginx is running"
        return 0
    else
        print_error "nginx is not running"
        
        # Try to start nginx
        echo "Trying to start nginx..."
        sudo systemctl start nginx || sudo service nginx start
        
        # Check if nginx started successfully
        if systemctl is-active --quiet nginx || service nginx status > /dev/null 2>&1; then
            print_success "nginx started successfully"
            return 0
        else
            print_error "Failed to start nginx"
            return 1
        fi
    fi
}

# Main function
main() {
    print_header "Fixing nginx Configuration"
    echo "This script will fix nginx configuration issues"
    echo "Public IP: $PUBLIC_IP"
    echo "Script directory: $SCRIPT_DIR"
    
    # Create SSL certificates
    create_ssl_certificates
    
    # Optimize nginx main configuration
    optimize_nginx_main_config
    
    # Create proper nginx configuration
    create_nginx_config
    
    # Check if nginx is running
    check_nginx_running
    
    print_header "Summary"
    echo "nginx configuration has been fixed."
    echo "You should now be able to access your application at:"
    echo "  HTTPS: https://$PUBLIC_IP"
    echo "  HTTP: http://$PUBLIC_IP (redirects to HTTPS)"
    
    echo -e "\nIf you're still having issues, please check the logs:"
    echo "  nginx error log: sudo tail -f /var/log/nginx/error.log"
    echo "  nginx access log: sudo tail -f /var/log/nginx/access.log"
    
    echo -e "\nYou can also try restarting the application with:"
    echo "  ./start-prod.sh --restart --url-access"
}

# Run the main function
main
