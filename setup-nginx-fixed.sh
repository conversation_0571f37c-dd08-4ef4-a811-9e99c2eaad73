#!/usr/bin/env bash

# Setup Nginx for Steel Unit Converter (Fixed for 2GB memory systems)
# This script installs and configures Nginx for production use

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$SCRIPT_DIR/backend"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
LOG_DIR="$SCRIPT_DIR/logs"
SSL_DIR="$SCRIPT_DIR/ssl"

# Create logs and SSL directories if they don't exist
mkdir -p "$LOG_DIR"
mkdir -p "$SSL_DIR"

# Function to check if a command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Function to install Nginx
install_nginx() {
    echo "Installing Nginx..."
    
    if command_exists apt-get; then
        sudo apt-get update && sudo apt-get install -y nginx
        return $?
    elif command_exists yum; then
        sudo yum install -y nginx
        return $?
    elif command_exists brew; then
        brew install nginx
        return $?
    else
        echo "Could not find a package manager to install Nginx."
        return 1
    fi
}

# Function to generate self-signed SSL certificates
generate_ssl_certs() {
    echo "Generating self-signed SSL certificates..."
    
    if [ -f "$SSL_DIR/cert.pem" ] && [ -f "$SSL_DIR/key.pem" ]; then
        echo "SSL certificates already exist."
        return 0
    fi
    
    if command_exists openssl; then
        openssl req -x509 -newkey rsa:2048 -keyout "$SSL_DIR/key.pem" -out "$SSL_DIR/cert.pem" -days 365 -nodes -subj "/CN=localhost" -addext "subjectAltName=DNS:localhost,IP:127.0.0.1"
        return $?
    else
        echo "OpenSSL not found. Cannot generate SSL certificates."
        return 1
    fi
}

# Function to create Nginx configuration
create_nginx_config() {
    echo "Creating Nginx configuration..."
    
    # Determine Nginx configuration directory
    NGINX_CONF_DIR=""
    if [ -d "/etc/nginx/conf.d" ]; then
        NGINX_CONF_DIR="/etc/nginx/conf.d"
    elif [ -d "/usr/local/etc/nginx/conf.d" ]; then
        NGINX_CONF_DIR="/usr/local/etc/nginx/conf.d"
    elif [ -d "/usr/local/etc/nginx/servers" ]; then
        NGINX_CONF_DIR="/usr/local/etc/nginx/servers"
    else
        echo "Could not find Nginx configuration directory."
        return 1
    fi
    
    echo "Using Nginx configuration directory: $NGINX_CONF_DIR"
    
    # Create Nginx configuration file optimized for low memory
    cat > /tmp/steelnet.conf << EOF
# Steel Unit Converter Nginx Configuration (Low Memory)

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name localhost;
    
    # Redirect all HTTP requests to HTTPS
    return 301 https://\$host\$request_uri;
}

server {
    listen 443 ssl;
    server_name localhost;
    
    # SSL configuration
    ssl_certificate $SSL_DIR/cert.pem;
    ssl_certificate_key $SSL_DIR/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    
    # SSL optimizations for low memory
    ssl_session_cache shared:SSL:1m;
    ssl_session_timeout 5m;
    
    # Buffer size settings (reduced for low memory)
    client_body_buffer_size 8k;
    client_header_buffer_size 1k;
    client_max_body_size 1m;
    large_client_header_buffers 2 1k;
    
    # Frontend proxy
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        
        # Add caching for static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://localhost:3000;
            expires 30d;
            add_header Cache-Control "public, no-transform";
        }
    }
    
    # Backend API proxy
    location /api {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
EOF
    
    # Copy configuration file to Nginx directory
    if command_exists sudo; then
        sudo cp /tmp/steelnet.conf "$NGINX_CONF_DIR/steelnet.conf"
    else
        cp /tmp/steelnet.conf "$NGINX_CONF_DIR/steelnet.conf"
    fi
    
    return $?
}

# Function to create Nginx main configuration
create_nginx_main_config() {
    echo "Creating Nginx main configuration..."
    
    # Determine Nginx main configuration file
    NGINX_MAIN_CONF=""
    if [ -f "/etc/nginx/nginx.conf" ]; then
        NGINX_MAIN_CONF="/etc/nginx/nginx.conf"
    elif [ -f "/usr/local/etc/nginx/nginx.conf" ]; then
        NGINX_MAIN_CONF="/usr/local/etc/nginx/nginx.conf"
    else
        echo "Could not find Nginx main configuration file."
        return 1
    fi
    
    echo "Using Nginx main configuration file: $NGINX_MAIN_CONF"
    
    # Backup original configuration
    if command_exists sudo; then
        sudo cp "$NGINX_MAIN_CONF" "$NGINX_MAIN_CONF.bak"
    else
        cp "$NGINX_MAIN_CONF" "$NGINX_MAIN_CONF.bak"
    fi
    
    # Create optimized main configuration for low memory
    cat > /tmp/nginx.conf << EOF
# Steel Unit Converter Nginx Main Configuration (Low Memory)

worker_processes 1;
worker_rlimit_nofile 1024;

events {
    worker_connections 512;
    multi_accept off;
}

http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # MIME types
    include mime.types;
    default_type application/octet-stream;
    
    # Buffer size settings (reduced for low memory)
    client_body_buffer_size 8k;
    client_header_buffer_size 1k;
    client_max_body_size 1m;
    large_client_header_buffers 2 1k;
    
    # Gzip settings (save CPU)
    gzip on;
    gzip_min_length 1000;
    gzip_comp_level 2;
    gzip_types text/plain text/css application/javascript application/json;
    
    # Logging settings
    access_log logs/access.log;
    error_log logs/error.log;
    
    # Virtual Host Configs
    include conf.d/*.conf;
    include servers/*.conf;
}
EOF
    
    # Copy configuration file to Nginx directory
    if command_exists sudo; then
        sudo cp /tmp/nginx.conf "$NGINX_MAIN_CONF"
    else
        cp /tmp/nginx.conf "$NGINX_MAIN_CONF"
    fi
    
    return $?
}

# Function to test and reload Nginx
test_and_reload_nginx() {
    echo "Testing Nginx configuration..."
    
    if command_exists sudo; then
        sudo nginx -t
        if [ $? -eq 0 ]; then
            echo "Nginx configuration is valid. Reloading Nginx..."
            if command_exists systemctl; then
                sudo systemctl reload nginx
            else
                sudo nginx -s reload
            fi
            return $?
        else
            echo "Nginx configuration is invalid."
            return 1
        fi
    else
        nginx -t
        if [ $? -eq 0 ]; then
            echo "Nginx configuration is valid. Reloading Nginx..."
            nginx -s reload
            return $?
        else
            echo "Nginx configuration is invalid."
            return 1
        fi
    fi
}

# Main function
main() {
    echo "Setting up Nginx for Steel Unit Converter (Low Memory)..."
    
    # Check if Nginx is installed
    if ! command_exists nginx; then
        echo "Nginx is not installed."
        read -p "Would you like to install Nginx? (y/n) " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            install_nginx
            if [ $? -ne 0 ]; then
                echo "Failed to install Nginx."
                exit 1
            fi
        else
            echo "Nginx installation skipped."
            exit 0
        fi
    else
        echo "Nginx is already installed."
    fi
    
    # Generate SSL certificates
    generate_ssl_certs
    
    # Create Nginx configuration
    create_nginx_config
    if [ $? -ne 0 ]; then
        echo "Failed to create Nginx configuration."
        exit 1
    fi
    
    # Create Nginx main configuration
    create_nginx_main_config
    if [ $? -ne 0 ]; then
        echo "Failed to create Nginx main configuration."
        exit 1
    fi
    
    # Test and reload Nginx
    test_and_reload_nginx
    if [ $? -ne 0 ]; then
        echo "Failed to reload Nginx."
        exit 1
    fi
    
    echo "Nginx setup completed successfully."
    echo "You can now access the application at:"
    echo "  https://localhost"
    
    return 0
}

# Run the main function
main
