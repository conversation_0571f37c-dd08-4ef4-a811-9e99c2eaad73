#!/usr/bin/env python3
"""
Comprehensive debug script to identify table corruption source
Tests the complete pipeline from LLM to parsing
"""
import requests
import json
import time
import re

def debug_table_corruption():
    """Debug the complete table pipeline to find corruption source"""
    
    # Use the exact same data from the user's report
    test_data = """S/S 430  BA  NO PI
 
.015(+/-.0015) X 2.343"(+/-.005) X COIL                                              7190#
.015(+/-.0015) X 2.406"(+/-.005) X COIL                                              8,061#
.015(+/-.0015) X 16.50"(+/-.005) X COIL                                              12,550#
.015(+/-.0015) X 19.68"(+/-.03125) X COIL                                         8,835#
.015(+/-.0015) X 47.438"(+/-.03125) X COIL                                      57,655#
.015(+/-.0015) X 47.000"(+/-.03125) X COIL                                      118,001#
.015(+/-.0015) X 35.438"(+/-.03125) X COIL                                      62,515#
 
S/S 430 #2B NO PI
 
.015(+/-.0015) X 16.938"(+/-.005) X COIL                                           5,000#
.016(+/-.0015) X 19.6875"(+/-.005) X COIL                                         725,321#
.016(+/-.0015) X 35.500"(+/-.03125) X COIL                                      122,083#
.016(+/-.0015) X 36.000"(+/-.03125) X COIL                                      234,265#
.016(+/-.0015) X 48.000"(+/-.03125) X COIL                                      201,347#
.018(+/-.0015) X 36.000"(+/-.03125) X COIL                                      33,841#"""

    url = "http://localhost:8000/api/llm/stream"
    payload = {
        "text": test_data,
        "function": "conversion,table",
        "unit_system": "metric"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    print("🔍 DEBUGGING TABLE CORRUPTION - FULL PIPELINE TEST")
    print("=" * 80)
    print(f"📝 Input data preview:")
    print(test_data[:200] + "..." if len(test_data) > 200 else test_data)
    print("=" * 80)
    
    try:
        response = requests.post(url, json=payload, headers=headers, stream=True)
        
        if response.status_code == 200:
            print("✅ Stream started, analyzing corruption...")
            
            accumulated_data = ""
            chunk_count = 0
            corrupted_chunks = []
            
            # Track each chunk to identify corruption point
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    
                    if decoded_line.startswith('data: '):
                        try:
                            data_str = decoded_line[6:]
                            if data_str == '[DONE]':
                                print("\n🏁 Stream completed")
                                break
                            
                            data = json.loads(data_str)
                            if 'content' in data:
                                chunk_content = data['content']
                                accumulated_data += chunk_content
                                chunk_count += 1
                                
                                # Analyze each chunk for corruption signs
                                corruption_indicators = []
                                if '|' in chunk_content and len(chunk_content) > 10:
                                    # Check for pipe delimiter issues
                                    pipe_count = chunk_content.count('|')
                                    if pipe_count > 0:
                                        corruption_indicators.append(f"Has {pipe_count} pipes")
                                
                                # Check for mixed content patterns
                                if any(bad_pattern in chunk_content for bad_pattern in [
                                    'S/S 430', 'NOin', '.000in25in', 'xmm', 'kg    BA', 'mm(+/-0.79mm53524'
                                ]):
                                    corruption_indicators.append("CORRUPTION DETECTED")
                                    corrupted_chunks.append((chunk_count, chunk_content))
                                
                                if corruption_indicators or '|' in chunk_content:
                                    print(f"CHUNK {chunk_count:3d}: {chunk_content!r} [{', '.join(corruption_indicators)}]")
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ JSON decode error: {e}")
                            print(f"   Raw data: {data_str!r}")
            
            print("\n" + "=" * 80)
            print("📄 COMPLETE RAW RESPONSE ANALYSIS:")
            print("=" * 80)
            
            # Extract and analyze table content
            table_match = re.search(r'<table_stream>\s*([\s\S]*?)\s*</table_stream>', accumulated_data)
            if table_match:
                table_content = table_match.group(1).strip()
                print("📊 EXTRACTED TABLE CONTENT:")
                print("=" * 50)
                print(repr(table_content))
                print("=" * 50)
                
                # Analyze line by line for corruption
                lines = table_content.split('\n')
                print(f"\n🔍 LINE-BY-LINE CORRUPTION ANALYSIS ({len(lines)} lines):")
                print("=" * 80)
                
                for i, line in enumerate(lines):
                    if line.strip():
                        pipe_count = line.count('|')
                        is_corrupted = any(pattern in line for pattern in [
                            'NOin', '.000in25in', 'xmm(+/-0.04mm) xmm', 'kg    BA'
                        ])
                        
                        # Detailed character analysis for corrupted lines
                        if is_corrupted or pipe_count != 6:
                            print(f"🚨 LINE {i:2d} [CORRUPTED]: {pipe_count} pipes")
                            print(f"    Content: {repr(line)}")
                            
                            # Show character-by-character analysis
                            if '|' in line:
                                segments = line.split('|')
                                print(f"    Segments ({len(segments)}):")
                                for j, segment in enumerate(segments):
                                    print(f"      [{j}]: {repr(segment.strip())}")
                                print()
                        else:
                            print(f"✅ LINE {i:2d} [OK]: {pipe_count} pipes | {repr(line[:50])}...")
                
                # Test alternative delimiter
                print("\n🔧 TESTING ALTERNATIVE DELIMITERS:")
                print("=" * 50)
                
                for line in lines[1:4]:  # Test first few data lines
                    if line.strip() and not line.startswith('Item Code'):
                        print(f"Original: {repr(line)}")
                        
                        # Test tab delimiter
                        if '\t' in line:
                            tab_segments = line.split('\t')
                            print(f"Tab split ({len(tab_segments)}): {[s.strip() for s in tab_segments]}")
                        
                        # Test multiple spaces
                        space_segments = re.split(r'\s{2,}', line.strip())
                        if len(space_segments) > 1:
                            print(f"Space split ({len(space_segments)}): {space_segments}")
                        
                        print()
                
            else:
                print("❌ No table_stream content found")
            
            # Report corruption summary
            if corrupted_chunks:
                print(f"\n💥 CORRUPTION SUMMARY:")
                print(f"   Total corrupted chunks: {len(corrupted_chunks)}")
                print(f"   First corruption at chunk: {corrupted_chunks[0][0]}")
                print(f"   Sample corrupted content: {corrupted_chunks[0][1]!r}")
            else:
                print(f"\n✅ NO CORRUPTION DETECTED in {chunk_count} chunks")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"💥 Request failed: {e}")

if __name__ == "__main__":
    debug_table_corruption()